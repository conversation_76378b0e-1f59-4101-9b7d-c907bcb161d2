{"compileOnSave": false, "compilerOptions": {"rootDir": ".", "sourceMap": true, "declaration": false, "moduleResolution": "node", "emitDecoratorMetadata": true, "allowSyntheticDefaultImports": true, "experimentalDecorators": true, "resolveJsonModule": true, "importHelpers": true, "jsx": "react", "target": "esnext", "module": "esnext", "lib": ["es2020", "dom", "dom.iterable", "esnext.array"], "skipLibCheck": true, "skipDefaultLibCheck": true, "baseUrl": ".", "paths": {"@humansignal/app-common": ["libs/app-common/src/index.ts"], "@humansignal/app-common/*": ["libs/app-common/src/*"], "@humansignal/core": ["libs/core/src/index.ts"], "@humansignal/core/*": ["libs/core/src/*"], "@humansignal/datamanager": ["libs/datamanager/src/index.js"], "@humansignal/editor": ["libs/editor/src/index.js"], "@humansignal/frontend-test/*": ["libs/frontend-test/src/*"], "@humansignal/icons": ["libs/ui/src/assets/icons"], "@humansignal/shad/*": ["./libs/ui/src/shad/*"], "@humansignal/typography": ["libs/ui/src/typography"], "@humansignal/ui": ["libs/ui/src/index.ts"], "@humansignal/ui/*": ["libs/ui/src/*"]}, "types": ["vite-plugin-svgr/client"]}, "exclude": ["node_modules", "tmp"]}
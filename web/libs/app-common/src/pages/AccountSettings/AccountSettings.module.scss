.accountSettings {
  flex: 1;
  display: flex;
  flex-direction: column;

  &__content {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-wide);
    max-width: 660px;

    h1 {
      font-size: var(--font-size-header, 28px);
      margin: 0;
    }
  }
}

.accountSettingsPadding {
  padding: var(--spacing-wide);
}

.sectionContent {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  gap: var(--spacing-wide);
}

.flexRow {
  display: flex;
  align-items: center;
  gap: var(--spacing-wide);

  &.flexEnd {
    justify-content: flex-end;
  }
}

.flex1 {
  flex: 1;
}

.userPic {
  flex: none;
}

.saveButton {
  width: 125px;
}

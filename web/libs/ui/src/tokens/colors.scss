// Legacy colors
// TODO: Remove these once we've converted all the components to use the new colors
//       which are defined in the `tokens.scss` file
:root {
  // Deprecated: [DO NOT USE]
  --black-raw: 0 0 0;
  --white-raw: 255 255 255;
  --accent_color-raw: 86 111 207;
  --danger_color-raw: 221 0 0;
  --danger_color_dimmed: rgb(var(--danger_color-raw) / 50%);
  --black: rgb(var(--black-raw));
  --white: rgb(var(--white-raw));
  --black_2: rgb(var(--black-raw) / 2%);
  --black_4: rgb(var(--black-raw) / 4%);
  --black_5: rgb(var(--black-raw) / 5%);
  --black_10: rgb(var(--black-raw) / 10%);
  --black_15: rgb(var(--black-raw) / 15%);
  --black_20: rgb(var(--black-raw) / 20%);
  --black_40: rgb(var(--black-raw) / 40%);
  --sand_0: var(--color-sand-000);
  --sand_100: var(--color-sand-100);
  --sand_200: var(--color-sand-200);
  --sand_300: var(--color-sand-300);
  --sand_400: var(--color-sand-400);
  --sand_500: var(--color-sand-500);
  --sand_600: var(--color-sand-600);
  --sand_700: var(--color-sand-700);
  --sand_800: var(--color-sand-800);
  --sand_900: var(--color-sand-900);
  --plum_500: var(--color-plum-500);
  --plum_400: var(--color-plum-400);
  --plum_200: var(--color-plum-200);
  --plum_100: var(--color-plum-100);
  --plum_0: var(--color-plum-000);
  --persimmon_0: var(--color-persimmon-000);
  --persimmon_300: var(--color-persimmon-300);
  --persimmon_400: var(--color-persimmon-400);
  --canteloupe_0: var(--color-canteloupe-000);
  --canteloupe_100: var(--color-canteloupe-100);
  --canteloupe_400: var(--color-canteloupe-400);
  --canteloupe_500: var(--color-canteloupe-500);
  --canteloupe_600: var(--color-canteloupe-600);
  --canteloupe_700: var(--color-canteloupe-700);
  --kale_0: var(--color-kale-000);
  --kale_300: var(--color-kale-300);
  --kale_400: var(--color-kale-400);
  --red_1: #FFF1F0;
  --red_3: #FFCCC7;
  --red_7: #CF1322;
  --red_10: #5C0011;
  --grape_0: var(--color-grape-000);
  --grape_100: var(--color-grape-100);
  --grape_500-raw: 86 111 207;
  --grape_500: var(--color-grape-500);
  --grape_600: var(--color-grape-600);
  --grape_700: var(--color-grape-700);
  --grape_800: var(--color-grape-800);
  --primary_link: var(--grape_500);
  --danger_color: var(--red_7);
  --danger_color--faded: var(--red_3);
  --success_color: var(--kale_400);
  --surface-background: var(--color-neutral-background);
  --surface-outline-outline: var(--sand_300);
  --surface-border: var(--sand_300);
  --surface-surface: var(--sand_100);
  --surface-base: var(--sand_100);
  --surface-on: var(--sand_900);
  --surface-on-dim: var(--color-neutral-content-subtler);
  --surface-bright: var(--color-primary-surface-content);
  --surface-border-light: var(--sand_200);
  --surface-on-surface: var(--sand_700);
  --surface-icon-on-base: var(--sand_700);
  --surface-text-dimmer: var(--color-neutral-content-subtler);
  --surface-text-muted: var(--color-neutral-content-subtler);
  --surface-surface-text-base: var(--sand_900);
  --surface-outline-outline-light: var(--sand_200);
  --surface-outline-outline-dark: var(--color-neutral-content-subtler);
  --inverted-surface: var(--sand_900);
  --neutral-palette-light-theme-surface-on-surface: var(--sand_900);
  --neutral-variant-palette-light-theme-outline-opacity-outline-opacity16: var(--sand_200);
  --neutral-palette-light-theme-surface-surface: var(--sand_100);
  --neutral-palette-light-theme-surface-text: var(--sand_900);
  --volcano-palette-tonal-palette-volcano70: var(--persimmon_400);
  --primary-primary: var(--grape_500);
  --primary-background: var(--color-primary-surface-content);
  --primary-text-on-background: var(--color-neutral-background);
  --primary-bright: var(--grape_500);
  --primary-bright-secondary: var(--color-primary-surface-content);
  --primary-bright-tertiary: var(--color-primary-surface-content-subtle);
  --primary-text-on-background-dark: var(--grape_800);
  --color-surface-on-base-subtle: var(--color-neutral-content-subtler);
  --color-negative-bold: #CC5E46;
  --color-negative-accent-light: #FFBAAA;
  --accent_color: var(--grape_500);
  --colors-surface-border: var(--sand_300);
  --colors-surface-icon-on-base: var(--sand_700);
  --colors-surface-text: var(--sand_700);
  --primary-button-waiting-animation-bg: repeating-linear-gradient(-63.43deg, rgba(var(--color-primary-surface-active-raw) / 50%) 1px, rgba(var(--color-primary-surface-raw) / 50%) 2px, rgba(var(--color-primary-surface-raw) / 50%) 6px, rgba(var(--color-primary-surface-active-raw) / 50%) 7px, rgba(var(--color-primary-surface-active-raw) / 50%) 12px);
  --negative-button-waiting-animation-bg: repeating-linear-gradient(-63.43deg, rgba(var(--color-negative-surface-active-raw) / 50%) 1px, rgba(var(--color-negative-surface-raw) / 50%) 2px, rgba(var(--color-negative-surface-raw) / 50%) 6px, rgba(var(--color-negative-surface-active-raw) / 50%) 7px, rgba(var(--color-negative-surface-active-raw) / 50%) 12px);
  --button-waiting-animation-bg: repeating-linear-gradient(-63.43deg, rgba(var(--color-neutral-surface-active-raw) / 50%) 1px, rgba(var(--color-neutral-surface-raw) / 50%) 2px, rgba(var(--color-neutral-surface-raw) / 50%) 6px, rgba(var(--color-neutral-surface-active-raw) / 50%) 7px, rgba(var(--color-neutral-surface-active-raw) / 50%) 12px);
  --project-title-icon-primary-color: rgba(var(--black-raw), 0.35);
  --project-title-icon-alternate-color: rgba(var(--white-raw), 0.35);
  --shadow-intensity: 1;
  --color-accent-gradient-subtle: rgba(var(--color-accent-canteloupe-base-raw) / 10%) 0%, rgba(var(--color-accent-persimmon-base-raw) / 10%) 50%, rgba(var(--color-accent-plum-base-raw) / 10%) 100%;
  --color-accent-gradient-base: rgba(var(--color-accent-canteloupe-base-raw) / 90%) 0%, rgba(var(--color-accent-persimmon-base-raw) / 90%) 50%, rgba(var(--color-accent-plum-base-raw) / 90%) 100%;
  --color-accent-gradient-bold: rgba(var(--color-accent-canteloupe-base-raw) / 100%) 0%, rgba(var(--color-accent-persimmon-base-raw) / 100%) 50%, rgba(var(--color-accent-plum-base-raw) / 100%) 100%;
  --color-accent-gradient-dark: rgba(var(--color-accent-canteloupe-bold-raw) / 100%) 0%, rgba(var(--color-accent-persimmon-bold-raw) / 100%) 50%, rgba(var(--color-accent-plum-bold-raw) / 100%) 100%;
}

[data-color-scheme="dark"] {
  --shadow-intensity: 2;

  input[type="date"] {
    color-scheme: dark;
  }
}

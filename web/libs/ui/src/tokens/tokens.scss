// Generated from design-tokens.json - DO NOT EDIT DIRECTLY

:root {
  --color-neutral-surface: var(--color-sand-100);
  --color-neutral-surface-raw: 249 248 246;
  --color-neutral-on-dark-surface: var(--color-sand-900);
  --color-neutral-on-dark-surface-raw: 18 17 13;
  --color-neutral-on-dark-surface-hover: var(--color-sand-800);
  --color-neutral-on-dark-surface-hover-raw: 38 37 34;
  --color-neutral-on-dark-surface-active: var(--color-sand-900);
  --color-neutral-on-dark-surface-active-raw: 18 17 13;
  --color-neutral-on-dark-content: var(--color-sand-000);
  --color-neutral-on-dark-icon: var(--color-sand-000);
  --color-neutral-on-dark-content-subtle: var(--color-sand-300);
  --color-neutral-on-dark-background: var(--color-sand-800);
  --color-neutral-on-dark-background-raw: 38 37 34;
  --color-neutral-on-dark-background-bold: var(--color-sand-900);
  --color-neutral-on-dark-background-bold-raw: 18 17 13;
  --color-neutral-on-dark-border: var(--color-sand-700);
  --color-neutral-surface-hover: var(--color-sand-000);
  --color-neutral-surface-hover-raw: 253 253 252;
  --color-neutral-surface-active: var(--color-sand-200);
  --color-neutral-surface-active-raw: 240 239 235;
  --color-neutral-surface-inset: var(--color-sand-200);
  --color-neutral-surface-inset-raw: 240 239 235;
  --color-neutral-content: var(--color-sand-800);
  --color-neutral-content-subtle: var(--color-sand-700);
  --color-neutral-content-subtler: var(--color-sand-600);
  --color-neutral-content-subtlest: var(--color-sand-500);
  --color-neutral-icon: var(--color-sand-700);
  --color-neutral-background: var(--color-sand-000);
  --color-neutral-background-raw: 253 253 252;
  --color-neutral-background-bold: var(--color-sand-000);
  --color-neutral-background-bold-raw: 253 253 252;
  --color-neutral-emphasis: var(--color-sand-200);
  --color-neutral-emphasis-subtle: var(--color-sand-100);
  --color-neutral-border: var(--color-sand-300);
  --color-neutral-border-subtle: var(--color-sand-300);
  --color-neutral-border-subtler: var(--color-sand-200);
  --color-neutral-border-bold: var(--color-sand-400);
  --color-neutral-border-bolder: var(--color-sand-500);
  --color-neutral-border-boldest: var(--color-sand-600);
  --color-neutral-shadow: var(--color-sand-900);
  --color-neutral-shadow-raw: 18 17 13;
  --color-neutral-inverted-surface: var(--color-sand-900);
  --color-neutral-inverted-surface-raw: 18 17 13;
  --color-neutral-inverted-surface-hover: var(--color-sand-800);
  --color-neutral-inverted-surface-hover-raw: 38 37 34;
  --color-neutral-inverted-surface-active: var(--color-sand-700);
  --color-neutral-inverted-surface-active-raw: 69 67 62;
  --color-neutral-inverted-content: var(--color-sand-100);
  --color-neutral-inverted-icon: var(--color-sand-100);
  --color-neutral-inverted-content-subtle: var(--color-sand-400);
  --color-neutral-inverted-content-subtler: var(--color-sand-300);
  --color-neutral-inverted-content-subtlest: var(--color-sand-500);
  --color-neutral-inverted-background: var(--color-sand-900);
  --color-neutral-inverted-background-raw: 18 17 13;
  --color-neutral-inverted-border: var(--color-sand-800);
  --color-primary-surface: var(--color-grape-700);
  --color-primary-surface-raw: 76 95 169;
  --color-primary-surface-hover: var(--color-grape-600);
  --color-primary-surface-hover-raw: 87 108 193;
  --color-primary-surface-active: var(--color-grape-800);
  --color-primary-surface-active-raw: 55 68 122;
  --color-primary-surface-content: var(--color-grape-000);
  --color-primary-surface-content-raw: 240 243 254;
  --color-primary-surface-content-subtle: var(--color-grape-100);
  --color-primary-surface-content-subtle-raw: 212 219 251;
  --color-primary-surface-icon: var(--color-grape-100);
  --color-primary-surface-icon-raw: 212 219 251;
  --color-primary-content: var(--color-grape-700);
  --color-primary-content-raw: 76 95 169;
  --color-primary-content-hover: var(--color-grape-400);
  --color-primary-content-hover-raw: 109 135 241;
  --color-primary-content-subtle: var(--color-grape-500);
  --color-primary-content-subtle-raw: 97 122 218;
  --color-primary-icon: var(--color-grape-500);
  --color-primary-icon-raw: 97 122 218;
  --color-primary-background: var(--color-grape-000);
  --color-primary-background-raw: 240 243 254;
  --color-primary-emphasis: var(--color-grape-100);
  --color-primary-emphasis-raw: 212 219 251;
  --color-primary-emphasis-subtle: var(--color-grape-000);
  --color-primary-emphasis-subtle-raw: 240 243 254;
  --color-primary-border: var(--color-grape-700);
  --color-primary-border-raw: 76 95 169;
  --color-primary-border-subtle: var(--color-grape-500);
  --color-primary-border-subtle-raw: 97 122 218;
  --color-primary-border-subtler: var(--color-grape-300);
  --color-primary-border-subtler-raw: 153 171 245;
  --color-primary-border-subtlest: var(--color-grape-200);
  --color-primary-border-subtlest-raw: 182 195 248;
  --color-primary-border-bold: var(--color-grape-800);
  --color-primary-border-bold-raw: 55 68 122;
  --color-primary-focus-outline: var(--color-grape-100);
  --color-primary-focus-outline-raw: 212 219 251;
  --color-primary-shadow: var(--color-grape-900);
  --color-primary-shadow-raw: 43 54 96;
  --color-negative-surface: var(--color-persimmon-600);
  --color-negative-surface-raw: 204 94 70;
  --color-negative-surface-hover: var(--color-persimmon-500);
  --color-negative-surface-hover-raw: 230 105 78;
  --color-negative-surface-active: var(--color-persimmon-800);
  --color-negative-surface-active-raw: 153 70 52;
  --color-negative-surface-content: var(--color-persimmon-000);
  --color-negative-surface-content-raw: 255 241 238;
  --color-negative-surface-content-subtle: var(--color-persimmon-100);
  --color-negative-surface-content-subtle-raw: 255 214 205;
  --color-negative-surface-icon: var(--color-persimmon-100);
  --color-negative-surface-icon-raw: 255 214 205;
  --color-negative-content: var(--color-persimmon-700);
  --color-negative-content-hover: var(--color-persimmon-500);
  --color-negative-content-subtle: var(--color-persimmon-500);
  --color-negative-icon: var(--color-persimmon-500);
  --color-negative-background: var(--color-persimmon-000);
  --color-negative-background-raw: 255 241 238;
  --color-negative-emphasis: var(--color-persimmon-100);
  --color-negative-emphasis-subtle: var(--color-persimmon-000);
  --color-negative-border: var(--color-persimmon-700);
  --color-negative-border-subtle: var(--color-persimmon-500);
  --color-negative-border-subtler: var(--color-persimmon-300);
  --color-negative-border-subtlest: var(--color-persimmon-200);
  --color-negative-border-bold: var(--color-persimmon-800);
  --color-negative-focus-outline: var(--color-persimmon-100);
  --color-negative-focus-outline-raw: 255 214 205;
  --color-positive-surface: var(--color-kale-600);
  --color-positive-surface-raw: 40 122 114;
  --color-positive-surface-hover: var(--color-kale-500);
  --color-positive-surface-hover-raw: 52 152 141;
  --color-positive-surface-active: var(--color-kale-800);
  --color-positive-surface-active-raw: 32 79 77;
  --color-positive-surface-content: var(--color-kale-000);
  --color-positive-surface-content-raw: 244 249 249;
  --color-positive-surface-content-subtle: var(--color-kale-100);
  --color-positive-surface-content-subtle-raw: 212 241 235;
  --color-positive-surface-icon: var(--color-kale-100);
  --color-positive-surface-icon-raw: 212 241 235;
  --color-positive-content: var(--color-kale-700);
  --color-positive-content-hover: var(--color-kale-500);
  --color-positive-content-subtle: var(--color-kale-500);
  --color-positive-icon: var(--color-kale-500);
  --color-positive-background: var(--color-kale-000);
  --color-positive-background-raw: 244 249 249;
  --color-positive-emphasis: var(--color-kale-100);
  --color-positive-emphasis-subtle: var(--color-kale-000);
  --color-positive-border: var(--color-kale-700);
  --color-positive-border-subtle: var(--color-kale-500);
  --color-positive-border-subtler: var(--color-kale-300);
  --color-positive-border-subtlest: var(--color-kale-200);
  --color-positive-border-bold: var(--color-kale-800);
  --color-positive-focus-outline: var(--color-kale-100);
  --color-positive-focus-outline-raw: 212 241 235;
  --color-warning-surface: var(--color-canteloupe-600);
  --color-warning-surface-raw: 204 133 79;
  --color-warning-surface-hover: var(--color-canteloupe-500);
  --color-warning-surface-hover-raw: 230 149 89;
  --color-warning-surface-active: var(--color-canteloupe-800);
  --color-warning-surface-active-raw: 153 100 58;
  --color-warning-surface-content: var(--color-canteloupe-000);
  --color-warning-surface-content-raw: 255 246 239;
  --color-warning-surface-content-subtle: var(--color-canteloupe-100);
  --color-warning-surface-content-subtle-raw: 255 228 208;
  --color-warning-surface-icon: var(--color-canteloupe-100);
  --color-warning-surface-icon-raw: 255 228 208;
  --color-warning-content: var(--color-canteloupe-700);
  --color-warning-content-hover: var(--color-canteloupe-500);
  --color-warning-content-subtle: var(--color-canteloupe-500);
  --color-warning-icon: var(--color-canteloupe-500);
  --color-warning-background: var(--color-canteloupe-000);
  --color-warning-background-raw: 255 246 239;
  --color-warning-emphasis: var(--color-canteloupe-100);
  --color-warning-emphasis-subtle: var(--color-canteloupe-000);
  --color-warning-border: var(--color-canteloupe-700);
  --color-warning-border-subtle: var(--color-canteloupe-500);
  --color-warning-border-subtler: var(--color-canteloupe-300);
  --color-warning-border-subtlest: var(--color-canteloupe-200);
  --color-warning-border-bold: var(--color-canteloupe-800);
  --color-warning-focus-outline: var(--color-canteloupe-100);
  --color-warning-focus-outline-raw: 255 228 208;
  --color-accent-grape-dark: var(--color-grape-900);
  --color-accent-grape-dark-raw: 43 54 96;
  --color-accent-grape-bold: var(--color-grape-600);
  --color-accent-grape-bold-raw: 87 108 193;
  --color-accent-grape-base: var(--color-grape-400);
  --color-accent-grape-base-raw: 109 135 241;
  --color-accent-grape-subtle: var(--color-grape-100);
  --color-accent-grape-subtle-raw: 212 219 251;
  --color-accent-grape-subtlest: var(--color-grape-000);
  --color-accent-grape-subtlest-raw: 240 243 254;
  --color-accent-blueberry-dark: var(--color-blueberry-900);
  --color-accent-blueberry-dark-raw: 28 60 95;
  --color-accent-blueberry-bold: var(--color-blueberry-600);
  --color-accent-blueberry-bold-raw: 43 120 202;
  --color-accent-blueberry-base: var(--color-blueberry-400);
  --color-accent-blueberry-base-raw: 83 158 238;
  --color-accent-blueberry-subtle: var(--color-blueberry-100);
  --color-accent-blueberry-subtle-raw: 212 231 251;
  --color-accent-blueberry-subtlest: var(--color-blueberry-000);
  --color-accent-blueberry-subtlest-raw: 240 247 254;
  --color-accent-kale-dark: var(--color-kale-900);
  --color-accent-kale-dark-raw: 31 66 64;
  --color-accent-kale-bold: var(--color-kale-600);
  --color-accent-kale-bold-raw: 40 122 114;
  --color-accent-kale-base: var(--color-kale-400);
  --color-accent-kale-base-raw: 87 183 171;
  --color-accent-kale-subtle: var(--color-kale-100);
  --color-accent-kale-subtle-raw: 212 241 235;
  --color-accent-kale-subtlest: var(--color-kale-000);
  --color-accent-kale-subtlest-raw: 244 249 249;
  --color-accent-kiwi-dark: var(--color-kiwi-900);
  --color-accent-kiwi-dark-raw: 43 66 31;
  --color-accent-kiwi-bold: var(--color-kiwi-600);
  --color-accent-kiwi-bold-raw: 69 122 40;
  --color-accent-kiwi-base: var(--color-kiwi-400);
  --color-accent-kiwi-base-raw: 120 183 87;
  --color-accent-kiwi-subtle: var(--color-kiwi-100);
  --color-accent-kiwi-subtle-raw: 222 241 212;
  --color-accent-kiwi-subtlest: var(--color-kiwi-000);
  --color-accent-kiwi-subtlest-raw: 246 249 244;
  --color-accent-mango-dark: var(--color-mango-900);
  --color-accent-mango-dark-raw: 98 71 24;
  --color-accent-mango-bold: var(--color-mango-600);
  --color-accent-mango-bold-raw: 235 156 20;
  --color-accent-mango-base: var(--color-mango-400);
  --color-accent-mango-base-raw: 250 186 76;
  --color-accent-mango-subtle: var(--color-mango-100);
  --color-accent-mango-subtle-raw: 255 238 208;
  --color-accent-mango-subtlest: var(--color-mango-000);
  --color-accent-mango-subtlest-raw: 255 249 239;
  --color-accent-canteloupe-dark: var(--color-canteloupe-900);
  --color-accent-canteloupe-dark-raw: 102 66 40;
  --color-accent-canteloupe-bold: var(--color-canteloupe-600);
  --color-accent-canteloupe-bold-raw: 204 133 79;
  --color-accent-canteloupe-base: var(--color-canteloupe-400);
  --color-accent-canteloupe-base-raw: 255 166 99;
  --color-accent-canteloupe-subtle: var(--color-canteloupe-100);
  --color-accent-canteloupe-subtle-raw: 255 228 208;
  --color-accent-canteloupe-subtlest: var(--color-canteloupe-000);
  --color-accent-canteloupe-subtlest-raw: 255 246 239;
  --color-accent-persimmon-dark: var(--color-persimmon-900);
  --color-accent-persimmon-dark-raw: 128 59 44;
  --color-accent-persimmon-bold: var(--color-persimmon-600);
  --color-accent-persimmon-bold-raw: 204 94 70;
  --color-accent-persimmon-base: var(--color-persimmon-400);
  --color-accent-persimmon-base-raw: 255 117 87;
  --color-accent-persimmon-subtle: var(--color-persimmon-100);
  --color-accent-persimmon-subtle-raw: 255 214 205;
  --color-accent-persimmon-subtlest: var(--color-persimmon-000);
  --color-accent-persimmon-subtlest-raw: 255 241 238;
  --color-accent-plum-dark: var(--color-plum-900);
  --color-accent-plum-dark-raw: 114 62 106;
  --color-accent-plum-bold: var(--color-plum-600);
  --color-accent-plum-bold-raw: 182 98 169;
  --color-accent-plum-base: var(--color-plum-400);
  --color-accent-plum-base-raw: 227 123 211;
  --color-accent-plum-subtle: var(--color-plum-100);
  --color-accent-plum-subtle-raw: 247 214 242;
  --color-accent-plum-subtlest: var(--color-plum-000);
  --color-accent-plum-subtlest-raw: 251 242 252;
  --color-accent-fig-dark: var(--color-fig-900);
  --color-accent-fig-dark-raw: 91 62 114;
  --color-accent-fig-bold: var(--color-fig-600);
  --color-accent-fig-bold-raw: 146 98 182;
  --color-accent-fig-base: var(--color-fig-400);
  --color-accent-fig-base-raw: 172 121 210;
  --color-accent-fig-subtle: var(--color-fig-100);
  --color-accent-fig-subtle-raw: 233 214 247;
  --color-accent-fig-subtlest: var(--color-fig-000);
  --color-accent-fig-subtlest-raw: 248 242 252;
  --color-accent-sand-dark: var(--color-sand-900);
  --color-accent-sand-dark-raw: 18 17 13;
  --color-accent-sand-bold: var(--color-sand-600);
  --color-accent-sand-bold-raw: 107 104 96;
  --color-accent-sand-base: var(--color-sand-400);
  --color-accent-sand-base-raw: 202 197 184;
  --color-accent-sand-subtle: var(--color-sand-100);
  --color-accent-sand-subtle-raw: 249 248 246;
  --color-accent-sand-subtlest: var(--color-sand-100);
  --color-accent-sand-subtlest-raw: 249 248 246;
  --color-sand-100: rgb(249 248 246);
  --color-sand-200: rgb(240 239 235);
  --color-sand-300: rgb(225 222 213);
  --color-sand-400: rgb(202 197 184);
  --color-sand-500: rgb(164 159 149);
  --color-sand-600: rgb(107 104 96);
  --color-sand-700: rgb(69 67 62);
  --color-sand-800: rgb(38 37 34);
  --color-sand-850: rgb(30 29 26);
  --color-sand-900: rgb(18 17 13);
  --color-sand-950: rgb(13 12 9);
  --color-sand-000: rgb(253 253 252);
  --color-grape-100: rgb(212 219 251);
  --color-grape-200: rgb(182 195 248);
  --color-grape-300: rgb(153 171 245);
  --color-grape-400: rgb(109 135 241);
  --color-grape-500: rgb(97 122 218);
  --color-grape-600: rgb(87 108 193);
  --color-grape-700: rgb(76 95 169);
  --color-grape-800: rgb(55 68 122);
  --color-grape-900: rgb(43 54 96);
  --color-grape-950: rgb(17 22 38);
  --color-grape-000: rgb(240 243 254);
  --color-blueberry-100: rgb(212 231 251);
  --color-blueberry-200: rgb(182 214 248);
  --color-blueberry-300: rgb(153 197 245);
  --color-blueberry-400: rgb(83 158 238);
  --color-blueberry-500: rgb(50 135 226);
  --color-blueberry-600: rgb(43 120 202);
  --color-blueberry-700: rgb(43 105 171);
  --color-blueberry-800: rgb(37 80 126);
  --color-blueberry-900: rgb(28 60 95);
  --color-blueberry-950: rgb(11 24 38);
  --color-blueberry-000: rgb(240 247 254);
  --color-kale-100: rgb(212 241 235);
  --color-kale-200: rgb(171 228 218);
  --color-kale-300: rgb(122 206 193);
  --color-kale-400: rgb(87 183 171);
  --color-kale-500: rgb(52 152 141);
  --color-kale-600: rgb(40 122 114);
  --color-kale-700: rgb(34 98 93);
  --color-kale-800: rgb(32 79 77);
  --color-kale-900: rgb(31 66 64);
  --color-kale-950: rgb(18 38 37);
  --color-kale-000: rgb(244 249 249);
  --color-kiwi-100: rgb(222 241 212);
  --color-kiwi-200: rgb(191 228 171);
  --color-kiwi-300: rgb(151 206 122);
  --color-kiwi-400: rgb(120 183 87);
  --color-kiwi-500: rgb(87 152 52);
  --color-kiwi-600: rgb(69 122 40);
  --color-kiwi-700: rgb(56 98 34);
  --color-kiwi-800: rgb(48 79 32);
  --color-kiwi-900: rgb(43 66 31);
  --color-kiwi-950: rgb(25 38 18);
  --color-kiwi-000: rgb(246 249 244);
  --color-mango-100: rgb(255 238 208);
  --color-mango-200: rgb(255 226 177);
  --color-mango-300: rgb(255 209 130);
  --color-mango-400: rgb(250 186 76);
  --color-mango-500: rgb(244 170 42);
  --color-mango-600: rgb(235 156 20);
  --color-mango-700: rgb(204 142 36);
  --color-mango-800: rgb(160 114 34);
  --color-mango-900: rgb(98 71 24);
  --color-mango-950: rgb(38 28 10);
  --color-mango-000: rgb(255 249 239);
  --color-canteloupe-100: rgb(255 228 208);
  --color-canteloupe-200: rgb(255 211 177);
  --color-canteloupe-300: rgb(255 184 130);
  --color-canteloupe-400: rgb(255 166 99);
  --color-canteloupe-500: rgb(230 149 89);
  --color-canteloupe-600: rgb(204 133 79);
  --color-canteloupe-700: rgb(179 116 69);
  --color-canteloupe-800: rgb(153 100 58);
  --color-canteloupe-900: rgb(102 66 40);
  --color-canteloupe-950: rgb(51 28 20);
  --color-canteloupe-000: rgb(255 246 239);
  --color-persimmon-100: rgb(255 214 205);
  --color-persimmon-200: rgb(255 186 170);
  --color-persimmon-300: rgb(255 159 137);
  --color-persimmon-400: rgb(255 117 87);
  --color-persimmon-500: rgb(230 105 78);
  --color-persimmon-600: rgb(204 94 70);
  --color-persimmon-700: rgb(179 82 61);
  --color-persimmon-800: rgb(153 70 52);
  --color-persimmon-900: rgb(128 59 44);
  --color-persimmon-950: rgb(38 18 13);
  --color-persimmon-000: rgb(255 241 238);
  --color-plum-100: rgb(247 214 242);
  --color-plum-200: rgb(241 189 233);
  --color-plum-300: rgb(233 149 220);
  --color-plum-400: rgb(227 123 211);
  --color-plum-500: rgb(204 111 190);
  --color-plum-600: rgb(182 98 169);
  --color-plum-700: rgb(159 86 148);
  --color-plum-800: rgb(136 74 128);
  --color-plum-900: rgb(114 62 106);
  --color-plum-950: rgb(38 21 36);
  --color-plum-000: rgb(251 242 252);
  --color-fig-100: rgb(233 214 247);
  --color-fig-200: rgb(218 189 241);
  --color-fig-300: rgb(197 149 233);
  --color-fig-400: rgb(172 121 210);
  --color-fig-500: rgb(159 108 198);
  --color-fig-600: rgb(146 98 182);
  --color-fig-700: rgb(127 86 159);
  --color-fig-800: rgb(109 74 136);
  --color-fig-900: rgb(91 62 114);
  --color-fig-950: rgb(31 21 38);
  --color-fig-000: rgb(248 242 252);
  --spacing-0: 0;
  --spacing-50: 0.125rem;
  --spacing-100: 0.25rem;
  --spacing-200: 0.5rem;
  --spacing-300: 0.75rem;
  --spacing-400: 1rem;
  --spacing-500: 1.25rem;
  --spacing-600: 1.5rem;
  --spacing-700: 1.75rem;
  --spacing-800: 2rem;
  --spacing-900: 2.25rem;
  --spacing-1000: 2.5rem;
  --spacing-1100: 2.75rem;
  --spacing-1200: 3rem;
  --spacing-1300: 3.25rem;
  --spacing-1400: 3.5rem;
  --spacing-1500: 3.75rem;
  --spacing-1600: 4rem;
  --font-size-8: 0.5rem;
  --font-size-9: 0.5625rem;
  --font-size-10: 0.625rem;
  --font-size-11: 0.6875rem;
  --font-size-12: 0.75rem;
  --font-size-14: 0.875rem;
  --font-size-16: 1rem;
  --font-size-22: 1.375rem;
  --font-size-24: 1.5rem;
  --font-size-28: 1.75rem;
  --font-size-32: 2rem;
  --font-size-36: 2.25rem;
  --font-size-48: 3rem;
  --font-size-56: 3.5rem;
  --font-size-64: 4rem;
  --font-size-80: 5rem;
  --font-size-120: 7.5rem;
  --font-size-160: 10rem;
  --line-height-12: 0.75rem;
  --line-height-16: 1rem;
  --line-height-18: 1.125rem;
  --line-height-20: 1.25rem;
  --line-height-24: 1.5rem;
  --line-height-28: 1.75rem;
  --line-height-32: 2rem;
  --line-height-36: 2.25rem;
  --line-height-40: 2.5rem;
  --line-height-44: 2.75rem;
  --line-height-48: 3rem;
  --line-height-52: 3.25rem;
  --line-height-56: 3.5rem;
  --line-height-60: 3.75rem;
  --line-height-64: 4rem;
  --line-height-68: 4.25rem;
  --line-height-72: 4.5rem;
  --letter-spacing-0: 0;
  --letter-spacing-15: 0.0094rem;
  --letter-spacing-25: 0.0156rem;
  --letter-spacing-50: 0.0313rem;
  --letter-spacing--20: -0.0125rem;
  --letter-spacing--15: -0.0094rem;
  --letter-spacing--10: -0.0063rem;
  --font-family-base: "Figtree";
  --font-family-mono: "IBMPlexMono";
  --corner-radius-0: var(--spacing-0);
  --corner-radius-2: var(--spacing-50);
  --corner-radius-4: var(--spacing-100);
  --corner-radius-8: var(--spacing-200);
  --corner-radius-12: var(--spacing-300);
  --corner-radius-16: var(--spacing-400);
  --corner-radius-18: var(--spacing-500);
  --corner-radius-24: var(--spacing-600);
  --corner-radius-28: var(--spacing-700);
  --corner-radius-32: var(--spacing-800);
  --corner-radius-36: var(--spacing-900);
  --corner-radius-40: var(--spacing-1000);
  --corner-radius-44: var(--spacing-1100);
  --corner-radius-48: var(--spacing-1200);
  --corner-radius-52: var(--spacing-1300);
  --corner-radius-56: var(--spacing-1400);
  --corner-radius-60: var(--spacing-1500);
  --corner-radius-64: var(--spacing-1600);
  --spacing-none: var(--spacing-0);
  --spacing-tightest: var(--spacing-50);
  --spacing-tighter: var(--spacing-100);
  --spacing-tight: var(--spacing-200);
  --spacing-base: var(--spacing-400);
  --spacing-wide: var(--spacing-600);
  --spacing-wider: var(--spacing-800);
  --spacing-widest: var(--spacing-1000);
  --corner-radius-none: var(--corner-radius-0);
  --corner-radius-smallest: var(--corner-radius-2);
  --corner-radius-smaller: var(--corner-radius-4);
  --corner-radius-small: var(--corner-radius-8);
  --corner-radius-medium: var(--corner-radius-12);
  --corner-radius-large: var(--corner-radius-16);
  --corner-radius-larger: var(--corner-radius-18);
  --corner-radius-largest: var(--corner-radius-24);
  --font-family-body: var(--font-family-base);
  --font-family-headings: var(--font-family-base);
  --font-family-monospace: var(--font-family-mono);
  --font-size-body-tinier: var(--font-size-8);
  --font-size-body-tiny: var(--font-size-9);
  --font-size-body-smallest: var(--font-size-10);
  --font-size-body-smaller: var(--font-size-12);
  --font-size-body-small: var(--font-size-14);
  --font-size-body-medium: var(--font-size-16);
  --font-size-label-tinier: var(--font-size-8);
  --font-size-label-tiny: var(--font-size-9);
  --font-size-label-smallest: var(--font-size-11);
  --font-size-label-smaller: var(--font-size-12);
  --font-size-label-small: var(--font-size-14);
  --font-size-label-medium: var(--font-size-16);
  --font-size-title-small: var(--font-size-14);
  --font-size-title-medium: var(--font-size-16);
  --font-size-title-large: var(--font-size-22);
  --font-size-headline-small: var(--font-size-24);
  --font-size-headline-medium: var(--font-size-28);
  --font-size-headline-large: var(--font-size-32);
  --font-size-display-small: var(--font-size-36);
  --font-size-display-medium: var(--font-size-48);
  --font-size-display-large: var(--font-size-56);
  --font-weight-light: 300;
  --font-weight-light-italic: light italic;
  --font-weight-regular: 400;
  --font-weight-regular-italic: italic;
  --font-weight-medium: 500;
  --font-weight-medium-italic: medium italic;
  --font-weight-semibold: 600;
  --font-weight-semibold-italic: semibold italic;
  --font-weight-bold: 700;
  --font-weight-bold-italic: bold italic;
  --line-height-auto: auto;
  --line-height-body-smallest: var(--line-height-12);
  --line-height-body-smaller: var(--line-height-16);
  --line-height-body-small: var(--line-height-18);
  --line-height-body-medium: var(--line-height-24);
  --line-height-label-smallest: var(--line-height-16);
  --line-height-label-smaller: var(--line-height-16);
  --line-height-label-small: var(--line-height-18);
  --line-height-label-medium: var(--line-height-24);
  --line-height-title-small: var(--line-height-20);
  --line-height-title-medium: var(--line-height-24);
  --line-height-title-large: var(--line-height-28);
  --line-height-headline-small: var(--line-height-32);
  --line-height-headline-medium: var(--line-height-36);
  --line-height-headline-large: var(--line-height-40);
  --line-height-display-small: var(--line-height-48);
  --line-height-display-medium: var(--line-height-56);
  --line-height-display-large: var(--line-height-72);
  --letter-spacing-densest: var(--letter-spacing--20);
  --letter-spacing-denser: var(--letter-spacing--15);
  --letter-spacing-dense: var(--letter-spacing--10);
  --letter-spacing-base: var(--letter-spacing-0);
  --letter-spacing-wide: var(--letter-spacing-15);
  --letter-spacing-wider: var(--letter-spacing-25);
  --letter-spacing-widest: var(--letter-spacing-50);
}

[data-color-scheme="dark"] {
  --color-neutral-surface: var(--color-sand-850);
  --color-neutral-surface-raw: 30 29 26;
  --color-neutral-on-dark-surface: var(--color-sand-900);
  --color-neutral-on-dark-surface-raw: 18 17 13;
  --color-neutral-on-dark-surface-hover: var(--color-sand-800);
  --color-neutral-on-dark-surface-hover-raw: 38 37 34;
  --color-neutral-on-dark-surface-active: var(--color-sand-900);
  --color-neutral-on-dark-surface-active-raw: 18 17 13;
  --color-neutral-on-dark-content: var(--color-sand-100);
  --color-neutral-on-dark-icon: var(--color-sand-000);
  --color-neutral-on-dark-content-subtle: var(--color-sand-300);
  --color-neutral-on-dark-background: var(--color-sand-800);
  --color-neutral-on-dark-background-raw: 38 37 34;
  --color-neutral-on-dark-background-bold: var(--color-sand-900);
  --color-neutral-on-dark-background-bold-raw: 18 17 13;
  --color-neutral-on-dark-border: var(--color-sand-700);
  --color-neutral-surface-hover: var(--color-sand-800);
  --color-neutral-surface-hover-raw: 38 37 34;
  --color-neutral-surface-active: var(--color-sand-900);
  --color-neutral-surface-active-raw: 18 17 13;
  --color-neutral-surface-inset: var(--color-sand-950);
  --color-neutral-surface-inset-raw: 13 12 9;
  --color-neutral-content: var(--color-sand-100);
  --color-neutral-content-subtle: var(--color-sand-400);
  --color-neutral-content-subtler: var(--color-sand-500);
  --color-neutral-content-subtlest: var(--color-sand-600);
  --color-neutral-icon: var(--color-sand-100);
  --color-neutral-background: var(--color-sand-800);
  --color-neutral-background-raw: 38 37 34;
  --color-neutral-background-bold: var(--color-sand-900);
  --color-neutral-background-bold-raw: 18 17 13;
  --color-neutral-emphasis: var(--color-sand-900);
  --color-neutral-emphasis-subtle: var(--color-sand-850);
  --color-neutral-border: var(--color-sand-700);
  --color-neutral-border-subtle: var(--color-sand-800);
  --color-neutral-border-subtler: var(--color-sand-850);
  --color-neutral-border-bold: var(--color-sand-600);
  --color-neutral-border-bolder: var(--color-sand-500);
  --color-neutral-border-boldest: var(--color-sand-400);
  --color-neutral-shadow: var(--color-sand-950);
  --color-neutral-shadow-raw: 13 12 9;
  --color-neutral-inverted-surface: var(--color-sand-200);
  --color-neutral-inverted-surface-raw: 240 239 235;
  --color-neutral-inverted-surface-hover: var(--color-sand-100);
  --color-neutral-inverted-surface-hover-raw: 249 248 246;
  --color-neutral-inverted-surface-active: var(--color-sand-300);
  --color-neutral-inverted-surface-active-raw: 225 222 213;
  --color-neutral-inverted-content: var(--color-sand-800);
  --color-neutral-inverted-icon: var(--color-sand-800);
  --color-neutral-inverted-content-subtle: var(--color-sand-500);
  --color-neutral-inverted-content-subtler: var(--color-sand-600);
  --color-neutral-inverted-content-subtlest: var(--color-sand-700);
  --color-neutral-inverted-background: var(--color-sand-000);
  --color-neutral-inverted-background-raw: 253 253 252;
  --color-neutral-inverted-border: var(--color-sand-300);
  --color-primary-surface: var(--color-grape-600);
  --color-primary-surface-raw: 87 108 193;
  --color-primary-surface-hover: var(--color-grape-500);
  --color-primary-surface-hover-raw: 97 122 218;
  --color-primary-surface-active: var(--color-grape-700);
  --color-primary-surface-active-raw: 76 95 169;
  --color-primary-surface-content: var(--color-grape-000);
  --color-primary-surface-content-raw: 240 243 254;
  --color-primary-surface-content-subtle: var(--color-grape-300);
  --color-primary-surface-content-subtle-raw: 153 171 245;
  --color-primary-surface-icon: var(--color-grape-100);
  --color-primary-surface-icon-raw: 212 219 251;
  --color-primary-content: var(--color-grape-400);
  --color-primary-content-raw: 109 135 241;
  --color-primary-content-hover: var(--color-grape-300);
  --color-primary-content-hover-raw: 153 171 245;
  --color-primary-content-subtle: var(--color-grape-600);
  --color-primary-content-subtle-raw: 87 108 193;
  --color-primary-icon: var(--color-grape-400);
  --color-primary-icon-raw: 109 135 241;
  --color-primary-background: var(--color-grape-950);
  --color-primary-background-raw: 17 22 38;
  --color-primary-emphasis: var(--color-grape-800);
  --color-primary-emphasis-raw: 55 68 122;
  --color-primary-emphasis-subtle: var(--color-grape-900);
  --color-primary-emphasis-subtle-raw: 43 54 96;
  --color-primary-border: var(--color-grape-600);
  --color-primary-border-raw: 87 108 193;
  --color-primary-border-subtle: var(--color-grape-700);
  --color-primary-border-subtle-raw: 76 95 169;
  --color-primary-border-subtler: var(--color-grape-800);
  --color-primary-border-subtler-raw: 55 68 122;
  --color-primary-border-subtlest: var(--color-grape-900);
  --color-primary-border-subtlest-raw: 43 54 96;
  --color-primary-border-bold: var(--color-grape-400);
  --color-primary-border-bold-raw: 109 135 241;
  --color-primary-focus-outline: var(--color-grape-700);
  --color-primary-focus-outline-raw: 76 95 169;
  --color-primary-shadow: var(--color-grape-950);
  --color-primary-shadow-raw: 17 22 38;
  --color-negative-surface: var(--color-persimmon-600);
  --color-negative-surface-raw: 204 94 70;
  --color-negative-surface-hover: var(--color-persimmon-500);
  --color-negative-surface-hover-raw: 230 105 78;
  --color-negative-surface-active: var(--color-persimmon-700);
  --color-negative-surface-active-raw: 179 82 61;
  --color-negative-surface-content: var(--color-persimmon-000);
  --color-negative-surface-content-raw: 255 241 238;
  --color-negative-surface-content-subtle: var(--color-persimmon-300);
  --color-negative-surface-content-subtle-raw: 255 159 137;
  --color-negative-surface-icon: var(--color-persimmon-100);
  --color-negative-surface-icon-raw: 255 214 205;
  --color-negative-content: var(--color-persimmon-400);
  --color-negative-content-hover: var(--color-persimmon-300);
  --color-negative-content-subtle: var(--color-persimmon-700);
  --color-negative-icon: var(--color-persimmon-400);
  --color-negative-background: var(--color-persimmon-950);
  --color-negative-background-raw: 38 18 13;
  --color-negative-emphasis: var(--color-persimmon-800);
  --color-negative-emphasis-subtle: var(--color-persimmon-900);
  --color-negative-border: var(--color-persimmon-600);
  --color-negative-border-subtle: var(--color-persimmon-700);
  --color-negative-border-subtler: var(--color-persimmon-800);
  --color-negative-border-subtlest: var(--color-persimmon-900);
  --color-negative-border-bold: var(--color-persimmon-400);
  --color-negative-focus-outline: var(--color-persimmon-700);
  --color-negative-focus-outline-raw: 179 82 61;
  --color-positive-surface: var(--color-kale-600);
  --color-positive-surface-raw: 40 122 114;
  --color-positive-surface-hover: var(--color-kale-500);
  --color-positive-surface-hover-raw: 52 152 141;
  --color-positive-surface-active: var(--color-kale-700);
  --color-positive-surface-active-raw: 34 98 93;
  --color-positive-surface-content: var(--color-kale-000);
  --color-positive-surface-content-raw: 244 249 249;
  --color-positive-surface-content-subtle: var(--color-kale-300);
  --color-positive-surface-content-subtle-raw: 122 206 193;
  --color-positive-surface-icon: var(--color-kale-100);
  --color-positive-surface-icon-raw: 212 241 235;
  --color-positive-content: var(--color-kale-400);
  --color-positive-content-hover: var(--color-kale-300);
  --color-positive-content-subtle: var(--color-kale-700);
  --color-positive-icon: var(--color-kale-400);
  --color-positive-background: var(--color-kale-950);
  --color-positive-background-raw: 18 38 37;
  --color-positive-emphasis: var(--color-kale-800);
  --color-positive-emphasis-subtle: var(--color-kale-900);
  --color-positive-border: var(--color-kale-600);
  --color-positive-border-subtle: var(--color-kale-700);
  --color-positive-border-subtler: var(--color-kale-800);
  --color-positive-border-subtlest: var(--color-kale-900);
  --color-positive-border-bold: var(--color-kale-400);
  --color-positive-focus-outline: var(--color-kale-700);
  --color-positive-focus-outline-raw: 34 98 93;
  --color-warning-surface: var(--color-canteloupe-600);
  --color-warning-surface-raw: 204 133 79;
  --color-warning-surface-hover: var(--color-canteloupe-500);
  --color-warning-surface-hover-raw: 230 149 89;
  --color-warning-surface-active: var(--color-canteloupe-700);
  --color-warning-surface-active-raw: 179 116 69;
  --color-warning-surface-content: var(--color-canteloupe-000);
  --color-warning-surface-content-raw: 255 246 239;
  --color-warning-surface-content-subtle: var(--color-canteloupe-300);
  --color-warning-surface-content-subtle-raw: 255 184 130;
  --color-warning-surface-icon: var(--color-canteloupe-100);
  --color-warning-surface-icon-raw: 255 228 208;
  --color-warning-content: var(--color-canteloupe-400);
  --color-warning-content-hover: var(--color-canteloupe-300);
  --color-warning-content-subtle: var(--color-canteloupe-700);
  --color-warning-icon: var(--color-canteloupe-400);
  --color-warning-background: var(--color-canteloupe-950);
  --color-warning-background-raw: 51 28 20;
  --color-warning-emphasis: var(--color-canteloupe-800);
  --color-warning-emphasis-subtle: var(--color-canteloupe-900);
  --color-warning-border: var(--color-canteloupe-600);
  --color-warning-border-subtle: var(--color-canteloupe-700);
  --color-warning-border-subtler: var(--color-canteloupe-800);
  --color-warning-border-subtlest: var(--color-canteloupe-900);
  --color-warning-border-bold: var(--color-canteloupe-400);
  --color-warning-focus-outline: var(--color-canteloupe-700);
  --color-warning-focus-outline-raw: 179 116 69;
  --color-accent-grape-dark: var(--color-grape-300);
  --color-accent-grape-dark-raw: 153 171 245;
  --color-accent-grape-bold: var(--color-grape-500);
  --color-accent-grape-bold-raw: 97 122 218;
  --color-accent-grape-base: var(--color-grape-400);
  --color-accent-grape-base-raw: 109 135 241;
  --color-accent-grape-subtle: var(--color-grape-900);
  --color-accent-grape-subtle-raw: 43 54 96;
  --color-accent-grape-subtlest: var(--color-grape-950);
  --color-accent-grape-subtlest-raw: 17 22 38;
  --color-accent-blueberry-dark: var(--color-blueberry-300);
  --color-accent-blueberry-dark-raw: 153 197 245;
  --color-accent-blueberry-bold: var(--color-blueberry-500);
  --color-accent-blueberry-bold-raw: 50 135 226;
  --color-accent-blueberry-base: var(--color-blueberry-400);
  --color-accent-blueberry-base-raw: 83 158 238;
  --color-accent-blueberry-subtle: var(--color-blueberry-900);
  --color-accent-blueberry-subtle-raw: 28 60 95;
  --color-accent-blueberry-subtlest: var(--color-blueberry-950);
  --color-accent-blueberry-subtlest-raw: 11 24 38;
  --color-accent-kale-dark: var(--color-kale-300);
  --color-accent-kale-dark-raw: 122 206 193;
  --color-accent-kale-bold: var(--color-kale-500);
  --color-accent-kale-bold-raw: 52 152 141;
  --color-accent-kale-base: var(--color-kale-400);
  --color-accent-kale-base-raw: 87 183 171;
  --color-accent-kale-subtle: var(--color-kale-900);
  --color-accent-kale-subtle-raw: 31 66 64;
  --color-accent-kale-subtlest: var(--color-kale-950);
  --color-accent-kale-subtlest-raw: 18 38 37;
  --color-accent-kiwi-dark: var(--color-kiwi-300);
  --color-accent-kiwi-dark-raw: 151 206 122;
  --color-accent-kiwi-bold: var(--color-kiwi-500);
  --color-accent-kiwi-bold-raw: 87 152 52;
  --color-accent-kiwi-base: var(--color-kiwi-400);
  --color-accent-kiwi-base-raw: 120 183 87;
  --color-accent-kiwi-subtle: var(--color-kiwi-900);
  --color-accent-kiwi-subtle-raw: 43 66 31;
  --color-accent-kiwi-subtlest: var(--color-kiwi-950);
  --color-accent-kiwi-subtlest-raw: 25 38 18;
  --color-accent-mango-dark: var(--color-mango-300);
  --color-accent-mango-dark-raw: 255 209 130;
  --color-accent-mango-bold: var(--color-mango-500);
  --color-accent-mango-bold-raw: 244 170 42;
  --color-accent-mango-base: var(--color-mango-400);
  --color-accent-mango-base-raw: 250 186 76;
  --color-accent-mango-subtle: var(--color-mango-900);
  --color-accent-mango-subtle-raw: 98 71 24;
  --color-accent-mango-subtlest: var(--color-mango-950);
  --color-accent-mango-subtlest-raw: 38 28 10;
  --color-accent-canteloupe-dark: var(--color-canteloupe-300);
  --color-accent-canteloupe-dark-raw: 255 184 130;
  --color-accent-canteloupe-bold: var(--color-canteloupe-500);
  --color-accent-canteloupe-bold-raw: 230 149 89;
  --color-accent-canteloupe-base: var(--color-canteloupe-400);
  --color-accent-canteloupe-base-raw: 255 166 99;
  --color-accent-canteloupe-subtle: var(--color-canteloupe-900);
  --color-accent-canteloupe-subtle-raw: 102 66 40;
  --color-accent-canteloupe-subtlest: var(--color-canteloupe-950);
  --color-accent-canteloupe-subtlest-raw: 51 28 20;
  --color-accent-persimmon-dark: var(--color-persimmon-300);
  --color-accent-persimmon-dark-raw: 255 159 137;
  --color-accent-persimmon-bold: var(--color-persimmon-500);
  --color-accent-persimmon-bold-raw: 230 105 78;
  --color-accent-persimmon-base: var(--color-persimmon-400);
  --color-accent-persimmon-base-raw: 255 117 87;
  --color-accent-persimmon-subtle: var(--color-persimmon-900);
  --color-accent-persimmon-subtle-raw: 128 59 44;
  --color-accent-persimmon-subtlest: var(--color-persimmon-950);
  --color-accent-persimmon-subtlest-raw: 38 18 13;
  --color-accent-plum-dark: var(--color-plum-300);
  --color-accent-plum-dark-raw: 233 149 220;
  --color-accent-plum-bold: var(--color-plum-500);
  --color-accent-plum-bold-raw: 204 111 190;
  --color-accent-plum-base: var(--color-plum-400);
  --color-accent-plum-base-raw: 227 123 211;
  --color-accent-plum-subtle: var(--color-plum-900);
  --color-accent-plum-subtle-raw: 114 62 106;
  --color-accent-plum-subtlest: var(--color-plum-950);
  --color-accent-plum-subtlest-raw: 38 21 36;
  --color-accent-fig-dark: var(--color-fig-300);
  --color-accent-fig-dark-raw: 197 149 233;
  --color-accent-fig-bold: var(--color-fig-500);
  --color-accent-fig-bold-raw: 159 108 198;
  --color-accent-fig-base: var(--color-fig-400);
  --color-accent-fig-base-raw: 172 121 210;
  --color-accent-fig-subtle: var(--color-fig-900);
  --color-accent-fig-subtle-raw: 91 62 114;
  --color-accent-fig-subtlest: var(--color-fig-950);
  --color-accent-fig-subtlest-raw: 31 21 38;
  --color-accent-sand-dark: var(--color-sand-300);
  --color-accent-sand-dark-raw: 225 222 213;
  --color-accent-sand-bold: var(--color-sand-500);
  --color-accent-sand-bold-raw: 164 159 149;
  --color-accent-sand-base: var(--color-sand-400);
  --color-accent-sand-base-raw: 202 197 184;
  --color-accent-sand-subtle: var(--color-sand-900);
  --color-accent-sand-subtle-raw: 18 17 13;
  --color-accent-sand-subtlest: var(--color-sand-950);
  --color-accent-sand-subtlest-raw: 13 12 9;
}

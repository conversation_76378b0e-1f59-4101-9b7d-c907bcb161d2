// This file is generated by the design-tokens-converter tool.
// Do not edit this file directly. Edit design-tokens.json instead.

const designTokens = {
  colors: {
    neutral: {
      surface: {
        DEFAULT: "var(--color-neutral-surface)",
        hover: "var(--color-neutral-surface-hover)",
        active: "var(--color-neutral-surface-active)",
        inset: "var(--color-neutral-surface-inset)",
      },
      on: {
        dark: {
          surface: {
            DEFAULT: "var(--color-neutral-on-dark-surface)",
            hover: "var(--color-neutral-on-dark-surface-hover)",
            active: "var(--color-neutral-on-dark-surface-active)",
          },
          content: {
            DEFAULT: "var(--color-neutral-on-dark-content)",
            subtle: "var(--color-neutral-on-dark-content-subtle)",
          },
          icon: "var(--color-neutral-on-dark-icon)",
          background: {
            DEFAULT: "var(--color-neutral-on-dark-background)",
            bold: "var(--color-neutral-on-dark-background-bold)",
          },
          border: "var(--color-neutral-on-dark-border)",
        },
      },
      content: {
        DEFAULT: "var(--color-neutral-content)",
        subtle: "var(--color-neutral-content-subtle)",
        subtler: "var(--color-neutral-content-subtler)",
        subtlest: "var(--color-neutral-content-subtlest)",
      },
      icon: "var(--color-neutral-icon)",
      background: {
        DEFAULT: "var(--color-neutral-background)",
        bold: "var(--color-neutral-background-bold)",
      },
      emphasis: {
        DEFAULT: "var(--color-neutral-emphasis)",
        subtle: "var(--color-neutral-emphasis-subtle)",
      },
      border: {
        DEFAULT: "var(--color-neutral-border)",
        subtle: "var(--color-neutral-border-subtle)",
        subtler: "var(--color-neutral-border-subtler)",
        bold: "var(--color-neutral-border-bold)",
        bolder: "var(--color-neutral-border-bolder)",
        boldest: "var(--color-neutral-border-boldest)",
      },
      shadow: "var(--color-neutral-shadow)",
      inverted: {
        surface: {
          DEFAULT: "var(--color-neutral-inverted-surface)",
          hover: "var(--color-neutral-inverted-surface-hover)",
          active: "var(--color-neutral-inverted-surface-active)",
        },
        content: {
          DEFAULT: "var(--color-neutral-inverted-content)",
          subtle: "var(--color-neutral-inverted-content-subtle)",
          subtler: "var(--color-neutral-inverted-content-subtler)",
          subtlest: "var(--color-neutral-inverted-content-subtlest)",
        },
        icon: "var(--color-neutral-inverted-icon)",
        background: "var(--color-neutral-inverted-background)",
        border: "var(--color-neutral-inverted-border)",
      },
    },
    primary: {
      surface: {
        DEFAULT: "var(--color-primary-surface)",
        hover: "var(--color-primary-surface-hover)",
        active: "var(--color-primary-surface-active)",
        content: {
          DEFAULT: "var(--color-primary-surface-content)",
          subtle: "var(--color-primary-surface-content-subtle)",
        },
        icon: "var(--color-primary-surface-icon)",
      },
      content: {
        DEFAULT: "var(--color-primary-content)",
        hover: "var(--color-primary-content-hover)",
        subtle: "var(--color-primary-content-subtle)",
      },
      icon: "var(--color-primary-icon)",
      background: "var(--color-primary-background)",
      emphasis: {
        DEFAULT: "var(--color-primary-emphasis)",
        subtle: "var(--color-primary-emphasis-subtle)",
      },
      border: {
        DEFAULT: "var(--color-primary-border)",
        subtle: "var(--color-primary-border-subtle)",
        subtler: "var(--color-primary-border-subtler)",
        subtlest: "var(--color-primary-border-subtlest)",
        bold: "var(--color-primary-border-bold)",
      },
      focus: {
        outline: "var(--color-primary-focus-outline)",
      },
      shadow: "var(--color-primary-shadow)",
    },
    negative: {
      surface: {
        DEFAULT: "var(--color-negative-surface)",
        hover: "var(--color-negative-surface-hover)",
        active: "var(--color-negative-surface-active)",
        content: {
          DEFAULT: "var(--color-negative-surface-content)",
          subtle: "var(--color-negative-surface-content-subtle)",
        },
        icon: "var(--color-negative-surface-icon)",
      },
      content: {
        DEFAULT: "var(--color-negative-content)",
        hover: "var(--color-negative-content-hover)",
        subtle: "var(--color-negative-content-subtle)",
      },
      icon: "var(--color-negative-icon)",
      background: "var(--color-negative-background)",
      emphasis: {
        DEFAULT: "var(--color-negative-emphasis)",
        subtle: "var(--color-negative-emphasis-subtle)",
      },
      border: {
        DEFAULT: "var(--color-negative-border)",
        subtle: "var(--color-negative-border-subtle)",
        subtler: "var(--color-negative-border-subtler)",
        subtlest: "var(--color-negative-border-subtlest)",
        bold: "var(--color-negative-border-bold)",
      },
      focus: {
        outline: "var(--color-negative-focus-outline)",
      },
    },
    positive: {
      surface: {
        DEFAULT: "var(--color-positive-surface)",
        hover: "var(--color-positive-surface-hover)",
        active: "var(--color-positive-surface-active)",
        content: {
          DEFAULT: "var(--color-positive-surface-content)",
          subtle: "var(--color-positive-surface-content-subtle)",
        },
        icon: "var(--color-positive-surface-icon)",
      },
      content: {
        DEFAULT: "var(--color-positive-content)",
        hover: "var(--color-positive-content-hover)",
        subtle: "var(--color-positive-content-subtle)",
      },
      icon: "var(--color-positive-icon)",
      background: "var(--color-positive-background)",
      emphasis: {
        DEFAULT: "var(--color-positive-emphasis)",
        subtle: "var(--color-positive-emphasis-subtle)",
      },
      border: {
        DEFAULT: "var(--color-positive-border)",
        subtle: "var(--color-positive-border-subtle)",
        subtler: "var(--color-positive-border-subtler)",
        subtlest: "var(--color-positive-border-subtlest)",
        bold: "var(--color-positive-border-bold)",
      },
      focus: {
        outline: "var(--color-positive-focus-outline)",
      },
    },
    warning: {
      surface: {
        DEFAULT: "var(--color-warning-surface)",
        hover: "var(--color-warning-surface-hover)",
        active: "var(--color-warning-surface-active)",
        content: {
          DEFAULT: "var(--color-warning-surface-content)",
          subtle: "var(--color-warning-surface-content-subtle)",
        },
        icon: "var(--color-warning-surface-icon)",
      },
      content: {
        DEFAULT: "var(--color-warning-content)",
        hover: "var(--color-warning-content-hover)",
        subtle: "var(--color-warning-content-subtle)",
      },
      icon: "var(--color-warning-icon)",
      background: "var(--color-warning-background)",
      emphasis: {
        DEFAULT: "var(--color-warning-emphasis)",
        subtle: "var(--color-warning-emphasis-subtle)",
      },
      border: {
        DEFAULT: "var(--color-warning-border)",
        subtle: "var(--color-warning-border-subtle)",
        subtler: "var(--color-warning-border-subtler)",
        subtlest: "var(--color-warning-border-subtlest)",
        bold: "var(--color-warning-border-bold)",
      },
      focus: {
        outline: "var(--color-warning-focus-outline)",
      },
    },
    accent: {
      grape: {
        dark: "var(--color-accent-grape-dark)",
        bold: "var(--color-accent-grape-bold)",
        base: "var(--color-accent-grape-base)",
        subtle: "var(--color-accent-grape-subtle)",
        subtlest: "var(--color-accent-grape-subtlest)",
      },
      blueberry: {
        dark: "var(--color-accent-blueberry-dark)",
        bold: "var(--color-accent-blueberry-bold)",
        base: "var(--color-accent-blueberry-base)",
        subtle: "var(--color-accent-blueberry-subtle)",
        subtlest: "var(--color-accent-blueberry-subtlest)",
      },
      kale: {
        dark: "var(--color-accent-kale-dark)",
        bold: "var(--color-accent-kale-bold)",
        base: "var(--color-accent-kale-base)",
        subtle: "var(--color-accent-kale-subtle)",
        subtlest: "var(--color-accent-kale-subtlest)",
      },
      kiwi: {
        dark: "var(--color-accent-kiwi-dark)",
        bold: "var(--color-accent-kiwi-bold)",
        base: "var(--color-accent-kiwi-base)",
        subtle: "var(--color-accent-kiwi-subtle)",
        subtlest: "var(--color-accent-kiwi-subtlest)",
      },
      mango: {
        dark: "var(--color-accent-mango-dark)",
        bold: "var(--color-accent-mango-bold)",
        base: "var(--color-accent-mango-base)",
        subtle: "var(--color-accent-mango-subtle)",
        subtlest: "var(--color-accent-mango-subtlest)",
      },
      canteloupe: {
        dark: "var(--color-accent-canteloupe-dark)",
        bold: "var(--color-accent-canteloupe-bold)",
        base: "var(--color-accent-canteloupe-base)",
        subtle: "var(--color-accent-canteloupe-subtle)",
        subtlest: "var(--color-accent-canteloupe-subtlest)",
      },
      persimmon: {
        dark: "var(--color-accent-persimmon-dark)",
        bold: "var(--color-accent-persimmon-bold)",
        base: "var(--color-accent-persimmon-base)",
        subtle: "var(--color-accent-persimmon-subtle)",
        subtlest: "var(--color-accent-persimmon-subtlest)",
      },
      plum: {
        dark: "var(--color-accent-plum-dark)",
        bold: "var(--color-accent-plum-bold)",
        base: "var(--color-accent-plum-base)",
        subtle: "var(--color-accent-plum-subtle)",
        subtlest: "var(--color-accent-plum-subtlest)",
      },
      fig: {
        dark: "var(--color-accent-fig-dark)",
        bold: "var(--color-accent-fig-bold)",
        base: "var(--color-accent-fig-base)",
        subtle: "var(--color-accent-fig-subtle)",
        subtlest: "var(--color-accent-fig-subtlest)",
      },
      sand: {
        dark: "var(--color-accent-sand-dark)",
        bold: "var(--color-accent-sand-bold)",
        base: "var(--color-accent-sand-base)",
        subtle: "var(--color-accent-sand-subtle)",
        subtlest: "var(--color-accent-sand-subtlest)",
      },
    },
    sand: {
      100: "var(--color-sand-100)",
      200: "var(--color-sand-200)",
      300: "var(--color-sand-300)",
      400: "var(--color-sand-400)",
      500: "var(--color-sand-500)",
      600: "var(--color-sand-600)",
      700: "var(--color-sand-700)",
      800: "var(--color-sand-800)",
      850: "var(--color-sand-850)",
      900: "var(--color-sand-900)",
      950: "var(--color-sand-950)",
      "000": "var(--color-sand-000)",
    },
    grape: {
      100: "var(--color-grape-100)",
      200: "var(--color-grape-200)",
      300: "var(--color-grape-300)",
      400: "var(--color-grape-400)",
      500: "var(--color-grape-500)",
      600: "var(--color-grape-600)",
      700: "var(--color-grape-700)",
      800: "var(--color-grape-800)",
      900: "var(--color-grape-900)",
      950: "var(--color-grape-950)",
      "000": "var(--color-grape-000)",
    },
    blueberry: {
      100: "var(--color-blueberry-100)",
      200: "var(--color-blueberry-200)",
      300: "var(--color-blueberry-300)",
      400: "var(--color-blueberry-400)",
      500: "var(--color-blueberry-500)",
      600: "var(--color-blueberry-600)",
      700: "var(--color-blueberry-700)",
      800: "var(--color-blueberry-800)",
      900: "var(--color-blueberry-900)",
      950: "var(--color-blueberry-950)",
      "000": "var(--color-blueberry-000)",
    },
    kale: {
      100: "var(--color-kale-100)",
      200: "var(--color-kale-200)",
      300: "var(--color-kale-300)",
      400: "var(--color-kale-400)",
      500: "var(--color-kale-500)",
      600: "var(--color-kale-600)",
      700: "var(--color-kale-700)",
      800: "var(--color-kale-800)",
      900: "var(--color-kale-900)",
      950: "var(--color-kale-950)",
      "000": "var(--color-kale-000)",
    },
    kiwi: {
      100: "var(--color-kiwi-100)",
      200: "var(--color-kiwi-200)",
      300: "var(--color-kiwi-300)",
      400: "var(--color-kiwi-400)",
      500: "var(--color-kiwi-500)",
      600: "var(--color-kiwi-600)",
      700: "var(--color-kiwi-700)",
      800: "var(--color-kiwi-800)",
      900: "var(--color-kiwi-900)",
      950: "var(--color-kiwi-950)",
      "000": "var(--color-kiwi-000)",
    },
    mango: {
      100: "var(--color-mango-100)",
      200: "var(--color-mango-200)",
      300: "var(--color-mango-300)",
      400: "var(--color-mango-400)",
      500: "var(--color-mango-500)",
      600: "var(--color-mango-600)",
      700: "var(--color-mango-700)",
      800: "var(--color-mango-800)",
      900: "var(--color-mango-900)",
      950: "var(--color-mango-950)",
      "000": "var(--color-mango-000)",
    },
    canteloupe: {
      100: "var(--color-canteloupe-100)",
      200: "var(--color-canteloupe-200)",
      300: "var(--color-canteloupe-300)",
      400: "var(--color-canteloupe-400)",
      500: "var(--color-canteloupe-500)",
      600: "var(--color-canteloupe-600)",
      700: "var(--color-canteloupe-700)",
      800: "var(--color-canteloupe-800)",
      900: "var(--color-canteloupe-900)",
      950: "var(--color-canteloupe-950)",
      "000": "var(--color-canteloupe-000)",
    },
    persimmon: {
      100: "var(--color-persimmon-100)",
      200: "var(--color-persimmon-200)",
      300: "var(--color-persimmon-300)",
      400: "var(--color-persimmon-400)",
      500: "var(--color-persimmon-500)",
      600: "var(--color-persimmon-600)",
      700: "var(--color-persimmon-700)",
      800: "var(--color-persimmon-800)",
      900: "var(--color-persimmon-900)",
      950: "var(--color-persimmon-950)",
      "000": "var(--color-persimmon-000)",
    },
    plum: {
      100: "var(--color-plum-100)",
      200: "var(--color-plum-200)",
      300: "var(--color-plum-300)",
      400: "var(--color-plum-400)",
      500: "var(--color-plum-500)",
      600: "var(--color-plum-600)",
      700: "var(--color-plum-700)",
      800: "var(--color-plum-800)",
      900: "var(--color-plum-900)",
      950: "var(--color-plum-950)",
      "000": "var(--color-plum-000)",
    },
    fig: {
      100: "var(--color-fig-100)",
      200: "var(--color-fig-200)",
      300: "var(--color-fig-300)",
      400: "var(--color-fig-400)",
      500: "var(--color-fig-500)",
      600: "var(--color-fig-600)",
      700: "var(--color-fig-700)",
      800: "var(--color-fig-800)",
      900: "var(--color-fig-900)",
      950: "var(--color-fig-950)",
      "000": "var(--color-fig-000)",
    },
  },
  spacing: {
    0: "var(--spacing-0)",
    50: "var(--spacing-50)",
    100: "var(--spacing-100)",
    200: "var(--spacing-200)",
    300: "var(--spacing-300)",
    400: "var(--spacing-400)",
    500: "var(--spacing-500)",
    600: "var(--spacing-600)",
    700: "var(--spacing-700)",
    800: "var(--spacing-800)",
    900: "var(--spacing-900)",
    1000: "var(--spacing-1000)",
    1100: "var(--spacing-1100)",
    1200: "var(--spacing-1200)",
    1300: "var(--spacing-1300)",
    1400: "var(--spacing-1400)",
    1500: "var(--spacing-1500)",
    1600: "var(--spacing-1600)",
    none: "var(--spacing-none)",
    tightest: "var(--spacing-tightest)",
    tighter: "var(--spacing-tighter)",
    tight: "var(--spacing-tight)",
    base: "var(--spacing-base)",
    wide: "var(--spacing-wide)",
    wider: "var(--spacing-wider)",
    widest: "var(--spacing-widest)",
  },
  typography: {
    fontSize: {
      8: "var(--font-size-8)",
      9: "var(--font-size-9)",
      10: "var(--font-size-10)",
      11: "var(--font-size-11)",
      12: "var(--font-size-12)",
      14: "var(--font-size-14)",
      16: "var(--font-size-16)",
      22: "var(--font-size-22)",
      24: "var(--font-size-24)",
      28: "var(--font-size-28)",
      32: "var(--font-size-32)",
      36: "var(--font-size-36)",
      48: "var(--font-size-48)",
      56: "var(--font-size-56)",
      64: "var(--font-size-64)",
      80: "var(--font-size-80)",
      120: "var(--font-size-120)",
      160: "var(--font-size-160)",
      "body-tinier": "var(--font-size-body-tinier)",
      "body-tiny": "var(--font-size-body-tiny)",
      "body-smallest": "var(--font-size-body-smallest)",
      "body-smaller": "var(--font-size-body-smaller)",
      "body-small": "var(--font-size-body-small)",
      "body-medium": "var(--font-size-body-medium)",
      "label-tinier": "var(--font-size-label-tinier)",
      "label-tiny": "var(--font-size-label-tiny)",
      "label-smallest": "var(--font-size-label-smallest)",
      "label-smaller": "var(--font-size-label-smaller)",
      "label-small": "var(--font-size-label-small)",
      "label-medium": "var(--font-size-label-medium)",
      "title-small": "var(--font-size-title-small)",
      "title-medium": "var(--font-size-title-medium)",
      "title-large": "var(--font-size-title-large)",
      "headline-small": "var(--font-size-headline-small)",
      "headline-medium": "var(--font-size-headline-medium)",
      "headline-large": "var(--font-size-headline-large)",
      "display-small": "var(--font-size-display-small)",
      "display-medium": "var(--font-size-display-medium)",
      "display-large": "var(--font-size-display-large)",
    },
    lineHeight: {
      12: "var(--line-height-12)",
      16: "var(--line-height-16)",
      18: "var(--line-height-18)",
      20: "var(--line-height-20)",
      24: "var(--line-height-24)",
      28: "var(--line-height-28)",
      32: "var(--line-height-32)",
      36: "var(--line-height-36)",
      40: "var(--line-height-40)",
      44: "var(--line-height-44)",
      48: "var(--line-height-48)",
      52: "var(--line-height-52)",
      56: "var(--line-height-56)",
      60: "var(--line-height-60)",
      64: "var(--line-height-64)",
      68: "var(--line-height-68)",
      72: "var(--line-height-72)",
      auto: "var(--line-height-auto)",
      "body-smallest": "var(--line-height-body-smallest)",
      "body-smaller": "var(--line-height-body-smaller)",
      "body-small": "var(--line-height-body-small)",
      "body-medium": "var(--line-height-body-medium)",
      "label-smallest": "var(--line-height-label-smallest)",
      "label-smaller": "var(--line-height-label-smaller)",
      "label-small": "var(--line-height-label-small)",
      "label-medium": "var(--line-height-label-medium)",
      "title-small": "var(--line-height-title-small)",
      "title-medium": "var(--line-height-title-medium)",
      "title-large": "var(--line-height-title-large)",
      "headline-small": "var(--line-height-headline-small)",
      "headline-medium": "var(--line-height-headline-medium)",
      "headline-large": "var(--line-height-headline-large)",
      "display-small": "var(--line-height-display-small)",
      "display-medium": "var(--line-height-display-medium)",
      "display-large": "var(--line-height-display-large)",
    },
    letterSpacing: {
      0: "var(--letter-spacing-0)",
      15: "var(--letter-spacing-15)",
      25: "var(--letter-spacing-25)",
      50: "var(--letter-spacing-50)",
      densest: "var(--letter-spacing-densest)",
      denser: "var(--letter-spacing-denser)",
      dense: "var(--letter-spacing-dense)",
      base: "var(--letter-spacing-base)",
      wide: "var(--letter-spacing-wide)",
      wider: "var(--letter-spacing-wider)",
      widest: "var(--letter-spacing-widest)",
      "-20": "var(--letter-spacing--20)",
      "-15": "var(--letter-spacing--15)",
      "-10": "var(--letter-spacing--10)",
    },
    fontFamily: {
      body: "var(--font-family-body)",
      headings: "var(--font-family-headings)",
      monospace: "var(--font-family-monospace)",
      base: "var(--font-family-base)",
      mono: "var(--font-family-mono)",
    },
    fontWeight: {
      light: "var(--font-weight-light)",
      "light-italic": "var(--font-weight-light-italic)",
      regular: "var(--font-weight-regular)",
      "regular-italic": "var(--font-weight-regular-italic)",
      medium: "var(--font-weight-medium)",
      "medium-italic": "var(--font-weight-medium-italic)",
      semibold: "var(--font-weight-semibold)",
      "semibold-italic": "var(--font-weight-semibold-italic)",
      bold: "var(--font-weight-bold)",
      "bold-italic": "var(--font-weight-bold-italic)",
    },
  },
  cornerRadius: {
    0: "var(--corner-radius-0)",
    2: "var(--corner-radius-2)",
    4: "var(--corner-radius-4)",
    8: "var(--corner-radius-8)",
    12: "var(--corner-radius-12)",
    16: "var(--corner-radius-16)",
    18: "var(--corner-radius-18)",
    24: "var(--corner-radius-24)",
    28: "var(--corner-radius-28)",
    32: "var(--corner-radius-32)",
    36: "var(--corner-radius-36)",
    40: "var(--corner-radius-40)",
    44: "var(--corner-radius-44)",
    48: "var(--corner-radius-48)",
    52: "var(--corner-radius-52)",
    56: "var(--corner-radius-56)",
    60: "var(--corner-radius-60)",
    64: "var(--corner-radius-64)",
    none: "var(--corner-radius-none)",
    smallest: "var(--corner-radius-smallest)",
    smaller: "var(--corner-radius-smaller)",
    small: "var(--corner-radius-small)",
    medium: "var(--corner-radius-medium)",
    large: "var(--corner-radius-large)",
    larger: "var(--corner-radius-larger)",
    largest: "var(--corner-radius-largest)",
  },
};

module.exports = designTokens;

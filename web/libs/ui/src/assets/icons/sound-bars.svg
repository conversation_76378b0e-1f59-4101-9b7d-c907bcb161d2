<svg fill="currentColor" width="24" height="24" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
  <style>
    .spinner_7uc5 {
      animation: spinner_3l8F 1.3s cubic-bezier(0.175, 0.885, 0.32, 1.275) infinite;
      animation-delay: -0.9s;
    }
    .spinner_RibN {
      animation-delay: -0.7s;
    }
    .spinner_ZAxd {
      animation-delay: -0.5s;
    }
    @keyframes spinner_3l8F {
      0% {
        opacity: 0.8;
      }
      66.66% {
        opacity: 0.9;
      }
      0%, 66.66% {
        animation-timing-function: cubic-bezier(0.14, 0.73, 0.34, 1);
        y: 8px; /* Adjusted from 6px to 8px */
        height: 8px; /* Adjusted from 12px to 8px */
      }
      33.33% {
        animation-timing-function: cubic-bezier(0.65, 0.26, 0.82, 0.45);
        y: 4px; /* Adjusted from 1px to 4px */
        height: 16px; /* Adjusted from 22px to 16px */
        opacity: 0.9;
      }
      100% {
        opacity: 1;
      }
    }
  </style>
  <rect class="spinner_7uc5 spinner_RibN" x="6.5" y="8" width="2.8" height="8" rx="1.4" ry="1.4" />
  <rect class="spinner_7uc5" x="10.1" y="8" width="2.8" height="8" rx="1.4" ry="1.4" />
  <rect class="spinner_7uc5 spinner_ZAxd" x="13.7" y="8" width="2.8" height="8" rx="1.4" ry="1.4" />
</svg>
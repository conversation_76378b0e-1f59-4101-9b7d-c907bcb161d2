<svg viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
<path opacity="0.6" fill-rule="evenodd" clip-rule="evenodd" d="M7 8C7 7.44772 7.44772 7 8 7H24C24.5523 7 25 7.44772 25 8C25 8.47669 24.6665 8.87548 24.22 8.97572C24.2831 9.22777 24.2486 9.50407 24.1017 9.74285L19.9523 16.4855C21.2075 17.5853 22 19.2001 22 21C22 24.3137 19.3137 27 16 27C12.6863 27 10 24.3137 10 21C10 17.6863 12.6863 15 16 15C16.7956 15 17.555 15.1548 18.2498 15.4361L22.2104 9H8C7.44772 9 7 8.55228 7 8Z" fill="#7F64FF"/>
<g filter="url(#filter0_d)">
<path d="M23.2756 5.62075C23.3042 5.4777 23.3185 5.40617 23.3257 5.38071C23.5165 4.69893 24.483 4.69893 24.6739 5.38071C24.681 5.40617 24.6953 5.4777 24.7239 5.62075C24.7377 5.6898 24.7446 5.72432 24.752 5.75579C24.9252 6.49639 25.5034 7.07466 26.244 7.24785C26.2755 7.25521 26.31 7.26211 26.3791 7.27592C26.5221 7.30453 26.5936 7.31884 26.6191 7.32596C27.3009 7.51678 27.3009 8.48335 26.6191 8.67416C26.5936 8.68128 26.5221 8.69559 26.3791 8.7242C26.31 8.73801 26.2755 8.74491 26.244 8.75227C25.5034 8.92546 24.9252 9.50373 24.752 10.2443C24.7446 10.2758 24.7377 10.3103 24.7239 10.3794C24.6953 10.5224 24.681 10.594 24.6739 10.6194C24.483 11.3012 23.5165 11.3012 23.3257 10.6194C23.3185 10.594 23.3042 10.5224 23.2756 10.3794C23.2618 10.3103 23.2549 10.2758 23.2475 10.2443C23.0744 9.50373 22.4961 8.92546 21.7555 8.75227C21.724 8.74491 21.6895 8.73801 21.6204 8.7242C21.4774 8.69559 21.4059 8.68128 21.3804 8.67416C20.6986 8.48335 20.6986 7.51678 21.3804 7.32596C21.4059 7.31884 21.4774 7.30453 21.6204 7.27592C21.6895 7.26211 21.724 7.25521 21.7555 7.24785C22.4961 7.07466 23.0744 6.49639 23.2475 5.75579C23.2549 5.72432 23.2618 5.6898 23.2756 5.62075Z" fill="#5531FF"/>
</g>
<g filter="url(#filter1_d)">
<path d="M7.27562 5.62075C7.30423 5.4777 7.31853 5.40617 7.32566 5.38071C7.51647 4.69893 8.48304 4.69893 8.67385 5.38071C8.68098 5.40617 8.69528 5.4777 8.72389 5.62075C8.7377 5.6898 8.74461 5.72432 8.75197 5.75579C8.92515 6.49639 9.50342 7.07466 10.244 7.24785C10.2755 7.25521 10.31 7.26211 10.3791 7.27592C10.5221 7.30453 10.5936 7.31884 10.6191 7.32596C11.3009 7.51678 11.3009 8.48335 10.6191 8.67416C10.5936 8.68128 10.5221 8.69559 10.3791 8.7242C10.31 8.73801 10.2755 8.74491 10.244 8.75227C9.50342 8.92546 8.92515 9.50373 8.75197 10.2443C8.74461 10.2758 8.7377 10.3103 8.72389 10.3794C8.69528 10.5224 8.68098 10.594 8.67385 10.6194C8.48304 11.3012 7.51647 11.3012 7.32566 10.6194C7.31853 10.594 7.30423 10.5224 7.27562 10.3794C7.26181 10.3103 7.2549 10.2758 7.24754 10.2443C7.07436 9.50373 6.49609 8.92546 5.75549 8.75227C5.72401 8.74491 5.68949 8.73801 5.62045 8.7242C5.47739 8.69559 5.40587 8.68128 5.38041 8.67416C4.69862 8.48335 4.69862 7.51678 5.38041 7.32596C5.40587 7.31884 5.47739 7.30453 5.62045 7.27592C5.68949 7.26211 5.72401 7.25521 5.75549 7.24785C6.49609 7.07466 7.07436 6.49639 7.24754 5.75579C7.2549 5.72432 7.26181 5.6898 7.27562 5.62075Z" fill="#5531FF"/>
</g>
<path d="M15.3476 18.2609C15.4706 17.6459 15.5321 17.3384 15.602 17.2466C15.8021 16.9839 16.1974 16.9839 16.3975 17.2466C16.4674 17.3384 16.5289 17.6459 16.6519 18.2609C16.735 18.676 16.7765 18.8836 16.8508 19.0636C17.0538 19.5554 17.4444 19.946 17.9362 20.149C18.1162 20.2234 18.3238 20.2649 18.7389 20.3479C19.3539 20.4709 19.6614 20.5324 19.7532 20.6023C20.0159 20.8024 20.0159 21.1977 19.7532 21.3978C19.6614 21.4677 19.3539 21.5292 18.7389 21.6522C18.3238 21.7353 18.1162 21.7768 17.9362 21.8511C17.4444 22.0541 17.0538 22.4447 16.8508 22.9365C16.7765 23.1165 16.735 23.3241 16.6519 23.7392C16.5289 24.3542 16.4674 24.6617 16.3975 24.7535C16.1974 25.0162 15.8021 25.0162 15.602 24.7535C15.5321 24.6617 15.4706 24.3542 15.3476 23.7392C15.2646 23.3241 15.223 23.1165 15.1487 22.9365C14.9457 22.4447 14.5551 22.0541 14.0633 21.8511C13.8833 21.7768 13.6757 21.7353 13.2606 21.6522C12.6456 21.5292 12.3381 21.4677 12.2463 21.3978C11.9836 21.1977 11.9836 20.8024 12.2463 20.6023C12.3381 20.5324 12.6456 20.4709 13.2606 20.3479C13.6757 20.2649 13.8833 20.2234 14.0633 20.149C14.5551 19.946 14.9457 19.5554 15.1487 19.0636C15.223 18.8836 15.2646 18.676 15.3476 18.2609Z" fill="white"/>
<defs>
<filter id="filter0_d" x="17.8691" y="2.86938" width="12.2614" height="12.2614" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1"/>
<feGaussianBlur stdDeviation="1.5"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.333333 0 0 0 0 0.192157 0 0 0 0 1 0 0 0 0.6 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"/>
</filter>
<filter id="filter1_d" x="1.86914" y="2.86938" width="12.2614" height="12.2614" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1"/>
<feGaussianBlur stdDeviation="1.5"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.333333 0 0 0 0 0.192157 0 0 0 0 1 0 0 0 0.6 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"/>
</filter>
</defs>
</svg>

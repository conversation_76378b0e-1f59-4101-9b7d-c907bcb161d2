<svg viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
<path opacity="0.4" fill-rule="evenodd" clip-rule="evenodd" d="M21 3.5C17.9624 3.5 15.5 5.96243 15.5 9C15.5 12.0376 17.9624 14.5 21 14.5C24.0376 14.5 26.5 12.0376 26.5 9C26.5 5.96243 24.0376 3.5 21 3.5ZM3.5 16C3.5 12.9624 5.96243 10.5 9 10.5C12.0376 10.5 14.5 12.9624 14.5 16C14.5 19.0376 12.0376 21.5 9 21.5C5.96243 21.5 3.5 19.0376 3.5 16ZM15.5 23C15.5 19.9624 17.9624 17.5 21 17.5C24.0376 17.5 26.5 19.9624 26.5 23C26.5 26.0376 24.0376 28.5 21 28.5C17.9624 28.5 15.5 26.0376 15.5 23Z" fill="currentColor"/>
<g filter="url(#filter0_d)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M21 6.5C19.6193 6.5 18.5 7.61929 18.5 9C18.5 10.3807 19.6193 11.5 21 11.5C22.3807 11.5 23.5 10.3807 23.5 9C23.5 7.61929 22.3807 6.5 21 6.5ZM6.5 16C6.5 14.6193 7.61929 13.5 9 13.5C10.3807 13.5 11.5 14.6193 11.5 16C11.5 17.3807 10.3807 18.5 9 18.5C7.61929 18.5 6.5 17.3807 6.5 16ZM18.5 23C18.5 21.6193 19.6193 20.5 21 20.5C22.3807 20.5 23.5 21.6193 23.5 23C23.5 24.3807 22.3807 25.5 21 25.5C19.6193 25.5 18.5 24.3807 18.5 23Z" fill="currentColor"/>
</g>
<defs>
<filter id="filter0_d" x="3.5" y="4.5" width="23" height="25" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1"/>
<feGaussianBlur stdDeviation="1.5"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.4 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"/>
</filter>
</defs>
</svg>

<svg viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
<path opacity="0.4" fill-rule="evenodd" clip-rule="evenodd" d="M7 8C7 7.44772 7.44772 7 8 7H24C24.5523 7 25 7.44772 25 8C25 8.47669 24.6665 8.87548 24.22 8.97572C24.2831 9.22777 24.2486 9.50407 24.1017 9.74285L19.9523 16.4855C21.2075 17.5853 22 19.2001 22 21C22 24.3137 19.3137 27 16 27C12.6863 27 10 24.3137 10 21C10 17.6863 12.6863 15 16 15C16.7956 15 17.555 15.1548 18.2498 15.4361L22.2104 9H8C7.44772 9 7 8.55228 7 8Z" fill="currentColor"/>
<g filter="url(#filter0_d)">
<rect x="21.5" y="5.5" width="5" height="5" rx="2.5" fill="currentColor"/>
</g>
<g filter="url(#filter1_d)">
<rect x="5.5" y="5.5" width="5" height="5" rx="2.5" fill="currentColor"/>
</g>
<path fill-rule="evenodd" clip-rule="evenodd" d="M15.1464 17.6464C15 17.7929 15 18.0286 15 18.5V20H13.5C13.0286 20 12.7929 20 12.6464 20.1464C12.5 20.2929 12.5 20.5286 12.5 21C12.5 21.4714 12.5 21.7071 12.6464 21.8536C12.7929 22 13.0286 22 13.5 22H15V23.5C15 23.9714 15 24.2071 15.1464 24.3536C15.2929 24.5 15.5286 24.5 16 24.5C16.4714 24.5 16.7071 24.5 16.8536 24.3536C17 24.2071 17 23.9714 17 23.5V22H18.5C18.9714 22 19.2071 22 19.3536 21.8536C19.5 21.7071 19.5 21.4714 19.5 21C19.5 20.5286 19.5 20.2929 19.3536 20.1464C19.2071 20 18.9714 20 18.5 20H17V18.5C17 18.0286 17 17.7929 16.8536 17.6464C16.7071 17.5 16.4714 17.5 16 17.5C15.5286 17.5 15.2929 17.5 15.1464 17.6464Z" fill="white"/>
<defs>
<filter id="filter0_d" x="18.5" y="3.5" width="11" height="11" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1"/>
<feGaussianBlur stdDeviation="1.5"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.4 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"/>
</filter>
<filter id="filter1_d" x="2.5" y="3.5" width="11" height="11" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1"/>
<feGaussianBlur stdDeviation="1.5"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.4 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"/>
</filter>
</defs>
</svg>

.themeToggle {
  --button-background-color:  var(--color-neutral-background);
  --button-border-color:  var(--color-neutral-border);
  --button-background-color-hover:  var(--color-neutral-surface);
  --button-border-color-hover:  var(--color-neutral-border-bold);
  --button-background-color-active:  var(--color-neutral-surface-active);
  --button-border-color-active:  var(--color-neutral-border-bold);
  --button-text-color:  var(--color-neutral-content);
  --button-icon-color: var(--color-neutral-content);
  --button-font-size: var(--font-size-16);

  display: flex;
  justify-content: center;
  overflow: hidden;
  height: 32px;
  align-self: center;
  font-size: var(--button-font-size);
  border: 1px solid var(--button-border-color);
  border-radius: 24px;
  background: var(--button-background-color);
  padding: 4px 8px 4px 0;
  transition: all 150ms ease-out;
  cursor: pointer;

  &:hover {
    background: var(--button-background-color-hover);
    border-color: var(--button-border-color-hover);
  }

  &:active {
    background: var(--button-background-color-active);
    border-color: var(--button-border-color-active);
  }

  &__label {
    display: flex;
    height: 100%;
    align-items: center;
    color: var(--button-text-color);
    width: 38px;
  }

  &__icon {
    display: flex;
    align-items: center;
    width: 36px;
    height: 28px;
    margin-right: 0;
    position: relative;
    align-self: center;
    color: var(--button-icon-color);
  }

  &.light .animationWrapper {
    transform: rotate(90deg);
  }

  &.dark .animationWrapper {
    transform: rotate(0deg);
  }

}

.animationWrapper {
  width: 90px;
  height: 90px;
  position: absolute;
  top: -1px;
  left: -29px;
  transition: all 600ms cubic-bezier(0.47, 0, 0.23, 1.30);
  overflow: hidden;

  svg {
    position: absolute;
  }
}

.moon {
  left: 31px;
  top: 0;
}

.sun {
  left: 0;
  top: 30px;
}

.betaBadge {
  margin-left: var(--spacing-tight);
}

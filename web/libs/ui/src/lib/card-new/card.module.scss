.card {
width: 660px;
border-radius: var(--corner-radius-small);
border: 1px solid var(--color-neutral-border);
background: var(--color-neutral-background);
}

.cardHeader {
  padding: var(--spacing-wide, 24px);
  padding-bottom: 0;
}

.cardTitle{
color: var(--color-neutral-content);

/* headline/medium */
font-family: var(--font-family-headings, Figtree);
font-size: var(--font-size-headline-medium, 28px);
font-style: normal;
font-weight: var(--font-weight-medium, 500);
line-height: var(--line-height-headline-medium, 36px); /* 128.571% */
letter-spacing: var(--letter-spacing-denser, -0.15px);
}

.cardDescription{
  padding-top: var(--spacing-tight);
color: var(--color-neutral-content-subtler);

/* title/medium */
font-family: var(--font-family-headings, Figtree);
font-size: var(--font-size-title-medium, 16px);
font-style: normal;
line-height: var(--line-height-title-medium, 24px); /* 150% */
letter-spacing: var(--letter-spacing-base, 0);
}

.cardContent {
padding: var(--spacing-wide, 24px);
}

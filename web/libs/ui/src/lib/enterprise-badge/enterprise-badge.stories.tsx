import type { <PERSON>a, StoryObj } from "@storybook/react";
import { EnterpriseBadge } from "./enterprise-badge";

const meta: Meta<typeof EnterpriseBadge> = {
  component: EnterpriseBadge,
  title: "UI/EnterpriseBadge",
  tags: ["autodocs"],
  argTypes: {
    filled: { control: "boolean" },
  },
};

export default meta;
type Story = StoryObj<typeof EnterpriseBadge>;

export const Default: Story = {
  args: {},
};

export const Filled: Story = {
  args: {
    filled: true,
  },
};

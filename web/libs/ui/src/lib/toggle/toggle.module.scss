.toggle {
  width: 42px;
  height: 24px;
  display: block;
  min-width: 42px;
  max-height: 24px;
  border-radius: 12px;
  position: relative;
  cursor: pointer;
  color: var(--toggle-color, var(--grape_500));
  transition: all 100ms ease-out;
  background: var(--color-neutral-surface);
  border: 1px solid var(--color-neutral-border);
  box-sizing: content-box;

  &:hover {
    border: 1px solid var(--color-neutral-border-bold);
  }

  &__input {
    top: 0;
    left: 0;
    margin: 0;
    opacity: 0;
    padding: 0;
    width: 100%;
    height: 100%;
    position: absolute;
  }

  &__indicator {
    height: 24px;
    width: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 120ms ease;

    &::before {
      content: "";
      width: 10px;
      height: 10px;
      border-radius: 100%;
      transition: all 120ms ease;
      background: var(--color-neutral-border-bold);
      box-shadow: 0 5px 10px rgb(18 17 13 / 15%), inset 0 -1px 0 rgb(18 17 13 / 10%), inset 0 0 0 1px rgb(18 17 13 / 5%);
    }
  }

  &_checked &__indicator {
    margin-left: 18px;

    &::before {
      width: 16px;
      height: 16px;
      background: var(--color-primary-surface);
      box-shadow: 0 5px 10px var(--color-primary-shadow), inset 0 -1px 0 rgba(var(--color-primary-shadow) / 10%);
    }
  }

  &_alwaysBlue &__indicator {
    &::before {
      width: 16px;
      height: 16px;
      background: var(--primary_link);
      box-shadow: 0 5px 10px var(--color-primary-surface-content-subtle), inset 0 -1px 0 rgb(0 0 0 / 10%);
    }
  }

  &_disabled {
    box-shadow: none;
    pointer-events: none;
    background: var(--color-neutral-background);
    cursor: not-allowed;
  }

  &_disabled &__indicator {
    transition: none;
  }

  &_disabled &__indicator::before,
  &_disabled.toggle_checked &__indicator::before {
    box-shadow: none;
    transition: none;
    background-color: var(--color-neutral-border);
  }
}

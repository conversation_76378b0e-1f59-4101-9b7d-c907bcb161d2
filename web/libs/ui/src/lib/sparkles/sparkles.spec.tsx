import "@testing-library/jest-dom";
import { render, screen } from "@testing-library/react";
import { Sparkles, type SparklesProps } from "./sparkles";

const defaultProps: SparklesProps = {
  children: <span>Test</span>,
};

describe("Sparkles", () => {
  it("renders children", () => {
    render(<Sparkles {...defaultProps} />);
    expect(screen.getByText("Test")).toBeInTheDocument();
  });

  it("applies custom className", () => {
    render(<Sparkles {...defaultProps} className="custom-class" data-testid="sparkles-root" />);
    const root = screen.getByTestId("sparkles-root");
    expect(root).toHaveClass("custom-class");
  });

  it("sets aria-hidden and data-testid", () => {
    render(<Sparkles {...defaultProps} data-testid="sparkles-root" />);
    const root = screen.getByTestId("sparkles-root");
    expect(root).toHaveAttribute("aria-hidden", "true");
  });

  it("disables animation when disableAnimation is true", () => {
    render(<Sparkles {...defaultProps} disableAnimation buttonSize={40} />);
    const root = screen.getByText("Test").parentElement?.parentElement;
    expect(root).toHaveStyle({ width: "40px", height: "40px" });
  });

  it("shows area overlay when showArea is true", () => {
    render(<Sparkles {...defaultProps} showArea data-testid="sparkles-root" />);
    expect(screen.getByTestId("sparkles-root").querySelector("svg.sparkles-area-overlay")).toBeInTheDocument();
  });

  it("renders correct number of sparkles (animation enabled)", () => {
    jest.useFakeTimers();
    render(<Sparkles {...defaultProps} sparkleCount={3} sparkleLifetime={100} />);
    jest.advanceTimersByTime(500);
    // Sparkles are rendered as <span> children of the root
    const root = screen.getByText("Test").parentElement?.parentElement;
    expect(root?.querySelectorAll("svg").length).toBeGreaterThanOrEqual(1); // At least area overlay or sparkles
    jest.useRealTimers();
  });

  it("supports custom color", () => {
    render(<Sparkles {...defaultProps} color="#ff00ff" showArea data-testid="sparkles-root" />);
    const svg = screen.getByTestId("sparkles-root").querySelector("svg.sparkles-area-overlay");
    expect(svg).toBeInTheDocument();
  });

  it("supports areaShape and cutoutShape props", () => {
    render(
      <Sparkles
        {...defaultProps}
        areaShape="rect"
        areaWidth={40}
        areaHeight={20}
        cutoutShape="rect"
        cutoutWidth={10}
        cutoutHeight={5}
        showArea
        data-testid="sparkles-root"
      />,
    );
    const svg = screen.getByTestId("sparkles-root").querySelector("svg.sparkles-area-overlay");
    expect(svg).toBeInTheDocument();
  });

  it("renders with minimum and maximum sparkle size", () => {
    render(<Sparkles {...defaultProps} sparkleSizeMin={5} sparkleSizeMax={10} />);
    // No error thrown
    expect(screen.getByText("Test")).toBeInTheDocument();
  });

  it("renders with custom sparkle lifetime and interval", () => {
    render(
      <Sparkles
        {...defaultProps}
        sparkleLifetime={500}
        sparkleBaseIntervalMin={100}
        sparkleBaseIntervalMax={200}
        sparkleJitter={50}
      />,
    );
    expect(screen.getByText("Test")).toBeInTheDocument();
  });

  it("renders with custom min distance and size diff", () => {
    render(<Sparkles {...defaultProps} sparkleMinDistance={2} sparkleMinSizeDiff={1} />);
    expect(screen.getByText("Test")).toBeInTheDocument();
  });

  it("renders with custom area and cutout radius", () => {
    render(<Sparkles {...defaultProps} areaRadius={20} cutoutRadius={5} showArea data-testid="sparkles-root" />);
    const svg = screen.getByTestId("sparkles-root").querySelector("svg.sparkles-area-overlay");
    expect(svg).toBeInTheDocument();
  });

  it("renders with custom button size", () => {
    render(<Sparkles {...defaultProps} buttonSize={50} />);
    const root = screen.getByText("Test").parentElement?.parentElement;
    expect(root).toHaveStyle({ width: "50px", height: "50px" });
  });
});

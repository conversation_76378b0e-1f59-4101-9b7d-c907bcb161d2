.sparkle {
  animation: sparkles-come-in-out 2000ms forwards, sparkles-spin 2000ms ease-in-out;
  box-shadow: none;
}

@keyframes sparkles-come-in-out {
  0% {
    transform: scale(0);
    opacity: 0;
  }

  30% {
    transform: scale(1);
    opacity: 1;
  }

  70% {
    transform: scale(1);
    opacity: 1;
  }

  100% {
    transform: scale(0);
    opacity: 0;
  }
}

@keyframes sparkles-spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(90deg);
  }
}

.sparkles-area-overlay {
  /* Controls the color of the area overlay for showArea */
  color: var(--color-accent-primary-base);
} 

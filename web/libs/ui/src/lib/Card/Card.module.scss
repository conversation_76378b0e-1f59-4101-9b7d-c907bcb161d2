.card {
  border-radius: 5px;
  background-color: var(--color-neutral-background);
  border: 1px solid var(--sand_300);


  &:not(:first-child) {
    margin-top: 24px;
  }
}

.card.cardNoMargin {
  &:not(:first-child) {
    margin-top: 0;
  }
}

.header {
  display: flex;
  height: 48px;
  padding: 24px 24px 0;
  font-weight: 500;
  font-size: 16px;
  line-height: 18px;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 1px 0 0 rgb(0 0 0 / 10%);
}

.header.headerNoUnderline {
  box-shadow: none;
}

.headerContent {
  display: flex;
  align-items: center;
}

.content {
  padding: 24px;
}

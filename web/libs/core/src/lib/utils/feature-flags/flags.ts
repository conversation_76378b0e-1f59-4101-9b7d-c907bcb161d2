//// LEGACY FLAGS ////
// Consider all pre-XFN flags legacy. Should be revised and removed.
/**
 * Aync import for task data
 * @deprecated
 */
export const FF_LSDV_4915 = "fflag_feat_all_lsdv_4915_async_task_import_13042023_short";

/**
 * Fix displaying of created_at in the review mode
 */
export const FF_DEV_1480 = "ff_front_dev_1480_created_on_in_review_180122_short";

/**
 * Notifications
 */
export const FF_DEV_1658 = "ff_front_dev_1658_notification_center_170222_short";

/**
 * Model version selector per model backend
 */
export const FF_DEV_1682 = "ff_front_dev_1682_model_version_dropdown_070622_short";

/**
 * Fixes how presigned urls are generated and accessed to remove possibility of CORS errors.
 */
export const FF_LSDV_4711 = "fflag_fix_all_lsdv_4711_cors_errors_accessing_task_data_short";

/**
 * Enables "Enterprise Awareness" features
 */
export const FF_LSDV_E_297 = "fflag_feat_front_lsdv_e_297_increase_oss_to_enterprise_adoption_short";

/**
 * Project list search
 */
export const FF_PROD_281 = "fflag_feat_front_prod_281_project_list_search_19072023_short";
//// END OF LEGACY FLAGS ////

/**
 * Prompter workflow
 */
export const FF_DIA_835 = "fflag_feat_all_dia_835_prompter_workflow_long";

/**
 * Joyride
 */
export const FF_PRODUCT_TOUR = "fflag_feat_dia_1697_product_tour_short";
/**
 * It adds an unsaved changes warning and fix some caching problems on a saving project
 */
export const FF_UNSAVED_CHANGES = "fflag_feat_front_leap_1198_unsaved_changes_180724";

/**
 * Enables JWT tokens
 */
export const FF_AUTH_TOKENS = "fflag__feature_develop__prompts__dia_1829_jwt_token_auth";

export const FF_IMPROVE_GLOBAL_ERROR_MESSAGES = "fflag_feat_front_optic_1746_improve_global_error_messages_short";

/**
 * Enable new home page for LSO and LSE
 */
export const FF_HOMEPAGE = "fflag_all_feat_dia_1777_ls_homepage_short";

/**
 * Sample datasets UI for the import flow
 */
export const FF_SAMPLE_DATASETS = "fflag_feat_dia_1920_project_creation_sample_data_short";

/**
 * JSON preview window for the import flow
 */
export const FF_JSON_PREVIEW = "fflag_feat_dia_1925_view_sample_raw_json_short";

/**
 * Allow to resize spans in Text tag
 */
export const FF_ADJUSTABLE_SPANS = "fflag_feat_front_leap_1973_adjustable_spans_090425_short";

/**
 * Enables the theme toggle in the UI
 */
export const FF_THEME_TOGGLE = "fflag_feat_front_optic_1217_theme_toggle_short";

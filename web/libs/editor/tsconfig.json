{"compilerOptions": {"checkJs": false, "jsx": "preserve", "rootDirs": ["./src"], "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "allowJs": true, "allowSyntheticDefaultImports": true, "strict": true, "typeRoots": ["src/types", "../../node_modules/@types"], "types": []}, "files": [], "include": ["src/types", "node_modules/@types"], "references": [{"path": "./tsconfig.lib.json"}, {"path": "./tsconfig.spec.json"}, {"path": "./cypress/tsconfig.json"}, {"path": "./tests/integration/tsconfig.json"}], "extends": "../../tsconfig.base.json"}
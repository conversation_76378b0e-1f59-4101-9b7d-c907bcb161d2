{"name": "@humansignal/editor", "version": "0.0.0", "license": "MIT", "private": true, "dependencies": {"@ant-design/colors": "6.0.0", "@ant-design/icons": "4.8.1", "@martel/audio-file-decoder": "^2.3.15", "@thi.ng/rle-pack": "^3.1.30", "antd": "^4.3.3", "chroma-js": "^2.1.1", "d3": "^5.16.0", "date-fns": "^2.20.1", "emoji-regex": "^7.0.3", "insert-after": "^0.1.4", "keymaster": "^1.6.2", "konva": "^8.1.3", "lodash": "4.17.21", "lodash.get": "^4.4.0", "lodash.ismatch": "^4.4.0", "lodash.throttle": "^4.1.1", "mobx": "^5.15.4", "mobx-react": "^6", "mobx-react-lite": "2.2.2", "mobx-state-tree": "^3.16.0", "nanoid": "^3.3.8", "papaparse": "^5.4.1", "pleasejs": "^0.4.2", "prop-types": "15.8.1", "rc-tree": "^5.7.8", "react": "18.2.0", "react-beautiful-dnd": "^13.1.1", "react-dom": "18.2.0", "react-konva": "^17.0.2-0", "react-konva-utils": "^0.2.0", "react-virtualized-auto-sizer": "^1.0.20", "react-window": "^1.8.9", "sanitize-html": "^2.12.1", "strman": "^2.0.1", "wavesurfer.js": "^6.0.1", "xpath-range": "^1.1.1"}, "main": "src/index.js"}
.topbar {
  height: var(--topbar-height);
  width: 100%;
  z-index: 101;
  flex: none;
  display: flex;
  align-items: stretch;
  justify-content: space-between;
  background-color: var(--color-neutral-background);
  border-bottom: 1px solid var(--color-neutral-border);
  user-select: none;
  position: sticky;
  top: 0;

  &__button:not(.button_look_primary) {
    &:hover {
      background-color: var(--color-primary-emphasis-subtle);
    }
  }

  &__group {
    display: flex;
    align-items: stretch;
  }

  &__section {
    display: flex;
    padding: 0 8px;
    align-items: center;
    box-sizing: border-box;

    &_flat {
      padding: 0;
    }

    & + & {
      border-left: 1px solid var(--color-neutral-border);
    }
  }

  &_newLabelingUI {
    display: block;
    background-color: var(--color-neutral-background);
    height: 42px;
    border-bottom: 1px solid var(--color-neutral-border);

    & .topbar {
      &__group {
        height: 42px;
        flex: 1;
        align-items: center;
      }
    }
  }
}
.annotations-list {
  min-width: 264px;
  box-sizing: border-box;
  position: relative;

  &__draft {
    border-bottom: 1px solid var(--color-neutral-border);
  }

  &__list {
    top: calc(100% - 1px);
    left: -1px;
    right: -1px;
    position: absolute;
    background-color: var(--color-neutral-background);
    box-shadow: 0 0 1px rgb(0 0 0 / 10%), 0 2px 8px -2px rgb(0 0 0 / 10%), 0 0 0 1px rgb(0 0 0 / 10%) inset;
  }

  &__create {
    border-bottom: 1px solid var(--color-neutral-border);
  }

  &__create,
  &__entity {
    height: 48px;
    display: flex;
    padding: 0 16px;
    align-items: center;
    cursor: pointer;

    &:hover {
      background-color: var(--color-primary-surface-content);
    }
  }

  &__entity {
    &_selected {
      pointer-events: none;
      background-color: var(--color-primary-surface-content);
    }
  }

  &__user {
    font-size: 14px;
    line-height: 16px;
    width: 136px;
    display: flex;
  }

  &__icons {
    display: flex;
    justify-content: flex-end;
    align-items: center;
  }

  &__icon-column {
    display: flex;
    align-items: center;
    justify-content: center;

    &:nth-child(2) {
      width: 32px;
      height: 32px;
    }
  }

  &__name {
    flex: 1;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
  }

  &__entity-id {
    font-size: 11px;
    opacity: 0.5;
    flex: none;
    padding-left: 5px;
  }

  &__review {
    font-size: 11px;
    line-height: 13px;

    &_state {
      &_accepted {
        color: var(--success_color);
      }

      &_fixed {
        color: var(--persimmon_400);
      }

      &_rejected {
        color: var(--color-negative-content);
      }
    }
  }

  &__created {
    font-size: 11px;
    line-height: 13px;
    color: var(--sand_500);
  }

  &__date {
    color: var(--sand_500);
  }

  &__counter {
    font-size: 11px;
    line-height: 13px;
    color: var(--sand_500);
  }

  &__toggle {
    width: 32px;
    height: 32px;
    position: relative;

    &::before {
      top: 50%;
      left: 50%;
      content: '';
      width: 10px;
      height: 10px;
      display: block;
      position: absolute;
      border: 2px solid var(--sand_900);
      border-bottom: none;
      border-right: none;
      margin-top: -3px;
      transform: translate3d(-50%, -50%, 0) rotate(225deg);
    }

    &_opened::before {
      margin-top: 3px;
      transform: translate3d(-50%, -50%, 0) rotate(45deg);
    }
  }
}

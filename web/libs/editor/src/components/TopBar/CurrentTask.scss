.current-task {
  padding: 0 8px 0 16px;
  display: flex;
  align-content: center;
  align-items: center;

  &_with-history {
    height: 100%;
    display: grid;
    width: 220px;
    grid-template: "id buttons" 1fr "id buttons" 1fr / 1fr min-content;
  }

  &__task-id {
    grid-area: id;
    color: var(--color-neutral-content);
    font-size: 14px;
    user-select: text;

    &::before {
      content: '#';
    }
  }

  &__task-count {
    grid-area: counter;
    color: var(--color-neutral-content);
    font-size: 11px;
    user-select: none;
  }

  &__history-controls {
    display: flex;
    grid-area: buttons;

    &_newui {
      gap: 8px;
      margin-left: 8px;
    }
  }

  &__prevnext {
    width: 32px;
    height: 32px;
    place-self: center center;

    &_newui {
      &:hover {
        background: var(--color-primary-emphasis-subtle);
      }
    }

    &::before {
      top: 50%;
      left: 50%;
      content: '';
      width: 10px;
      height: 10px;
      display: block;
      position: absolute;
      border: 2px solid var(--color-neutral-icon);
      border-bottom: none;
      border-right: none;
      margin-top: -3px;
      transform: translate3d(-50%, -50%, 0) rotate(225deg);
    }

    &_next {
      grid-area: n;
      transform: rotate(-90deg);
    }

    &_next.current-task__prevnext_postpone::before {
      border-color: var(--color-primary-border);
    }

    &_prev {
      grid-area: p;
      transform: rotate(90deg);
    }

    &_hidden {
      display: none;
    }

    &_disabled {
      &:hover {
        background: inherit;
      }

      &::before {
        border: 2px solid var(--color-neutral-content-subtlest);
        border-bottom: none;
        border-right: none;
      }
    }
  }
}

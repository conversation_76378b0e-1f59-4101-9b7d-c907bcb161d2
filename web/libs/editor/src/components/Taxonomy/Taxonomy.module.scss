.taxonomy {
  border: 1px solid var(--color-neutral-border);
  border-radius: 6px;
  padding: 4px 4px 0; // margin of last row of selected divs will work as padding
  width: max-content;
  max-width: 100%;
  position: relative;

  & > span {
    display: flex;
    align-items: center;
    line-height: 30px;
    padding: 0 4px; // 4px padding to stay on the same indent as selected
    margin-bottom: 4px; // also replaces padding to be consistent with selected divs
    white-space: nowrap;
    cursor: pointer;
  }
}

.taxonomy > span > svg {
  transform: rotate(-180deg);
}

.taxonomy_open > span > svg {
  transform: none;
}

.taxonomy__selected {
  display: flex;
  flex-wrap: wrap;
  min-height: 28px; // 24px button + 4px margin

  div {
    margin: 0 2px 4px 0;
    background: hsl(0deg 0% 95%);
    padding: 0; // all the right space should be a clickable button, so no padding
    border-radius: 4px;
    display: flex;
    align-items: center;

    span {
      padding-inline: 8px;
    }
  }

  input[type="button"] {
    border: none;
    background: none;
    cursor: pointer;
    width: 24px;
    height: 24px;
    padding: 0;
    line-height: 1;
    font-weight: 600;
    font-size: 20px;
    color: var(--primary_link);
    margin-left: -8px; // to align with the right edge of the text

    &:hover {
      color: var(--color-negative-content);
    }
  }
}

.taxonomy__dropdown {
  position: absolute;
  z-index: 10;
  background: var(--color-neutral-background);
  border-radius: 6px;
  left: -1px;
  border: 1px solid var(--color-neutral-border);
  box-shadow: 0 4px 10px rgb(0 0 0 / 12%);

  input[type="checkbox"] {
    margin-right: 4px;
    font-size: 20px;
    line-height: 30px;
    width: 0.7em;
    height: 1.4em;

    &:disabled {
      opacity: 0.5;
      pointer-events: none;
    }
  }
}

.taxonomy__search {
  width: 100%;
  border: none;
  border-bottom: 1px solid var(--color-neutral-border);
  border-radius: 3px 3px 0 0;
  padding: 6px 16px;
  margin-bottom: 4px;
  box-shadow: 0 -2px 0 6px var(--color-neutral-background); // white background over scrolled items
  position: sticky;
  top: 0;
  z-index: 1;

  &:focus,
  &:focus-visible {
    outline: 1px solid var(--primary_link);
    border-bottom-color: var(--primary_link);
  }
}

.taxonomy__item ~ div {
  margin-left: 16px;
  width: calc(100% - 100px);
}

.taxonomy__measure {
  visibility: hidden;
  position: absolute;
  pointer-events: none;
  display: flex;
  padding-left: 43px;
  padding-right: 8px;
  line-height: 2em;
}

;

.taxonomy__item {
  display: flex;

  &:focus-within {
    background: var(--sand_200);
  }

  &_user,
  &_session {
    font-style: italic;
  }

  &_session {
    color: var(--primary_link);
  }
}

.taxonomy__item label {
  overflow-wrap: break-word;
  flex: 1;
  flex-grow: 1;
  width: max-content;
  line-height: 2em;
  padding-right: 8px;
}

.taxonomy__grouping {
  font-size: 16px;
  display: inline-block;
  width: 24px;
  height: 24px;
  margin: 2px 3px 0 0;
  text-align: center;
  user-select: none;
  flex-shrink: 0;
  pointer-events: all;
  font-family: var(--font-mono);

  svg {
    transition: transform 0.1s; // visible rotation of arrow
  }

  &:not(:empty) {
    cursor: pointer;
  }
}

.taxonomy__extra {
  margin-left: auto;
  margin-right: 5px;
  opacity: 0.6;
  padding: 0 12px;
  width: 40px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 40px; // 24px padding + 16px width for count/dots
  text-align: right;
  position: relative; // important for wide labels to not overlap counts with label

  &_actions {
    visibility: hidden;
    display: block;
    font-weight: bold;
    cursor: pointer;
    text-align: center;
  }

  .taxonomy__item &_count:not(:last-child) {
    position: absolute;
  }

  .taxonomy__item:hover &_count:not(:last-child) {
    visibility: hidden;
    display: none;
  }

  .taxonomy__item:hover &_actions {
    visibility: visible;
    display: block;
  }
}

.taxonomy__action {
  zoom: 1; // something to apply this class
}

.taxonomy__add__container {
  margin-bottom: 8px;
}

.taxonomy__add {
  margin-left: 27px; // chevron 24px + 3px margin
  display: flex;
  align-items: center;
  opacity: 0.6;

  &::before {
    content: "+";
    width: 16px; // as checkbox
    padding: 4px 0;
    text-align: center;
    font-size: 18px;
    line-height: 0;
  }

  button {
    flex-grow: 1;
    border: none;
    padding: 1px 4px;
    font-weight: normal;
    text-align: left;
  }
}

.taxonomy__newitem {
  display: flex;

  [name="taxonomy__add"] {
    padding: 1px 2px;
    border: none;
    flex-grow: 1;
    margin-left: 45px;
    margin-right: 8px;
    font: inherit;
    font-style: italic;
    color: #09f;
    background: rgb(0 0 0 / 3%);
  }
}

.taxonomy__collapsable {
  cursor: pointer;
  color: var(--color-neutral-content-subtler);
}

.comment-form-buttons {
  display: flex;
  justify-content: flex-end;

  &__buttons {
    display: flex;
    gap: 8px;
    height: 100%;
    align-items: center;
  }

  &__action {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-shrink: 0;
    flex-grow: 0;
    appearance: none;
    border: none;
    background-color: transparent;
    color: var(--secondary-action-color);
    border-radius: var(--primary-action-border-radius);
    height: 32px;
    width: 32px;

    &:hover {
      color: var(--secondary-action-color-hover);
      background-color: var(--primary-action-surface-color-hover);
    }

    &_highlight {
      color: var(--secondary-action-color-hover);
      background-color: var(--highlight-color);
      border: 1px solid var(--highlight-border-color);
    }

    &[type="submit"] {
      color: var(--primary-action-color);

      &:hover {
        color: var(--primary-action-color);
      }
    }
  }
}
.comment-form-new {
  --primary-action-color: var(--color-primary-content);
  --primary-action-padding: 4px;
  --primary-action-border-radius: 4px;
  --primary-action-surface-color-hover: var(--color-primary-emphasis-subtle);
  --secondary-action-color: var(--color-neutral-content-subtle);
  --secondary-action-color-hover: var(--color-neutral-content);
  --text-input-min-height: 40px;
  --action-button-width: 40px;
  --action-buttons-width: calc(var(--action-button-width) * 2);
  --tooltip-text-color: var(--color-negative-content);
  --highlight-color: var(--color-primary-background);
  --highlight-border-color: var(--color-primary-border-subtler);

  & .textarea_inline {
    position: relative;
    z-index: 1;
    padding: 8px 16px !important;
    flex: 1;
    width: 100%;

    &:focus {
      outline: none;
      box-shadow: 0 0 0 4px var(--color-primary-focus-outline), inset 0 -1px 0 rgb(0 0 0 / 10%), inset 0 0 0 1px rgb(0 0 0 / 15%), inset 0 0 0 1px var(--color-primary-focus-outline);
      border-color: var(--color-neutral-border-boldest);
    }
  }

  &__category-selector {
    display: flex;
    flex-grow: 1;

    // hack to keep selector within bounds and not push buttons out of view
    min-width: 0;
  }

  &__category-selector > div {
    width: 100%;
  }

  &__text-row {
    display: flex;
    gap: 8px;
  }

  &__classifications-row {
    display: flex;
    width: 100%;
    align-items: center;
    gap: 8px;
  }

  &_inline {
    & .comment-form-new__actions {
      grid-row: 1 / 2;
      grid-column: 2 / -1;

      & button {
        padding: 0;
        height: calc(var(--text-input-min-height) - 8px);
        width: calc(var(--action-button-width) - 8px);
      }
    }
  }

  &__tooltipMessage {
    color: var(--tooltip-text-color);
    font-size: 0.9em;
  }

  &__link-state {
    grid-column: 1 / -1;
    border: 1px solid var(--color-neutral-border);
    border-radius: 4px;
    border-top: none;
    border-top-left-radius: 0;
    border-top-right-radius: 0;
    padding-top: 7px;
    margin-top: -8px;
    overflow: hidden;
  }
}
.comment-form {
  --primary-action-color: var(--grape_500);
  --primary-action-padding: 4px;
  --primary-action-border-radius: 4px;
  --primary-action-surface-color-hover: var(--color-primary-surface-content);
  --text-input-min-height: 40px;
  --tooltip-text-color: var(--color-negative-content);

  display: grid;
  grid-template-columns: auto var(--text-input-min-height);
  align-items: center;
  gap: 4px;

  &__primary-action {
    height: 100%;
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: flex-start;
    flex-shrink: 0;
    flex-grow: 0;

    button {
      margin-top: 4px;
      display: flex;
      justify-content: center;
      align-items: center;
      flex-shrink: 0;
      flex-grow: 0;
      appearance: none;
      border: none;
      background-color: transparent;
      color: var(--primary-action-color);
      border-radius: var(--primary-action-border-radius);

      &:hover {
        background-color: var(--primary-action-surface-color-hover);
      }
    }
  }

  &_inline {
    & .comment-form__primary-action {
      grid-row: 1 / 2;
      grid-column: 2 / -1;

      & button {
        padding: 0;
        height: calc(var(--text-input-min-height) - 8px);
        width: calc(var(--text-input-min-height) - 8px);
      }
    }
  }

  &__tooltipMessage {
    color: var(--tooltip-text-color);
    font-size: 0.9em;
  }
}

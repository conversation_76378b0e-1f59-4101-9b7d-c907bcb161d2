%settings-title {
  font-size: 16px;
  font-weight: 500;
  line-height: 24px;
  letter-spacing: 0.15px;
  text-align: left;
  font-family: var(--font-sans);
  color: var(--color-neutral-content);
}


.settings__title {
  @extend %settings-title;

  color: var(--color-neutral-content);
}

.settings {
  &__field + &__field {
    margin-top: 10px;
    color: var(--color-neutral-content);
  }
}

.settings_newUI {
  .settings__field {
    display: flex;
    align-items: flex-start;
    cursor: pointer;
  }

  .settings__field + .settings__field {
    margin-top: 16px;
  }

  .settings__label {
    flex: 1;

    &__title {
      @extend %settings-title;

      align-items: center;
      display: flex;
      gap: 8px;
    }

    &__description {
      font-size: 14px;
      font-weight: 400;
      line-height: 20px;
      letter-spacing: 0.25px;
      text-align: left;
    }
  }
}

.keys {
  &__key {
    padding: 2px 4px 1px;
    display: inline-flex;
    align-items: center;
    background: var(--color-neutral-background);
    border: 1px solid var(--color-neutral-border);
    border-radius: var(--corner-radius-smaller);
    font-weight: bold;
    font-size: 12px;
    text-transform: uppercase;
    box-shadow: inset 0 -2px 0 0 rgba(var(--color-neutral-shadow-raw) / 8%), 0 1px 4px rgba(var(--color-neutral-shadow-raw) / 6%);
  }

  &__key + &__key {
    margin-left: 4px;
  }

  &__key-group {
    margin-left: -8px;
    margin-right: -8px;
    padding-left: 8px;
    padding-right: 8px;
  }

  &__key-group + &__key-group {
    margin-top: 5px;
    border-top: 2px solid var(--color-neutral-border);
  }
}

.settings-modal {
  --modal-button-text-color-active: var(--color-primary-content);
  --modal-border-radius: 8px;
  --modal-padding: 24px;

  width: 100% !important;
  max-width: 568px !important;

  .ant-pagination-item-active {
    border-color: var(--color-primary-content);
  }

  .ant-pagination-item-active a {
    color: var(--color-primary-content);
  }

  :global(.ant-tabs-tab .ant-tabs-tab-btn) {
    @extend %settings-title;

    text-shadow: none;
  }

  :global(.ant-tabs-ink-bar) {
    background: var(--color-primary-content);
  }

  :global(.ant-modal-close) {
    top: 28px;
    right: var(--modal-padding);
  }

  :global(.ant-modal-close-icon) {
    display: none;
  }

  :global(.ant-modal-close-x) {
    height: 24px !important;
    width: 24px !important;
    line-height: 24px !important;
    color: var(--modal-button-text-color-active);
  }

  :global(.ant-tabs-tab) {
    padding: calc(var(--modal-padding) / 4) var(--modal-padding) !important;

    :hover {
      color: var(--color-primary-content);
    }
  }

  :global(.ant-tabs .ant-tabs-nav)::before {
    border-color: var(--color-neutral-border);
  }

  :global(.ant-tabs-tab + .ant-tabs-tab) {
    margin-left: 0 !important;
  }

  :global(.ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn) {
    color: var(--modal-button-text-color-active) !important;
  }

  :global(.ant-modal-content) {
    border-radius: var(--modal-border-radius) !important;
    background-color: var(--color-neutral-background);
  }

  :global(.ant-modal-body) {
    padding: 0 var(--modal-padding) var(--modal-padding) !important;
  }

  :global(.ant-modal-header) {
    border-radius: var(--corner-radius-small) var(--modal-border-radius) 0 0 !important;
    padding: var(--modal-padding) !important;
    border: none !important;
    background-color: var(--color-neutral-background);
    color: var(--color-neutral-content);
  }

  :global(.ant-modal-title) {
    font-size: 24px;
    font-weight: 500;
    line-height: 32px;
    letter-spacing: 0;
    text-align: left;
    color: var(--color-neutral-content);
  }

  .settings-tag {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding-inline: 4px;
    color: var(--color-accent-grape-bold);
    background-color: var(--color-accent-grape-subtle);
    border-radius: 2px;
    height: 20px;
    font-size: 11px;
    font-weight: 500;
    line-height: 16px;
    letter-spacing: 0.5px;
    text-align: center;
  }

  :global(.ant-table) {
    background-color: var(--color-neutral-background);
    color: var(--color-neutral-content-subtle);
  }

  :global(.ant-table .ant-table-thead .ant-table-cell) {
    background-color: var(--color-neutral-surface);
    color: var(--color-neutral-content);
    border-color: var(--color-neutral-border);
  }

  :global(.ant-table .ant-table-tbody .ant-table-cell) {
    border-color: var(--color-neutral-border);
  }

  :global(.ant-table .ant-table-tbody .ant-table-row:hover .ant-table-cell) {
    background-color: transparent;
  }

  :global(.ant-pagination.ant-table-pagination.ant-pagination-mini) {
    gap: 4px;
  }

  :global(.ant-pagination .ant-pagination-item-active .ant-pagination-item-link) {
    background-color: var(--color-primary-emphasis-subtle);
    color: var(--color-neutral-content);
    border-radius: var(--radius-medium);
  }

  :global(.ant-pagination.ant-pagination-mini .ant-pagination-prev .ant-pagination-item-link) {
    color: var(--color-neutral-content-subtlest);
  }

  :global(.ant-pagination.ant-pagination-mini .ant-pagination-prev .ant-pagination-item-link:not[disabled]) {
    color: var(--color-neutral-content);
  }

  :global(.ant-pagination-item a) {
    color: var(--color-neutral-content);
  }


  :global(.ant-pagination-item:hover a) {
    color: var(--color-primary-content);
    border-color: var(--color-primary-border);
  }

  :global(.ant-pagination.ant-pagination-mini .ant-pagination-next .ant-pagination-item-link) {
    color: var(--color-neutral-content);
  }

  :global(.ant-pagination-item-active) {
    border-color: var(--color-primary-emphasis);
    background: var(--color-primary-emphasis);
    color: var(--color-neutral-content);
  }

  :global(.ant-input) {
    background-color: var(--color-neutral-background);
    color: var(--color-neutral-content);
    border: 1px solid var(--color-neutral-border);
    border-radius: var(--corner-radius-smaller);
  }

  :global(.ant-input:hover) {
    border-color: 1px solid var(--color-neutral-border-bold);
  }

  :global(.ant-input:focus) {
    border-color: 1px solid var(--color-neutral-border);
    box-shadow: 0 0 0 4px var(--color-primary-focus-outline);
  }
}

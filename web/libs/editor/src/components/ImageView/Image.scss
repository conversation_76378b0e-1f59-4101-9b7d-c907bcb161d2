.image {
  top: 0;
  position: absolute;
  overflow: hidden;
}

.image-progress {
  padding: 14px 16px;
  display: flex;
  justify-content: center;
  background: var(--color-neutral-background);
  border-radius: 4px;
  box-shadow: 0 0 0 0.5px rgb(var(--color-neutral-shadow-raw) / 20%);
  margin: 16px 0.5px;
  flex-direction: column;
  color: var(--color-neutral-content);
  font-weight: bold;

  &__bar {
    --height: 6px;

    /* Reset the default appearance */
    appearance: none;

    /* Get rid of default border in Firefox. */
    border: none;
    width: 200px;
    height: var(--height);
    display: block;

    &::-webkit-progress-inner-element{
      padding: 3px;
    }

    &[value]::-webkit-progress-bar {
      background-color: #eee;
      border-radius: 10px;
      box-shadow: 0 0 0 0.5px rgb(0 0 0 / 40%);
      height: var(--height);
    }

    &[value]::-webkit-progress-value {
      background: royalblue;
      border-radius: 10px;
      background-size: 35px 20px, 100% 100%, 100% 100%;
      transition: all 80ms ease;
      height: var(--height);
    }
  }
}

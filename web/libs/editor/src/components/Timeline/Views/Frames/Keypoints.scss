.keypoints {
  --hover: transparent;

  height: 24px;
  display: flex;
  background-color: var(--hover);

  &__label {
    left: 0;
    flex: none;
    width: var(--offset);
    height: 24px;
    z-index: 2;
    align-items: center;
    position: sticky;
    display: flex;
    padding: 0 8px;
    justify-content: space-between;
    background: linear-gradient(0deg, var(--hover), var(--hover) 100%), linear-gradient(0deg, var(--color-neutral-surface), var(--color-neutral-surface) 100%);
    box-shadow: 1px 0 0 rgb(var(--color-neutral-shadow-raw) / 5%);
  }

  &__data {
    display: grid;
    text-align: right;
    grid-auto-flow: column;
    grid-auto-columns: minmax(24px, 1fr);
  }

  &__data-item {
    &_faded {
      color: var(--color-neutral-content-subtler);
    }
  }

  &__name {
    font-size: 14px;
    width: min-content;
    color: var(--color);
  }

  &__keypoints {
    flex: 1;
    z-index: 1;
    height: 100%;
    position: relative;
  }

  &__lifespan {
    top: 50%;
    height: 2px;
    width: 100%;
    z-index: 1;
    position: absolute;
    transform: translateY(-50%);
    background-color: var(--lifespan-color);

    &_hidden {
      opacity: 0.3;
    }
  }

  &_timeline &__lifespan {
    height: 20px;
  }

  &__point {
    top: 50%;
    width: 9px;
    height: 9px;
    z-index: 2;
    position: absolute;
    border-radius: 2px;
    transform: translate3d(-50%, -50%, 0) rotate(45deg);
    background-color: var(--point-color);
  }

  &_timeline &__point {
    width: 3px;
    height: 20px;
    transform: translate3d(-100%, -50%, 0);
  }

  &_timeline &__point_last {
    transform: translate3d(0, -50%, 0);
  }

  /** instant */
  &_timeline &__lifespan_instant &__point {
    width: 6px;
    transform: translate3d(-50%, -50%, 0);
  }

  &__actions {
    display: flex;
  }

  &:hover {
    --hover: rgb(var(--color-neutral-shadow-raw) / calc( 8% * var(--shadow-intensity) ));
  }

  &_selected,
  &_selected:hover {
    --hover: rgb(var(--color-neutral-shadow-raw) / calc( 8% * var(--shadow-intensity) ));
  }
}

.region-action {
  margin: 1px;
  margin-left: 2px;
  height: 18px;
  min-width: 18px;
  cursor: pointer;
  border-radius: 3px;
  background: var(--color-neutral-background);
  box-shadow: 0 1px 0 rgb(0 0 0 / 10%), 0 0 0 1px rgb(0 0 0 / 15%);

  svg {
    width: 18px;
    height: 18px;
  }

  &_danger {
    color: var(--color-negative-content);
  }
}

.timeline-frames {
  --frame-size: 10px;
  --view-size: 0;
  --view-height: 64px;

  width: 100%;
  min-height: var(--view-height);
  position: relative;
  overflow: hidden;
  box-sizing: content-box;
  background: var(--color-neutral-surface);

  &__controls {
    height: 0;
    width: 100%;
  }

  &__labels-bg {
    top: 0;
    left: 0;
    height: 100%;
    z-index: 3;
    background-color: var(--color-neutral-surface);
    position: absolute;
    box-shadow: -1px 0 0 rgb(var(--color-neutral-shadow-raw) / 5%);
  }

  &__filler {
    min-width: 100%;
    min-height: 100%;
    height: min-content;
    white-space: nowrap;
    width: calc(var(--view-size) + var(--offset));
  }

  &__background {
    inset: 0 0 0 var(--offset);
    z-index: 1;
    position: absolute;
    background-color: rgb(var(--color-neutral-shadow-raw) / 5%);
  }

  &__scroll {
    z-index: 3;
    width: 100%;
    height: var(--view-height);
    overflow: hidden;
    position: relative;
    box-sizing: content-box;
    padding-top: 24px;
    border-top: 1px solid var(--color-neutral-border);
    background-image: repeating-linear-gradient(90deg, var(--color-neutral-background),
        var(--color-neutral-background) var(--frame-width),
        transparent var(--frame-width),
        transparent calc(var(--frame-width) + 1)),
      linear-gradient(0deg, var(--sand_100), transparent 50%);
  }

  &__indicator {
    top: 0;
    left: 0;
    bottom: 0;
    z-index: 2;
    position: absolute;
    width: var(--frame-size);
    box-shadow: inset 0 1px var(--color-neutral-border), inset 0 -1px var(--color-neutral-border);
    background: linear-gradient(0deg, rgb(var(--color-neutral-shadow-raw) / 5%), rgb(var(--color-neutral-shadow-raw) / 5%)), var(--color-neutral-background);

    &::before {
      inset: -5px;
      content: "";
      display: block;
      cursor: col-resize;
      position: absolute;
    }

    &::after {
      top: 100%;
      left: 0;
      content: "";
      width: 100%;
      height: var(--view-height);
      display: block;
      position: absolute;
      pointer-events: none;
      background: linear-gradient(180deg, rgb(var(--color-neutral-shadow-raw) / 15%) -1.28%, rgb(var(--color-neutral-shadow-raw) / 15%) 69.21%, rgb(var(--color-neutral-shadow-raw) / 0%) 100%), rgb(var(--color-neutral-shadow-raw) / 5%);
    }
  }

  &__hover {
    top: 0;
    bottom: 0;
    left: 0;
    z-index: 2;
    width: var(--frame-size);
    position: absolute;
    pointer-events: none;
    background: linear-gradient(0deg, rgb(var(--color-neutral-shadow-raw) / 5%), rgb(var(--color-neutral-shadow-raw) / 5%)), var(--color-neutral-surface);

    &::before {
      top: 0;
      left: 50%;
      height: 12px;
      color: var(--color-neutral-content-subtler);
      display: flex;
      padding: 0 4px;
      min-width: 100%;
      font-size: 10px;
      align-items: center;
      justify-content: center;
      width: min-content;
      position: absolute;
      content: attr(data-frame);
      transform: translateX(-50%);
      background-color: var(--color-neutral-surface-inset);
    }
  }

  &__keyframes {
    width: var(--view-size);
  }

  &__keypoints {
    position: relative;
  }
}

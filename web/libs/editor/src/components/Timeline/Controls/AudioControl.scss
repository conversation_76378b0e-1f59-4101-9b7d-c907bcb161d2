.audio-control {
  position: relative;

  &__modal {
    left: 0;
    top: 36px;
    position: absolute;
    width: 232px;
    background: var(--color-neutral-background);
    border-radius: 4px;
    padding: 22px 0 0;
    box-shadow: 0 4px 10px 0 rgba(var(--color-neutral-shadow-raw) / calc( 30% * var(--shadow-intensity) ));
    z-index: 10;
  }

  &__range {
    margin: 0 20px;
    width: calc(100% - 40px);
    padding: 0;
  }

  &__mute {
    margin-top: 10px;
    padding: 2px 0;
    border-top: 1px solid var(--color-neutral-border);
    font-size: 14px;
    cursor: pointer;
  }

  &__mute-button {
    padding: 8px 16px;
    color: var(--color-neutral-content);
    transition: all 150ms ease-out;

    &:hover {
      background: var(--color-primary-emphasis-subtle);
    }
  }
}
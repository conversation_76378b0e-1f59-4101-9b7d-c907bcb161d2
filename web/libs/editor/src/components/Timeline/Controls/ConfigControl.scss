.audio-config {
  position: relative;

  &__modal {
    left: 0;
    top: 36px;
    position: absolute;
    width: 260px;
    background: var(--color-neutral-background);
    box-shadow: 0 4px 10px 0 rgba(var(--color-neutral-shadow-raw) / 30%);
    border-radius: 4px;
    padding: 12px 0 0;
    z-index: 10;
  }

  &__toggle {
    padding: 8px 16px;
    font-size: 12px;
  }

  &__range {
    margin: 0 20px;
    width: calc(100% - 40px);
    padding: 0;
  }

  &__buttons {
    margin-top: 10px;
    padding: 2px 0;
    border-top: 1px solid var(--color-neutral-border);
    font-size: 14px;
    cursor: pointer;
  }

  &__menu-button {
    padding: 8px 16px;
    cursor: pointer;
    transition: all 150ms ease-out;
    color: var(--color-neutral-content-subtle);

    &:hover {
      background: var(--color-primary-emphasis-subtle);
    }
  }
}
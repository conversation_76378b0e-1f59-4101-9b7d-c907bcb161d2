.seeker {
  width: 100%;
  height: 24px;
  position: relative;

  &__track {
    width: 100%;
    height: 100%;
    border-radius: 3px;
    background: var(--color-neutral-surface-inset);
  }

  &__indicator {
    top: 0;
    left: 0;
    z-index: 1;
    width: 100px;
    height: 24px;
    max-width: 100%;
    position: absolute;
    cursor: col-resize;
    box-sizing: border-box;

    &::before {
      inset: 1px;
      content: '';
      display: block;
      position: absolute;
      border-radius: 3px;
      background: var(--color-neutral-background);
      box-shadow: 0 0 0 1px rgb(0 0 0 / 5%), 0 2px 5px rgb(0 0 0 / 20%);
    }
  }

  &__position {
    top: 0;
    left: 0;
    width: 1px;
    z-index: 2;
    cursor: grab;
    height: 100%;
    position: absolute;
    background-color: var(--color-neutral-inverted-surface);

    &::before {
      inset: 0 -5px;
      content: '';
      display: block;
      position: absolute;
    }

    &:hover {
      border-radius: 30px;
      box-shadow: 0 0 0 3px var(--color-primary-emphasis);
    }
  }

  &__minimap {
    top: 0;
    left: 0;
    z-index: 3;
    width: 100%;
    height: 100%;
    position: absolute;
    pointer-events: none;
  }
}

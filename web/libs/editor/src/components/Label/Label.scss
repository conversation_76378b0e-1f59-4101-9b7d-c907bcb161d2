.label {
  --color: var(--sand_500);
  --background: var(--sand_200);

  display: inline-flex;
  padding: 2px 8px;
  margin: 0;
  border-radius: 3px;
  font-weight: 400;
  font-size: 14px;
  line-height: 22px;
  background-color: var(--background);
  color: var(--color-neutral-content);
  border-left: 4px solid var(--color);
  position: relative;
  align-items: center;

  &_margins {
    margin: 0 8px 8px 0;

    &:last-child {
      margin: 0 0 8px;
    }
  }

  &_empty {
    border-left-width: 0;
  }

  &_hidden {
    display: none;
  }

  &_clickable {
    cursor: pointer;
  }

  &_solid {
    background-color: var(--color);
  }

  &_selected {
    background-color: var(--color);
    color: var(--white);
  }

  &_selected &__hotkey {
    color: var(--white);
  }

  &__text {
    position: relative;
    white-space: nowrap;
  }

  &__hotkey {
    position: relative;
    color: var(--color-neutral-content-subtler);
    font-size: 13px;
    margin-left: 12px;
  }
}
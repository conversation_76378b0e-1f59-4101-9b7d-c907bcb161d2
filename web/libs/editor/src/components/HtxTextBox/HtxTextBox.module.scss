.input {
  width: 100%;
  padding: 0.4em 1em;
  border-width: 1px;
  display: block;
  background-color: var(--color-neutral-surface);
  border-color: var(--color-neutral-border);
  color: var(--color-neutral-content);
}

.editing {
  padding: 0;
  position: relative;

  .enter {
    pointer-events: all;
  }
}

.delete {
  color: #1890ff;

  // Typography.Paragraph has margin-bottom: 1em, so we have to compensate it to have icon in the middle
  margin-top: -1em;
  padding-left: 1em;
  line-height: 1.8em;
}
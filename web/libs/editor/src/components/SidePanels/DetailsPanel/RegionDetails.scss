.detailed-region {
  &__icon {
    width: 24px;
    height: 24px;
    margin-right: 4px;
    flex-shrink: 0;
  }

  &__head {
    display: flex;
    margin: 8px 0;
    padding: 0 8px;
    align-items: center;
    justify-content: space-between;
    gap: 8px;
  }

  &__title {
    display: flex;
    align-items: flex-start;
    word-break: break-word;

    &+span {
      color: var(--color-neutral-content);

      &::before {
        opacity: 0.6;
        content: "ID ";
      }
    }
  }

  &__index {
    width: 18px;
    height: 18px;
    margin-top: 3px; // to align with 24px icon
    display: flex;
    font-size: 9px;
    text-align: center;
    align-items: center;
    border-radius: 4px;
    justify-content: center;
    background-color: currentcolor;
    flex-grow: 0;
    margin-right: 4px;
  }

  &__index_value {
    color: var(--color-sand-000);
  }

  &__result {
    padding: 0 8px;
  }

  &__meta-text {
    width: calc(100% - 16px);
    border: 1px solid transparent;
    margin: 0 8px;
    padding: 4px 8px;
    min-height: 64px;
    border-radius: 4px;
    outline: none;
    background-color: var(--color-neutral-surface);
    transition-property: background-color, border-color;
    transition-duration: 80ms;
    transition-timing-function: ease;

    &:hover {
      textarea {
        border-color: var(--color-neutral-border);
        background-color: var(--color-neutral-background);
      }
    }

    &:focus {
      background-color: var(--color-neutral-background);
      border-color: var(--color-primary-border-subtle);
      box-shadow: 0 0 0 1px var(--color-primary-border-subtle);
    }

    &:not(textarea) {
      white-space: pre-wrap;
      word-break: break-all;
    }
  }

  &_compact &__head {
    margin: -5px 0;
  }

  &__warning {
    box-sizing: border-box;
    display: flex;
    flex-direction: row;
    align-items: center;
    padding: 8px;
    gap: 8px;
    background: var(--color-warning-background);
    border: 1px solid var(--color-warning-border);
    border-radius: 4px;
    margin: 0 16px 8px;
    flex: none;
    order: 0;
    flex-grow: 1;

    svg {
      width: 20px;
      height: 17px;
      fill: var(--incomplete-warning-color, var(--color-warning-icon));
    }
  }
}

.region-meta {
  &__content {
    margin: 8px 0;

    &_type {
      &_text {
        padding: 3px 6px;
        border-radius: 4px;
        background-color: var(--color-neutral-surface);
        white-space: pre-wrap;
        line-break: loose;
        word-break: break-all;
      }
    }
  }

  &__result {
    display: grid;
    grid-template-columns: auto 1fr;
    grid-column-gap: 10px;
    color: var(--color-neutral-content);

    & .ant-typography {
      color: var(--color-neutral-content);

      & mark {
        color: var(--color-neutral-content);
        background-color: var(--color-primary-background);
        padding: var(--spacing-tightest) var(--spacing-tighter);
        border-radius: var(--corner-radius-smaller);
      }
    }
  }

  &__value {
    p {
      padding: 0;
      margin-bottom: 4px;

      &::before {
        padding-right: 4px;
        display: inline-block;
        content: attr(data-counter);
        opacity: 0.6;
      }
    }
  }
}

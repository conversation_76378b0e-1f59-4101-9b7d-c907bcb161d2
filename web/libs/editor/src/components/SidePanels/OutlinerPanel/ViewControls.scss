.view-controls {
  top: 0;
  z-index: 2;
  height: var(--view-controls-height);
  display: grid;
  position: sticky;
  padding: 0 4px;
  align-items: center;
  grid-auto-flow: column;
  grid-auto-columns: min-content;
  grid-column-gap: 4px;
  background-color: var(--color-neutral-background);
  box-shadow: 0 1px 0 rgb(0 0 0 / 10%);


  &__sort {
    display: flex;
    align-items: center;
  }

  &__label {
    width: 100%;
    display: flex;
    align-items: center;
    gap: var(--spacing-tight);

    span {
      display: flex;
      align-items: center;
      color: var(--grape_500);
    }
  }

  &_collapsed {
    top: 0;
    position: static;
  }

  .button {
    height: 24px;
    font-size: 11px;
    font-weight: 400;
    padding: 0;
    justify-content: flex-start;
    white-space: nowrap;
    box-shadow: none;
    gap: var(--spacing-tighter);

    &_newUI {
      font-size: 12px;
      font-weight: 500;
      line-height: 24px;
      height: 24px;
      box-shadow: initial;
    }

    &__icon {
      flex: none;
      margin: 0;
    }

    &:focus {
      box-shadow: none;
    }

    &:hover {
      background: var(--color-primary-emphasis-subtle);
      border-radius: 4px;
    }

    &[disabled] {
      background-color: var(--color-neutral-background);
      color: var(--color-neutral-content-subtlest);
    }

    &_type_text {
      padding: 0 2px !important;
    }

    &.dropdown__trigger {
      padding: 0 6px 0 2px;
    }
  }
}

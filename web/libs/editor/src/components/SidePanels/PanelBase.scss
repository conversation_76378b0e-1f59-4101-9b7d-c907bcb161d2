@import "../../assets/styles/global";

.panel {
  --header-height: 24px;
  --header-border-radius: 0;
  --header-background: var(--color-neutral-surface);

  height: 100%;
  z-index: 10;
  background-color: var(--color-neutral-background);
  pointer-events: all;

  &_newLabelingUI {
    --header-background-new: var(--color-neutral-surface-inset);
    --icon-color: var(--color-neutral-icon);
  }

  .panel {
    &__header {
      background: var(--header-background-new);
      color: var(--icon-color);

      &:hover {
        --icon-color: var(--color-primary-icon);
        --header-background-new: var(--color-primary-surface-content);
      }
    }
  }

  &.panel {
    &_alignment {
      &_left,
      &_right {
        & .panel__header {
          flex-direction: row;
          padding: 4px;
          padding-left: 9px;
        }
      }
    }

    &__header {
      color: var(--header-background-new);
      padding-left: 24px;
    }

    &_hidden:not(&.panel_detached) {
      margin: 0;
      border-radius: 0;
      height: 100%;

      & .panel {
        &__header {
          height: 100%;
          justify-content: space-between;
          padding: 0 4px;

          --header-border-radius: 0;
        }
      }
    }

    &_hidden &__header {
      padding: 0;
      width: 100%;
      height: 100%;
      cursor: pointer;
      line-height: normal;
      justify-content: center;
      border-radius: var(--header-border-radius);
    }
  }

  &_alignment {
    &_left {
      border-right: 1px solid var(--color-neutral-border);

      --header-border-radius: 0 4px 4px 0;

      & .panel__header {
        text-align: left;
        flex-direction: row;
        padding: 4px 0 4px 12px;
      }
    }

    &_right {
      border-left: 1px solid var(--color-neutral-border);

      --header-border-radius: 4px 0 0 4px;

      & .panel__header {
        text-align: right;
        flex-direction: row-reverse;
        padding: 4px 12px 4px 0;
      }
    }
  }

  &_detached {
    top: 0;
    left: 0;
    position: absolute;
    border: 1px solid var(--color-neutral-border);
    box-shadow: 0 6px 10px 4px rgb(38 38 38 / 15%), 0 2px 3px rgb(38 38 38 / 30%);

    & .panel_hidden:not(.panel_detached) {
      margin: 0;
    }
  }

  &_hidden:not(.panel_detached) {
    top: 0;
    width: 22px;
    border: none;
    margin: 8px 0;
    background: none;
    height: calc(100% - 16px);

    --header-background: var(--sand_200);
    --icon-color: var(--sand_500);

    &:hover {
      --icon-color: var(--color-primary-icon);
      --header-background: var(--color-primary-surface-content);
    }
  }

  &__header {
    top: 0;
    height: var(--header-height);
    z-index: 2;
    display: flex;
    cursor: move;
    font-size: 13px;
    font-weight: 700;
    line-height: 16px;
    color: var(--color-neutral-content-subtler);
    user-select: none;
    position: sticky;
    align-items: center;
    justify-content: space-between;
    background-color: var(--color-neutral-surface-inset);
  }

  &_hidden:not(.panel_detached) &__header {
    padding: 0;
    width: 100%;
    height: 100%;
    cursor: pointer;
    line-height: normal;
    justify-content: center;
    border-radius: var(--header-border-radius);
  }

  &__toggle {
    width: 24px;
    height: 24px;
    display: flex;
    position: relative;
    align-items: center;
    justify-content: center;
    color: var(--grape_500);

    svg {
      display: block;
    }

    &:hover {
      background-color: var(--color-primary-surface-content);
    }
  }

  &:not(.panel_detached).panel_hidden .panel__toggle {
    pointer-events: none;
  }

  &_hidden:not(.panel_detached) .panel__toggle {
    background-color: transparent;
  }

  &:not(.panel_hidden) .panel__toggle:hover,
  .panel_hidden.panel_detached .panel__toggle:hover,
  .panel_hidden:not(.panel_detached) .panel__header:hover .panel__toggle {
    &::before {
      color: #fff;
      font-size: 16px;
      font-weight: 500;
      padding: 4px 16px;
      position: absolute;
      white-space: nowrap;
      border-radius: 4px;
      background-color: #262626;
      content: attr(data-tooltip);
    }
  }

  &_hidden:not(.panel_detached).panel_alignment_left .panel__toggle,
  &_hidden:not(.panel_detached).panel_alignment_right .panel__toggle {
    &::before {
      top: 50%;
      transform: translate(0, -50%);
    }
  }

  &_hidden:not(.panel_detached).panel_alignment_right .panel__toggle::before {
    right: calc(100% + 4px);
  }

  &_hidden:not(.panel_detached).panel_alignment_left .panel__toggle {
    &::before {
      left: calc(100% + 4px);
    }
  }

  &:not(.panel_hidden, .panel_detached) .panel__toggle {
    &::before {
      left: 50%;
      top: calc(100% + 4px);
      transform: translate(-50%, 0);
    }
  }

  &_detached &__toggle {
    &::before {
      left: 50%;
      bottom: calc(100% + 4px);
      transform: translate(-50%, 0);
    }
  }

  &__content {
    width: 100%;
    height: 100%;

    & .panel_resizing {
      pointer-events: none;
    }
  }

  &__body {
    z-index: 1;
    overflow: hidden auto;
    height: calc(100% - var(--header-height));
    position: relative;

    @include styled-scrollbars;
  }

  &__resizer {
    --size: 12px;
    --visual-size: 2px;

    z-index: 10;
    position: absolute;
    background-color: transparent;
    user-select: none;

    &[data-resize="top"],
    &[data-resize="bottom"] {
      left: calc(var(--size) / 2);
      height: var(--size);
      width: calc(100% - var(--size));
      cursor: ns-resize;
    }

    &[data-resize="top"] {
      top: 0;
      transform: translate(0, -50%);
    }

    &[data-resize="bottom"] {
      bottom: 0;
      transform: translate(0, 50%);
    }

    &[data-resize="left"],
    &[data-resize="right"] {
      top: calc(var(--size) / 2);
      width: var(--size);
      height: calc(100% - var(--size));
      cursor: ew-resize;
    }

    &[data-resize="left"] {
      left: 0;
      transform: translate(-50%, 0);
    }

    &[data-resize="right"] {
      right: 0;
      transform: translate(50%, 0);
    }

    &[data-resize="top-left"],
    &[data-resize="top-right"],
    &[data-resize="bottom-left"],
    &[data-resize="bottom-right"] {
      width: var(--size);
      height: var(--size);
    }

    &[data-resize="top-left"],
    &[data-resize="top-right"] {
      top: 0;
    }

    &[data-resize="bottom-left"],
    &[data-resize="bottom-right"] {
      bottom: 0;
    }

    &[data-resize="top-left"] {
      left: 0;
      transform: translate(-50%, -50%);
      cursor: nwse-resize;
    }

    &[data-resize="top-right"] {
      right: 0;
      transform: translate(50%, -50%);
      cursor: nesw-resize;
    }

    &[data-resize="bottom-left"] {
      left: 0;
      transform: translate(-50%, 50%);
      cursor: nesw-resize;
    }

    &[data-resize="bottom-right"] {
      right: 0;
      transform: translate(50%, 50%);
      cursor: nwse-resize;
    }

    &::before {
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      content: '';
      width: 6px;
      height: 6px;
      display: none;
      position: absolute;
      background-color: var(--grape_500);
      border-radius: 100%;
    }

    &[data-resize="top"]::before,
    &[data-resize="bottom"]::before,
    &[data-resize="left"]::before,
    &[data-resize="right"]::before {
      border-radius: 20px;
    }

    &[data-resize="top"]::before,
    &[data-resize="bottom"]::before {
      top: 50%;
      height: var(--visual-size);
      transform: translate(0, -50%);
      width: calc(100% + var(--size));
      left: calc(var(--size) / 2 * -1);
    }

    &[data-resize="left"]::before,
    &[data-resize="right"]::before {
      left: 50%;
      width: var(--visual-size);
      transform: translate(-50%, 0);
      height: calc(100% + var(--size));
      top: calc(var(--size) / 2 * -1);
    }

    &:hover::before,
    &_drag::before {
      display: block;
    }
  }
}

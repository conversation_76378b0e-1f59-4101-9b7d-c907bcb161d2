.flyoutmenu {
  width: 32px;
  height: 32px;
  margin: 4px;
  display: flex;
  cursor: pointer;
  position: relative;
  align-items: center;
  justify-content: center;
  user-select: none;
  z-index: 1;
  background: none;
  border: none;
  padding: 0;

  --text-color: var(--color-neutral-content-subtler);
  --text-color-hover: var(--color-neutral-content);

  &:hover {
    z-index: 100;
  }

  &__icon {
    width: 32px;
    height: 32px;
    opacity: 0.5;
    color: var(--text-color-hover);

    svg {
      width: 100%;
      height: 100%;
    }

    &.isClicked {
      background: var(--color-primary-emphasis-subtle);
      box-shadow: inset 0 0 2px var(--color-primary-border-subtle);
      border-radius: 5px;
      opacity: 1;
    }
  }

  &_active, &:hover {
    & .flyoutmenu__icon {
      opacity: 1;
    }
  }

  &.hovered {
    z-index: 100;

    .tooltips {
      right: 100%;
    }
  }

  .tooltips {
    top: 50%;
    right: 100vw;
    font-size: 14px;
    font-weight: 500;
    user-select: none;
    position: absolute;
    box-sizing: border-box;
    margin-right: 10px;
    transform: translate3d(0, -50%, 0);
    border-radius: 5px;
    background-color: var(--color-neutral-background);
    box-shadow: 0 0 0 1px rgb(0 0 0 / 5%), 0 5px 10px rgb(0 0 0 / 10%);

    @media (width <= 1200px) {
      right: 1200px;
    }

    &__tooltip {
      font-size: 14px;
      font-weight: 500;

      &-body {
        display: flex;
        height: 40px;
        align-items: center;
        white-space: nowrap;
        padding: 0 14px;
        color: var(--text-color-hover);
        flex-direction: row;
        justify-content: space-between;
      }

      &_controlled {
        pointer-events: all;
      }

      &__controls {
        top: 50%;
        right: 100%;
        position: absolute;
        transform: translate3d(0, -50%, 0);

        &-body {
          display: flex;
          border-radius: 5px;
          background-color: var(--color-neutral-background);
          box-shadow: 0 0 0 1px rgb(0 0 0 / 5%), 0 5px 10px rgb(0 0 0 / 10%);
        }
      }
    }

    &_alignment_right {
      padding-right: 10px;
      flex-direction: row-reverse;

      & .tool_tooltip-body {
        flex-direction: row-reverse;
      }

      & .tool__controls {
        padding-right: 10px;
      }

      & .tool__shortcut {
        margin: 0 20px 0 0;
      }

      & .tool__label {
        padding-left: 8px;
        padding-right: 12px;
        flex-direction: row-reverse;
      }
    }

    &_alignment_left {
      flex-direction: row;
      padding-left: 10px;

      & .tool_tooltip-body {
        flex-direction: row;
      }

      & .tool__controls-body{
        padding-left: 10px;
      }

      & .tool__shortcut {
        margin: 0 0 0 20px;
      }

      & .tool__label {
        padding-left: 12px;
        padding-right: 8px;
        flex-direction: row;
      }
    }

    &_active {
      & .tool__tooltip {
        display: none;
      }

      & .tool__tooltip-body {
        color: var(--text-color-hover);
      }

      & .tool_expanded {
        border-radius: 5px;
        background-color: var(--color-neutral-background);
      }

      & .tool_smart {
        border-radius: 5px;
        background-color: var(--color-neutral-background);
      }
    }

    &:hover {
      & .tool__tooltip {
        display: block;
      }

      & .tool__tooltip-body {
        color: var(--text-color-hover);
      }
    }

    &__shortcut {
      display: flex;
      font-size: 12px;
      font-weight: bold;
      color: var(--color-neutral-content-subtler);
      margin: 0 0 0 20px;
    }

    &_active, &:hover {
      & .tool__shortcut {
        opacity: 1;
      }

      & .tool__label {
        color: var(--text-color-hover);
      }
    }

    &_expanded &_alignment_right {
      & .tool__shortcut {
        margin: 0 20px 0 0;
      }
    }

    &_expanded &_alignment_left {
      & .tool__shortcut {
        margin: 0 0 0 20px;
      }
    }

    &__key {
      height: 24px;
      min-width: 24px;
      display: flex;
      align-items: center;
      justify-content: center;
      background: var(--color-neutral-surface);
      text-transform: uppercase;
      padding: 3px var(--spacing-tight) 5px;
      border-radius: var(--corner-radius-smaller);
      border: 1px solid var(--color-neutral-border);
      box-shadow: 0 2px 0 var(--color-neutral-background) inset, 0 -3px 0.5px 0 rgba(var(--color-neutral-shadow-raw) / calc( var(--shadow-intensity) * 10% )) inset, 0 2px 1px rgba(var(--color-neutral-shadow-raw) / calc( var(--shadow-intensity) * 4% ));
      position: relative;
      color: var(--color-neutral-content-subtler);
      margin-left: 4px;
    }

    &__label {
      flex: 1;
      display: flex;
      align-items: center;
      font-weight: 500;
      justify-content: space-between;
      color: var(--text-color-hover);
    }

    &__smart {
      display: flex;
    }

    &_expanded {
      width: calc(100% - 8px);
    }

    &_smart {
      --text-color: var(--plum_200);
      --text-color-hover: var(--plum_400);
    }
  }
}

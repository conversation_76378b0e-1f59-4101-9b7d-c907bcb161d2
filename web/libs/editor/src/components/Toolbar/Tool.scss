.tool {
  width: 32px;
  height: 32px;
  margin: 4px;
  display: flex;
  cursor: pointer;
  position: relative;
  align-items: center;
  justify-content: center;
  user-select: none;
  z-index: 1;
  background: none;
  border: none;
  padding: 0;

  --text-color: var(--color-neutral-content-subtle);
  --text-color-hover: var(--color-neutral-content);

  &:hover {
    z-index: 100;

    & .tool__tooltip {
      display: block;
    }

    & .tool__tooltip-body {
      color: var(--text-color-hover);
    }
  }

  &__icon {
    width: 32px;
    height: 32px;
    opacity: 0.5;
    color: var(--text-color-hover);
    border-radius: var(--corner-radius-smaller);
    transition: all 150ms ease-out;

    svg {
      width: 100%;
      height: 100%;
    }
  }

  &:not(&_active):hover {
    & .tool__icon {
      opacity: 1;
      box-shadow: inset 0 0 2px var(--color-primary-border-subtle);
      background: var(--color-primary-emphasis-subtle);
      color: var(--text-color-hover);
    }

    & .tool__label {
      color: var(--text-color-hover);
    }

    & .tool__shortcut {
      opacity: 1;
    }
  }


  &_active {
    & .tool__tooltip {
      display: none;
    }

    & .tool__tooltip-body {
      color: var(--text-color-hover);
    }

    & .tool_expanded {
      border-radius: 5px;
      background-color: var(--color-neutral-background);
    }

    & > .tool__icon {
      opacity: 1;
      box-shadow: inset 0 0 2px var(--color-primary-border-subtle);
      background: var(--color-primary-emphasis-subtle);
      color: var(--text-color-hover);
    }

    .tool_smart {
      border-radius: 5px;
    }

    & .tool__label {
      color: var(--text-color-hover);
    }

    & .tool__shortcut {
      opacity: 1;
    }

    > &__icon {
      background: var(--color-neutral-surface-active);
      box-shadow: inset 0 0 0 1px var(--color-primary-border-subtle);
      border-radius: 5px;
    }

  }

  &_smart {
    --text-color: var(--color-accent-plum-base);
    --text-color-hover: var(--color-accent-plum-base);
  }

  &__tooltip {
    top: 50%;
    display: none;
    right: 100%;
    font-size: 14px;
    font-weight: 500;
    user-select: none;
    position: absolute;
    box-sizing: border-box;
    padding-right: 10px;
    transform: translate3d(0, -50%, 0);
    pointer-events: none;

    &-body {
      display: flex;
      height: 40px;
      align-items: center;
      border-radius: 5px;
      white-space: nowrap;
      padding: 0 14px;
      color: var(--color-neutral-content);
      background-color: var(--color-neutral-background);
      box-shadow: 0 0 0 1px var(--color-neutral-border), 0 5px 10px rgba(var(--color-neutral-shadow-raw) / 16%);
    }

    &_controlled {
      pointer-events: all;
    }
  }

  &_alignment_right {
    flex-direction: row-reverse;

    & .tool__tooltip-body {
      flex-direction: row-reverse;
    }

    & .tool__controls {
      padding-right: 10px;
    }

    & .tool__shortcut {
      margin: 0 20px 0 0;
    }

    & .tool__label {
      padding-left: 8px;
      padding-right: 12px;
      flex-direction: row-reverse;
    }
  }

  &_alignment_left {
    flex-direction: row;

    & .tool__tooltip-body {
      flex-direction: row;
    }

    & .tool__shortcut {
      margin: 0 0 0 20px;
    }

    & .tool__label {
      padding-left: 12px;
      padding-right: 8px;
      flex-direction: row;
    }
  }

  &__controls {
    top: 50%;
    right: 100%;
    position: absolute;
    transform: translate3d(0, -50%, 0);

    &-body {
      display: flex;
      border-radius: 5px;
      background-color: var(--color-neutral-background);
      box-shadow: 0 0 0 1px rgb(0 0 0 / 5%), 0 5px 10px rgb(0 0 0 / 10%);
    }
  }

  &__shortcut {
    opacity: 0.6;
    display: flex;
    font-size: 12px;
    font-weight: bold;
    color: var(--color-neutral-content-subtler);
  }

  &_expanded &_alignment_right {
    & .tool__shortcut {
      margin: 0 20px 0 0;
    }
  }

  &_expanded &_alignment_left {
    & .tool__shortcut {
      margin: 0 0 0 20px;
    }
  }

  &__key {
    height: 24px;
    min-width: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--color-neutral-surface);
    text-transform: uppercase;
    padding: 3px var(--spacing-tight) 5px;
    border-radius: var(--corner-radius-smaller);
    border: 1px solid var(--color-neutral-border);
    box-shadow: 0 4px 2px var(--color-neutral-background) inset, 0 -3px 0.5px 0 rgba(var(--color-neutral-shadow-raw) / calc( var(--shadow-intensity) * 10% )) inset, 0 2px 1px rgba(var(--color-neutral-shadow-raw) / calc( var(--shadow-intensity) * 4% ));
    position: relative;
    color: var(--color-neutral-content-subtler);
  }

  &__key + &__key {
    margin-left: 4px;
  }

  &__label {
    flex: 1;
    display: flex;
    align-items: center;
    font-weight: 500;
    justify-content: space-between;
    color: var(--text-color);
  }

  &__smart {
    display: flex;
  }

  &_expanded {
    width: calc(100% - 8px);
  }
}

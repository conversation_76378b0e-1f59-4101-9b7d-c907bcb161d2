.toolbar {
  width: 42px;
  background: var(--color-neutral-background);
  border: 1px solid var(--color-neutral-border);
  box-shadow: 0 0 0 1px rgb(0 0 0 / 5%), 0 5px 10px rgb(0 0 0 / 10%);
  border-radius: 7px;
  position: sticky;
  top: 70px;
  margin-top: 50px;

  &__group ~ &__group:not(:last-child) {
    margin-top: 4px;
    border-top: 2px solid var(--color-neutral-border);
  }

  &_expanded {
    width: auto;
    min-width: 210px;
    display: flex;
    flex-direction: column;
  }
}

.current-task {
  padding: 0 8px 0 16px;
  display: flex;
  align-content: center;
  align-items: center;

  &_with-history {
    display: grid;
    grid-template: "id buttons" 1fr "id buttons" 1fr / 1fr min-content;
    height: 100%;
    width: 220px;
  }

  &__task-id {
    grid-area: id;
    color: var(--color-neutral-content);
    font-size: 14px;
    user-select: text;

    &::before {
      content: '#';
    }
  }

  &__task-count {
    grid-area: counter;
    color: var(--sand_500);
    font-size: 11px;
    user-select: none;
  }

  &__history-controls {
    display: flex;
    grid-area: buttons;
  }

  &__prevnext {
    width: 32px;
    height: 32px;
    place-self: center center;

    &::before {
      top: 50%;
      left: 50%;
      content: '';
      width: 10px;
      height: 10px;
      display: block;
      position: absolute;
      border: 2px solid var(--sand_900);
      border-bottom: none;
      border-right: none;
      margin-top: -3px;
      transform: translate3d(-50%, -50%, 0) rotate(225deg);
    }

    &_next {
      grid-area: n;
      transform: rotate(-90deg);
    }

    &_next&_postpone::before {
      border-color: var(--color-primary-border-subtle);
    }

    &_prev {
      grid-area: p;
      transform: rotate(90deg);
    }

    &_hidden {
      display: none;
    }

    &_disabled::before {
      border: 2px solid var(--sand_300);
      border-bottom: none;
      border-right: none;
    }
  }
}

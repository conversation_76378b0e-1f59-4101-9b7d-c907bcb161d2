.bottombar {
  .controls {
    flex: 1;
    height: 100%;
    display: grid;
    grid-auto-columns: 1fr;
    column-gap: 8px;
    grid-auto-flow: column;
    justify-content: flex-end;
    padding-right: 8px;
    align-items: center;

    & .button {
      width: 160px;
      height: 40px;
      font-size: 16px;
      line-height: 24px;
      border-color: var(--color-neutral-border);

      &:hover:not(.button_look_primary) {
        background-color: var(--color-neutral-surface);
        border-color: var(--color-neutral-border-bold);
      }

      &_look_danger {
        border-color: var(--color-neutral-border);
      }
    }

    .submit {
      border-radius: 4px;
      background: var(--color-primary-surface);
      box-shadow: 0 4px 4px 0 rgb(var(--color-neutral-shadow-raw) / 25%);
      display: flex;
      width: 160px;
      height: 40px;
      justify-content: center;
      align-items: center;
      color: var(--color-primary-surface-content);
      text-align: center;
      font-size: 16px;
      font-style: normal;
      font-weight: 500;
      line-height: 24px;
      letter-spacing: 0.15px;
      flex-direction: row-reverse;
      padding: 0;
      cursor: pointer;
      transition: all 150ms ease-out;

      &:hover {
        background-color: var(--color-primary-surface-hover);
        border-color: var(--color-primary-border-bold);
      }

      &_disabled {
        color: var(--color-neutral-content-subtlest);
        background-color: var(--color-neutral-background);
        border: 1px solid var(--color-neutral-border);
        box-shadow: none;

        &:hover {
          background-color: var(--color-neutral-background);
          border-color: var(--color-neutral-border);
          color: var(--color-neutral-content-subtlest);
          cursor: not-allowed;
        }

      }

      &__has_icon {
        justify-content: space-between;
      }

      span {
        width: 100%;
      }

      &__icon {
        display: flex;
        max-width: 40px;
        height: 100%;
        justify-content: center;
        align-items: center;
        align-self: stretch;
        border-left: 1px solid rgb(255 255 255 / 16%);

        div {
          width: 100%;
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
        }

        svg {
          pointer-events: none;
          transform: rotate(180deg) scale(1.1);
        }
      }
    }

    &__skipped-info {
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: 500;
      font-size: 16px;

      svg {
        margin: 0 8px 0 4px;
      }
    }
  }

  .action-dialog {
    height: 278px;
    width: 319px;
    padding: 12px 16px;
    display: flex;
    flex-direction: column;

    &__input-title {
      margin-bottom: 4px;
    }

    &__input {
      resize: none;
      flex: 1 1 auto;
      margin-bottom: 16px;
      background-color: var(--color-neutral-surface);
      font-size: 16px;
    }

    &__footer {
      display: flex;
      flex-direction: row;
      justify-content: flex-end;
    }
  }
}

.submit-option {
  padding: 12px 24px;
  display: flex;
  align-items: center;
  align-self: stretch;
  background: var(--color-neutral-background);
  color: var(--color-neutral-content);
  text-overflow: ellipsis;
  white-space: nowrap;
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 24px;
  box-shadow: 0 1px 3px 1px rgb(38 38 38 / 15%), 0 1px 2px 0 rgb(38 38 38 / 30%);


  &::before {
    content: '';
    z-index: -1;
    width: calc(100% - 16px);
    height: calc(100% - 16px);
    position: absolute;
    left: 0;
    top: 0;
    margin: 8px;
    border-radius: 4px;
    opacity: 0;
    background: var(--color-primary-emphasis-subtle);
    pointer-events: none;
    transition: all 150ms ease-out;
  }

  &:hover::before {
    opacity: 1;
  }
}

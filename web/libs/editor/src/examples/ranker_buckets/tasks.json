[{"data": {"items": [{"id": "blog", "title": "10 tips to write a better function", "body": "There is nothing worse than being left in the lurch when it comes to writing a function!"}, {"id": "mdn", "title": "Arrow function expressions", "body": "An arrow function expression is a compact alternative to a traditional function"}, {"id": "wiki", "title": "<PERSON> (computer science)", "body": "In computer science, arrows or bolts are a type class..."}]}, "predictions": []}]
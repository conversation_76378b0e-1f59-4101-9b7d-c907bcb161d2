<View>
  <Text name="text" value="$reviewText" valueType="text" />
  <Taxonomy name="tax" toName="text">
    <Choice value="One" />
    <Choice value="Two" />
    <Choice value="Three">
      <Choice value="Sub" />
      <Choice value="With" />
      <Choice value="Tuna" />
    </Choice>
  </Taxonomy>
  <Choices name="sentiment" toName="text" showInLine="true">
    <Choice value="Positive" />
    <Choice value="Negative" />
    <Choice value="Neutral" />
  </Choices>
  <Choices name="ch" toName="text"
    choice="single" showInLine="true"
    visibleWhen="choice-selected"
  >
    <Choice value="Descriptive" />
    <Choice value="Emotional" />
  </Choices>
</View>
@import "./antd-no-reset";
@import "./variables";
@import "./mixins";
@import "./functions";

.root {
  font-size: 14px;
  font-variant: tabular-nums;
  font-feature-settings: "tnum";
  line-height: 1.5715;
}

.ant-divider-horizontal.ant-divider-with-text-center::before,
.ant-divider-horizontal.ant-divider-with-text-left::before,
.ant-divider-horizontal.ant-divider-with-text-right::before,
.ant-divider-horizontal.ant-divider-with-text-center::after,
.ant-divider-horizontal.ant-divider-with-text-left::after,
.ant-divider-horizontal.ant-divider-with-text-right::after {
  border-top-color: var(--color-neutral-border) !important;
}

.renderall {
  display: flex;
  flex-wrap: wrap;
  width: 100%;
}

.renderall > div {
  width: 49%;
  margin-right: 1%;
}

.fade {
  opacity: 1;
  animation-name: fade-in-opacity;
  animation-iteration-count: 1;
  animation-timing-function: ease-in;
  animation-duration: 1s;
}

.htx-highlight > .htx-highlight {
  padding: 2px;
}

// .htx-no-highlight {
//     display: none;
// }

.htx-no-label::after {
  display: none;
}

.htx-highlight {
  // border: 1px dashed #00aeff;
}

.htx-highlight-last {
  // position: relative;
}

.htx-highlight-last::after {
  margin-left: 2px;

  // top: -0.3em;
  // margin-right: 2px;
  // position: relative;
  // color: #1f104d;
  // font-size: 75%;
  // font-weight: bold;
}

.htx-yoyo::after {
  content: "Yoyo";
}

@keyframes fade-in-opacity {
  0% {
    opacity: 0;
  }

  100% {
    opacity: 1;
  }
}

.ant-list-item-action {
  margin-left: 10px !important;
}

.ant-modal-confirm-error ul {
  padding-left: 1.5em;
}

.react-dropdown-tree-select .node > .toggle {
  font-family: var(--font-mono);
  font-style: normal;
  color: initial;
  cursor: pointer;
}

// when node is disabled (when leafsOnly enabled) allow to collapse it by click anywhere
// achieve this with enormously growing button to the right covering all the text
.react-dropdown-tree-select .node.disabled > .toggle {
  padding-right: 100%;
  margin-right: -100%;
  position: relative;
  z-index: 1;
  user-select: none;
}

.react-dropdown-tree-select .node.disabled > label {
  color: initial;
  cursor: default;
}

.react-dropdown-tree-select .node.disabled > label > input {
  opacity: 0;
}

[data-color-scheme="dark"] input[type="number"] {
  color-scheme: dark;
}

.number input {
  background-color: var(--color-neutral-background);
  border: 1px solid var(--color-neutral-border);
  color: var(--color-neutral-content);
  border-radius: var(--corner-radius-small);
  padding: var(--spacing-tight) var(--spacing-base);
  transition: all 150ms ease-out;

  &[type="number"]:hover {
    border-color: var(--color-neutral-border-bold);
  }

  &[type="number"]:focus {
    box-shadow: 0 0 0 4px var(--color-primary-focus-outline);
    border-color: var(--color-neutral-border-bold);
    outline: none;
  }

  &[type="range"] {
    accent-color: var(--color-primary-surface);
  }

  &[type="range"]:hover {
    accent-color: var(--color-primary-surface-hover);
  }
  
  /* Removes default focus */
  &[type="range"]:focus {
    outline: none;
  }
  
  // /* slider thumb */
  &[type="range"]::-webkit-slider-thumb {
    appearance: none;
    margin-top: 0;
    transition: all 150ms ease-out;
    border-radius: 50%;
  }
  
  &[type="range"]:hover::-webkit-slider-thumb {
    background-color: var(--color-primary-surface-hover);
  }
  
  &[type="range"]:focus::-webkit-slider-thumb {
    background-color: var(--color-primary-surface-hover);
    outline: 4px solid var(--color-primary-focus-outline);
    border: 1px solid var(--color-primary-border-bold);
  }
}

:global(.htx-datetime) {
  display: flex;
  flex-direction: row;
  gap: var(--spacing-base);
  padding: 0 var(--spacing-tight);
}

:global(.htx-datetime input) {
  background-color: var(--color-neutral-background);
  border: 1px solid var(--color-neutral-border);
  color: var(--color-neutral-content);
  border-radius: var(--corner-radius-small);
  padding: var(--spacing-tighter) var(--spacing-tight) var(--spacing-tighter) var(--spacing-base);
  transition: all 150ms ease-out;
}

:global(.htx-datetime input:hover) {
  border: 1px solid var(--color-neutral-border-bold);
}

:global(.htx-datetime input:focus) {
  border: 1px solid var(--color-neutral-border-bold);
  box-shadow: 0 0 0 4px var(--color-primary-focus-outline);
  outline: none;
}

:global(.htx-datetime input::-webkit-calendar-picker-indicator) {
  opacity: 0.5;
}

:global([data-color-scheme="dark"] .htx-datetime input[type="time"]) {
  color-scheme: dark;
}

// stub file to keep TextRegion docs

/**
 * @example
 * {
 *   "value": {
 *     "start": 2,
 *     "end": 81,
 *     "labels": ["Car"]
 *   }
 * }
 * @typedef {Object} TextRegionResult
 * @property {Object} value
 * @property {string} value.start position of the start of the region in characters
 * @property {string} value.end position of the end of the region in characters
 * @property {string} [value.text] text content of the region, can be skipped
 */

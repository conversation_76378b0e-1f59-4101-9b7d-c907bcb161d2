// stub file to keep HyperTextRegion docs

/**
 * @example
 * {
 *   "value": {
 *     "start": "/div[1]/p[2]/text()[1]",
 *     "end": "/div[1]/p[4]/text()[3]",
 *     "startOffset": 2,
 *     "endOffset": 81,
 *     "hypertextlabels": ["Car"]
 *   }
 * }
 * @typedef {Object} HyperTextRegionResult
 * @property {Object} value
 * @property {string} value.start xpath of the container where the region starts (xpath)
 * @property {string} value.end xpath of the container where the region ends (xpath)
 * @property {number} value.startOffset offset within start container
 * @property {number} value.endOffset offset within end container
 * @property {string} [value.text] text content of the region, can be skipped
 */

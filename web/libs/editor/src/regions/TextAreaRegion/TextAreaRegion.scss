.mark {
  background-color: var(--color-neutral-surface);
  border: 1px solid var(--color-neutral-border);
  border-radius: var(--corner-radius-small);
  margin: 0 var(--spacing-tight) 0 0;
  padding: var(--spacing-tight) var(--spacing-base);
  white-space: pre-line;
  box-shadow: 0 4px 12px 0 var(--black_4), 0 1px 2px 0 var(--black_10);
  flex-grow: 1;
  color: var(--color-neutral-content);

  &:global(.ant-typography) {
    color: var(--color-neutral-content);
  }

  &.ant-typography {
    margin-bottom: 0;
  }

  &.selected {
    border: 1px solid var(--color-primary-border);
    background-color: var(--color-primary-emphasis);
  }

  &.highlighted {
    border: 1px dashed var(--color-primary-border);
    background-color: var(--color-primary-emphasis-subtle);
  }

  &.relation {
    cursor: crosshair;
  }
}

.row {
  display: flex;
  margin-bottom: var(--spacing-base);
  flex-wrap: nowrap;
  align-items: center;
  gap: var(--spacing-tight);

  .button {
    display: flex;
    flex: 0 0 32px;

    &:hover {
      background-color: var(--color-neutral-surface);
    }

    &_look {
      &_danger {
        &:hover {
          background-color: var(--color-negative-emphasis-subtle);
        }
      }
    }
  }

  // fix antd styles to resize textarea container and fix paddings
  div[class~="ant-typography-edit-content"] {
    flex-grow: 1;
    left: -1px;
    padding: 0;
    margin-top: -1px;
    margin-right: 0;
    background-color: var(--color-neutral-surface);
    border-color: var(--color-neutral-border);

    textarea {
      // the same as in .mark, its padding set to 0
      padding: var(--spacing-tight) var(--spacing-base);
    }
  }
}

:global(.ant-typography-edit-content .ant-input) {
  background-color: var(--color-neutral-background);
  color: var(--color-neutral-content);
  border-color: var(--color-neutral-border);
}

:global(.ant-typography-edit-content .ant-input:focus) {
  border-color: var(--color-neutral-border-bold);
  box-shadow: 0 0 0 4px var(--color-primary-focus-outline);
}

:global(.ant-typography-edit-content .ant-input:focus-visible) {
  border-color: var(--color-neutral-border-bold);
  box-shadow: 0 0 0 4px var(--color-primary-focus-outline);
}



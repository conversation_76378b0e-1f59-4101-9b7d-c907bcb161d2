export const defaultStyle = {
  fillcolor: "#666",
  opacity: 0.2,
  strokecolor: "#666",
  strokewidth: 1,
};

export default {
  FILL_COLOR: "",
  STROKE_COLOR: "",
  STROKE_WIDTH: 1,

  // labels
  LABEL_BACKGROUND: "#36B37E",
  EMPTY_LABEL: "blank",

  RELATION_BACKGROUND: "#fff",

  // label on image
  SHOW_LABEL_FILL: "white",
  SHOW_LABEL_BACKGROUND: "black",

  // when region gets highlighted styling
  HIGHLIGHTED_STROKE_COLOR: "red",
  HIGHLIGHTED_STROKE_WIDTH: 2,
  HIGHLIGHTED_CSS_BORDER: "1px dashed #00aeff",

  // when using dynamic suggestions
  SUGGESTION_STROKE_WIDTH: 4,

  // cursors
  DEFAULT_CURSOR: "default",
  CHOOSE_CURSOR: "pointer",
  POINTER_CURSOR: "pointer",
  MOVE_CURSOR: "hand",
  LINKING_MODE_CURSOR: "crosshair",

  // images
  BRIGHTNESS_VALUE: 100,
  BRIGHTNESS_MAX: 400,

  CONTRAST_VALUE: 100,
  CONTRAST_MAX: 400,
};

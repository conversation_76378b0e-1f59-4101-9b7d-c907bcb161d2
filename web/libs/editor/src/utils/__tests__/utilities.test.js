/* global it, describe, expect, test */
import { emailFromCreatedBy, toArray, getUrl, isString, isStringEmpty, isStringJSON, toTimeString } from "../utilities";

describe("Helper function emailFromCreatedBy", () => {
  expect(emailFromCreatedBy("<EMAIL>, 12")).toBe("<EMAIL>");
  // empty username, not a rare case
  expect(emailFromCreatedBy(" <EMAIL>, 12")).toBe("<EMAIL>");
  expect(emailFromCreatedBy("usrnm <EMAIL>, 12")).toBe("<EMAIL>");
  // first and last name
  expect(emailFromCreatedBy("<NAME_EMAIL>, 12")).toBe("<EMAIL>");
  // complex case
  expect(emailFromCreatedBy("Ab.C D@E.F <EMAIL>, 12")).toBe("<EMAIL>");
  // just a email, should not be a real case though
  expect(emailFromCreatedBy("<EMAIL>")).toBe("<EMAIL>");
});

describe("Helper function toArray, converting any value to array, skipping undefined values", () => {
  test("Empty", () => {
    expect(toArray()).toEqual([]);
  });

  test("Single value", () => {
    expect(toArray("value")).toEqual(["value"]);
  });

  test("Zero", () => {
    expect(toArray(0)).toEqual([0]);
  });

  test("Array", () => {
    expect(toArray(["value"])).toEqual(["value"]);
  });
});

/**
 * isString
 */
it("Function isString works", () => {
  expect(isString("value")).toBeTruthy();
});

/**
 * isStringEmpty
 */
describe("Helper function isStringEmpty", () => {
  test("Empty", () => {
    expect(isStringEmpty("")).toBeTruthy();
  });

  test("Not string", () => {
    expect(isStringEmpty(123)).toBeFalsy();
  });

  test("Not empty", () => {
    expect(isStringEmpty("value")).toBeFalsy();
  });
});

/**
 * isStringJSON
 */
describe("Helper function isStrinJSON", () => {
  test("JSON", () => {
    expect(isStringJSON('{"test": "value"}')).toBeTruthy();
  });

  test("String isn't JSON", () => {
    expect(isStringJSON("value")).toBeFalsy();
  });

  test("Number", () => {
    expect(isStringJSON(1)).toBeFalsy();
  });

  test("Null", () => {
    expect(isStringJSON(null)).toBeFalsy();
  });
});

/**
 * getUrl
 */
describe("Helper function getUrl", () => {
  test("Correct https", () => {
    expect(getUrl(0, "https://heartex.net testing value")).toBe("https://heartex.net");
  });

  test("Correct http", () => {
    expect(getUrl(0, "http://heartex.net testing value")).toBe("http://heartex.net");
  });

  test("Correct wwww", () => {
    expect(getUrl(0, "www.heartex.net testing value")).toBe("www.heartex.net");
  });

  test("Not correct", () => {
    expect(getUrl(2, "https://heartex.net testing value")).toBe("");
  });
});

/**
 * toTimeString
 */
describe("Helper function toTimeString", () => {
  test("Correct", () => {
    expect(toTimeString(5000)).toBe("00:00:05");
  });
});

/* global jest, describe, expect, beforeEach, test */
import { debounce } from "../debounce";

jest.useFakeTimers();

describe("Debounce function", () => {
  let func;
  let debouncedFunc;

  beforeEach(() => {
    func = jest.fn();
    debouncedFunc = debounce(func, 1000);
  });

  test("Execute just once", () => {
    for (let i = 0; i < 100; i++) {
      debouncedFunc();
    }

    jest.runAllTimers();

    expect(func).toBeCalledTimes(1);
  });
});

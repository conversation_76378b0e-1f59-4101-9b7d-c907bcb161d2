.richtext {
  position: relative;

  &__container {
    overflow: auto;
    font-size: 16px;
    line-height: 26px;

    &_canResizeSpans {
      overflow: unset;
      font-size: 16px;
      line-height: 20px;
    }
  }

  &__orig-container {
    display: none !important;
  }

  &__work-container {
    display: none !important;
  }

  &__iframe {
    border: none;
    width: 100%;
  }

  &__orig-iframe {
    display: none !important;
  }

  &__work-iframe {
    display: none !important;
  }

  &__loading {
    position: absolute;
    inset: 0;
    display: flex;
    flex-flow: row nowrap;
    justify-content: center;
    align-items: center;
    background: var(--color-neutral-surface);
    font-size: 24px;
  }

  &__line {
    position: relative;
    display: inline-block;
    min-height: 1.2em;
    padding-left: 8px;
    cursor: auto;
  }

  &__container[data-linenumbers="enabled"] {
    padding-left: 3em;
    counter-reset: line-number;

    & .richtext__line {
      &::before {
        counter-increment: line-number;
        content: counter(line-number);
        position: absolute;
        top: 0;
        left: -3em;
        height: 100%;
        min-height: 2.2em;
        width: 3em;
        border-right: 1px solid var(--color-neutral-border);
        padding-right: 4px;
        font-size: 0.8em;
        line-height: 2em;
        text-align: right;
        z-index: 1;
      }

      &:last-child::before {
        min-height: 1em;
      }

      &:hover,
      &:hover::before {
        background: var(--color-neutral-surface);
      }

      &:empty {
        background: none;
      }
    }
  }

  :global(.htx-highlight) {
    // resize handles around selected region,
    // they look like popsicles, normal one at the beginning, upside down one at the end
    area {
      $handle-radius: 3px;
      $hitbox-width: 2px;

      position: absolute;
      width: 1px;

      // line-height of the text
      height: 1.25em;

      // hitbox
      padding: 0 $hitbox-width;
      cursor: col-resize;
      box-sizing: content-box;
      margin-left: -$hitbox-width - 1px;

      // popsicle stick
      &::before {
        position: absolute;
        content: '';
        width: 1px;
        height: 100%;
        background: var(--color-primary-content-subtle);
      }

      &:hover::before {
        width: 100%;
        left: 0;
      }

      // popsicle sphere
      &::after {
        content: '';
        position: absolute;
        width: $handle-radius * 2 + 1px;
        height: $handle-radius * 2 + 1px;
        background: var(--color-primary-content-subtle);
        top: -($handle-radius * 2 + 1px);
        left: $hitbox-width - $handle-radius;
        z-index: 100;
        border-radius: 2px;
      }

      &:global(.__resize_right) {
        // area at the end placed a little off for some reason
        margin-left: -$hitbox-width;
      }

      &:global(.__resize_right)::after {
        top: unset;
        bottom: -($handle-radius * 2 + 1px);
      }

      &:hover::after {
        outline: 1px solid var(--color-primary-border-subtle);
      }
    }
  }
}

:global(.htx-no-label)::after {
  display: none;
}

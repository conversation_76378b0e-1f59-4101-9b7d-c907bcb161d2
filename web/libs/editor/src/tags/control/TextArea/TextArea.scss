.textarea-tag {
  margin-bottom: 12px;

  &:last-child {
    margin-bottom: 0;
  }

  &__form {
    margin-bottom: 12px;

    &:last-child {
      margin-bottom: 0;
    }

    &_hidden {
      display: none;
    }
  }

  &__item {
    position: relative;
    margin-bottom: 12px;

    &:last-child {
      margin-bottom: 0;
    }

    button {
      width: auto !important;
    }
  }

  &__input {
    color: var(--color-neutral-content);
    background: transparent;
    font-size: 16px;
    line-height: 22px;
    height: 22px;
    padding: 0 24px 0 0;
    margin-bottom: 0;

    &.ant-input {
      border: none;
      box-shadow: none;
      outline: none;
    }
  }

  &__action {
    position: absolute;
    right: 0;
    top: 0;
    display: flex;
    justify-content: center;
    align-items: center;

    & :global(.anticon) {
      margin: 0 !important;
    }
  }

  &_outliner {
    --border-color: var(--color-neutral-border);
  }

  &_outliner &__item + &__item,
  &_outliner &__item + &__form,
  &_outliner &__form + &__item,
  &_outliner &__form + &__form {
    margin-top: 8px;
  }

  &_outliner &__input {
    display: block;
    font-size: 14px;
    line-height: normal;
    padding: 8px;
    box-sizing: border-box;
    border-radius: 4px;
    border: 1px solid var(--color-neutral-border);
    background-color: var(--color-neutral-surface);
    color: var(--color-neutral-content);


    &:hover {
      border: 1px solid var(--color-neutral-border-bold);
      background-color: var(--color-neutral-surface-hover);
    }

    &::placeholder {
      color: var(--color-neutral-content-subtler);
    }
  }

  &_outliner &__input:focus {
    box-sizing: border-box;
    border-color: var(--color-neutral-border-bold);
    background-color: var(--color-neutral-surface-hover);
    box-shadow: 0 0 0 4px var(--color-primary-focus-outline);
  }
}

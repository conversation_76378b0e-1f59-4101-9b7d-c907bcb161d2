.choice_leaf.choice_layout,
.choice {
  line-height: 40px;

  &_leaf.choice_layout {
    &_inline,
    &_horizontal {
      display: inline-block;
    }

    &_vertical {
      display: block;
      position: relative;
    }
  }

  &_notLeaf {
    line-height: 40px;
  }

  @at-root #{selector-append(&,_leaf,&,_hidden)},
  #{selector-append(&,_notLeaf,&,_hidden)} {
    display: none;
  }

  &__item {
    display: flex;
    flex-flow: row nowrap;

    &_notLeaf {
      border-radius: 4px;
      background: var(--sand_200);
      position: relative;
    }
  }

  &__checkbox {
    padding-left: 8px;
    flex: 100 0 1%;
  }

  &__toggle {
    color: var(--grape_500);
    display: flex;
    justify-content: center;
    align-items: center;
    flex-flow: column;
    width: 24px;
    margin: 0 8px;
    flex-shrink: 0;
    align-self: center;

    &:hover {
      color: var(--grape_500);
    }

    &_collapsed {
      transform: scaleY(-1);
    }
  }

  &__children {
    padding-left: 8px;

    &_collapsed {
      display: none;
    }
  }
}

body :global(.ant-checkbox-wrapper) {
  color: var(--color-neutral-content);
}

body :global(.ant-checkbox .ant-checkbox-inner) {
  background-color: var(--color-neutral-surface);
  border-color: var(--color-neutral-border) !important;
}

body :global(.ant-checkbox-checked .ant-checkbox-inner) {
  background-color: var(--color-primary-surface);
  border-color: var(--color-primary-surface);
}

body :global(.ant-checkbox-checked)::after {
  border: 1px solid var(--color-primary-surface);
}

:global(.ant-checkbox-wrapper span + span) {
  color: var(--color-neutral-content);
}

body :global(.ant-checkbox-disabled .ant-checkbox-inner) {
  background-color: var(--color-neutral-background);
}

body :global(.ant-checkbox-disabled.ant-checkbox-checked .ant-checkbox-inner::after) {
  border-color: var(--color-neutral-content-subtlest);
}

body :global(.ant-radio .ant-radio-inner) {
  border-color: var(--color-neutral-border);
  background-color: var(--color-neutral-surface);
  transition: all 150ms ease-out;
}

body :global(.ant-radio-wrapper) {
  color: var(--color-neutral-content);
}

body :global(.ant-radio:hover .ant-radio-inner) {
  border-color: var(--color-primary-border);
  background-color: var(--color-neutral-background);
}


body :global(.ant-radio .ant-radio-input:focus + .ant-radio-inner) {
  border-color: var(--color-primary-border);
  background-color: var(--color-neutral-background);
  box-shadow: 0 0 0 4px var(--color-primary-focus-outline);
}

body :global(.ant-radio-checked .ant-radio-inner) {
  border-color: var(--color-primary-surface);
}

body :global(.ant-radio-checked .ant-radio-inner::after) {
  background-color: var(--color-primary-surface);
}

body :global(.ant-radio-checked .ant-radio-input:focus + .ant-radio-inner) {
  border-color: var(--color-primary-border);
  background-color: var(--color-neutral-background);
  box-shadow: 0 0 0 4px var(--color-primary-focus-outline);
}

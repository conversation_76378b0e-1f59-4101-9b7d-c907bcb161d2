.space {
  display: grid;
  grid-gap: 16px;

  &_direction {
    &_horizontal {
      grid-auto-flow: column;
      align-items: center;
    }

    &_vertical {
      grid-auto-flow: row;
      justify-content: center;
      grid-auto-rows: max-content;
    }
  }

  &_direction_horizontal:not(.space_truncated) {
    grid-auto-columns: max-content;
  }

  &_spread {
    width: 100%;
    grid-gap: 0;
    justify-content: space-between;
  }

  &_align {
    &_start {
      justify-content: flex-start;
    }

    &_end {
      justify-content: flex-end;
    }
  }

  &_stretch.space_direction {
    &_horizontal {
      grid-auto-columns: 1fr;
      grid-auto-rows: 1fr;
    }
  }

  &_size {
    &_large {
      grid-gap: 32px;
    }

    &_medium {
      grid-gap: 16px;
    }

    &_small {
      grid-gap: 8px;
    }

    &_none {
      grid-gap: 0;
    }
  }

  &_collapsed {
    grid-gap: 0;
  }
}

.input:not([type='checkbox'], [type='radio']),
.textarea {
  height: 32px;
  width: 100%;
  background: var(--color-neutral-background);
  font-size: 14px;
  border: 1px solid var(--color-neutral-border);
  box-sizing: border-box;
  border-radius: 5px;
  padding: 0 16px;
  transition: box-shadow 150ms ease-out;
  box-shadow: inset 0 1px 4px rgba(var(--color-neutral-shadow-raw) / 12%);
}

.input_size_compact:not([type='checkbox'], [type='radio']) {
  height: 32px;
}

.input_size_small:not([type='checkbox'], [type='radio']) {
  height: 24px;
  font-size: 12px;
  padding: 0 5px;
}

.input_size_large:not([type='checkbox'], [type='radio']) {
  height: 40px;
  font-size: 16px;
}

.textarea {
  padding: 12px 16px;
  min-height: 50px;
}

.input:not([type='checkbox'], [type='radio']):focus,
.textarea:focus {
  outline: none;
  box-shadow: 0 0 0 6px var(--color-primary-focus-outline), inset 0 -1px 0 rgb(0 0 0 / 10%), inset 0 0 0 1px rgb(0 0 0 / 15%), inset 0 0 0 1px var(--color-primary-focus-outline);
  border-color: var(--color-neutral-border-boldest);
}

.input[type="checkbox"],
.input[type="radio"] {
  width: 16px;
  height: 16px;
}

.input {
  &__spinner {
    width: 16px;
    height: 16px;
    border-radius: 100%;
    box-sizing: border-box;
    border: 2px solid var(--persimmon_400);
    border-right-color: transparent;
    animation: waiting-spin 1s linear infinite;
  }
}

@keyframes waiting-spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

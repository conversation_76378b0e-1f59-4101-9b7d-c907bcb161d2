.button {
  --button-color: var(--color-neutral-content);
  --button-background-color: var(--color-neutral-background);
  --button-border: transparent;

  height: 32px;
  border: 1px solid var(--button-border);
  background-color: var(--button-background-color);
  color: var(--button-color);
  cursor: pointer;
  padding: 0 15px;
  outline: none;
  display: inline-flex;
  align-items: center;
  border-radius: 5px;
  text-align: center;
  text-decoration: none;
  transition: all 150ms ease;
  justify-content: center;
  font-weight: 500;
  font-size: 14px;
  box-shadow: none;

  &_newUI {
    .button__extra {
      margin-left: 3px;
    }
  }

  &_waiting,
  &_disabled,
  &:disabled,
  &[disabled] {
    --button-color: var(--color-neutral-content-subtlest);
    --button-background-color: var(--color-neutral-surface);
    --button-events: none;
    --button-border: 1px solid transparent;

    pointer-events: none;
  }

  &:hover {
    box-shadow: 0 2px 4px rgb(0 0 0 / 5%);
  }

  &:active {
    background: linear-gradient(0deg, rgb(0 0 0 / 2%), rgb(0 0 0 / 2%)), var(--color-neutral-surface);
    box-shadow: inset 0 1px 0 rgb(0 0 0 / 10%), inset 0 0 0 1px rgb(0 0 0 / 20%);
  }

  &:focus {
    outline: none;
    box-shadow: 0 0 0 6px var(--color-primary-surface-content-subtle), inset 0 -1px 0 rgb(0 0 0 / 10%), inset 0 0 0 1px rgb(0 0 0 / 15%), inset 0 0 0 1px var(--color-primary-surface-content-subtle);
  }

  &__extra {
    font-size: 14px;
    line-height: 16px;
    display: flex;
    color: var(--sand_500);
    align-items: center;
    margin-left: 7px;
    margin-right: -7px;
  }

  &__icon {
    display: flex;
    width: 16px;
    height: 100%;
    align-items: center;

    &:not(:only-child) {
      margin-right: 12px;
    }

    &:only-child {
      flex: 1;
      align-items: center;
      justify-content: center;
    }
  }

  &_type {
    &_text {
      padding: 0;
    }

    &_text,
    &_link,
    &[href] {
      padding: 0;
      min-width: 0;
      box-shadow: none;
      background: none;

      &:hover {
        box-shadow: none;
      }
    }

    &_link,
    &[href] {
      --button-color: var(--primary_link);

      text-decoration: underline;
    }
  }

  &_look {
    &_primary {
      --button-color: var(--color-primary-surface-content);
      --button-background-color: var(--color-primary-surface);

      box-shadow: inset 0 -1px 2px rgba(var(--color-neutral-shadow-raw) / 10%);

      &:disabled {
        --button-color: var(--color-neutral-content-subtlest);
        --button-background-color: var(--color-neutral-surface);
      }

      &:hover {
        background: linear-gradient(0deg, rgb(255 255 255 / 10%), rgb(255 255 255 / 10%)), var(--primary_link);
        box-shadow: 0 2px 4px var(--color-primary-surface-content-subtle), inset 0 -1px 0 rgb(0 0 0 / 10%);
      }

      &:active {
        background: linear-gradient(0deg, rgb(0 0 0 / 4%), rgb(0 0 0 / 4%)), var(--primary_link);
        box-shadow: inset 0 1px 0 rgb(0 0 0 / 10%);
      }

      &:focus {
        box-shadow: 0 0 0 6px var(--color-primary-surface-content-subtle), inset 0 -1px 0 rgb(0 0 0 / 10%);
      }
    }

    &_danger {
      --button-color: var(--color-negative-content);
    }

    &_destructive {
      --button-color: var(--color-negative-surface-content);
      --button-background-color: var(--color-negative-surface);

      border-color: var(--color-negative-border);
    }

    &_outlined {
      --button-color: var(--color-primary-content);
      --button-border: var(--color-primary-border);

      background: none;
      padding: 1.5rem 2rem;
      font-size: 0.875rem;
      outline: none;
      box-shadow: none;
    }

    &_alt {
      --button-color: var(--color-neutral-content);
      --button-background-color: var(--color-neutral-background);

      box-shadow: none;

      &:hover,
      &:focus,
      &:active {
        --button-color: var(--color-neutral-content);
        --button-background-color: var(--color-neutral-surface-hover);

        box-shadow: none;
      }

      &:disabled {
        --button-color: var(--color-neutral-content-subtlest);
        --button-background-color: var(--color-neutral-background);
      }
    }

    &_active {
      --button-border: var(--color-neutral-border);

      background: none;
      border-radius: 4px;
      box-shadow: none;
      outline: none;

      &:focus {
        box-shadow: none;
      }
    }
  }

  &_look_destructive:disabled,
  &_look_destructive.button_waiting {
    --button-color: var(--color-neutral-content-subtlest);
    --button-background-color: var(--color-negative-content);
  }

  &_danger.button_look_alt,
  &_danger.button_type_text {
    --button-color: var(--color-negative-content);

    &:disabled {
      --button-color: var(--color-neutral-subtlest);
      --button-background-color: var(--color-neutral-surface);
    }
  }

  &_size {
    &_compact {
      height: 36px;
      font-size: 16px;
      line-height: 20px;
    }

    &_medium {
      height: 32px;
      font-size: 14px;
      line-height: 20px;
    }

    &_small {
      height: 24px;
      font-size: 12px;
      line-height: 12px;
      padding: 0 10px;
    }

    &_large {
      height: 40px;
      font-size: 16px;
    }
  }

  &_size_small &__extra {
    margin-left: 5px;
    margin-right: -5px;
  }

  &_size_medium &__extra {
    margin-left: 7px;
    margin-right: -7px;
  }

  &_size_compact &__extra {
    margin-left: 7px;
    margin-right: -7px;
  }

  &_size_large &__extra {
    margin-left: 10px;
    margin-right: -10px;
  }

  &_nopadding {
    padding: 0;
  }

  &_withIcon {
    justify-content: space-between;
  }

  &_withIcon:not(.button_type_link, &[href]) {
    padding: 0 14px;
  }

  &_withIcon.button_size_small {
    padding: 0 10px;
  }

  &_waiting {
    --button-background-color: var(--color-neutral-background);
    --button-color: var(--color-neutral-surface-hover);

    pointer-events: none;
    background-repeat: repeat;
    background-position: 40px;
    background-size: 37px 100%;
    animation: button-waiting 1s linear infinite;
    background-image: var(--button-waiting-animation-bg);
  }

  &_waiting.button_look_primary {
    --button-background-color: var(--primary_link);

    background-image: var(--primary-button-waiting-animation-bg);
  }

  &_waiting.button_look_danger,
  &_waiting.button_look_destructive {
    --button-background-color: var(--color-negative-emphasis-subtle);

    background-image: var(--negative-button-waiting-animation-bg);
  }

  &_size_small &__icon {
    width: 12px;

    &:not(:only-child) {
      margin-right: 8px;
    }
  }
}

.button-group {
  display: flex;

  &:not(.button-group_collapsed) {
    .button+.button {
      margin-left: 16px;
    }
  }

  &_collapsed {
    .button:first-child {
      border-radius: 5px 0 0 5px;
    }

    .button:last-child {
      border-radius: 0 5px 5px 0;
    }

    .button:not(:first-child, :last-child) {
      border-radius: 0;
    }
  }
}

@keyframes button-waiting {
  0% {
    background-position: 0 0;
  }

  100% {
    background-position: 37px 0;
  }
}

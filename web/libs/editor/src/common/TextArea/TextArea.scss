.textarea {
  --textarea-surface-color: var(--color-neutral-surface);
  --textarea-border-size: 1px;
  --textarea-border-color: var(--color-neutral-border);
  --textarea-border-radius: 4px;
  --textarea-font-size: 16px;
  --textarea-line-height: 24px;
  --textarea-min-height: 40px;
  --textarea-letter-spacing: 0.5px;
  --textarea-padding: 8px var(--textarea-min-height) 8px 16px;

  &_inline {
    display: flex;
    align-items: center;
    min-height: var(--textarea-min-height) !important;
    border: var(--textarea-border-size) solid var(--textarea-border-color) !important;
    outline-color: var(--color-primary-border) !important;
    border-radius: var(--textarea-border-radius) !important;
    background-color: var(--textarea-surface-color) !important;
    padding: var(--textarea-padding) !important;
    font-size: var(--textarea-font-size) !important;
    line-height: var(--textarea-line-height) !important;
    letter-spacing: var(--textarea-letter-spacing) !important;
    grid-row: 1 / 2;
    grid-column: 1 / span 2;
    padding-right: var(--textarea-min-height);
    scrollbar-width: none;
    -ms-overflow-style: none;

    &::-webkit-scrollbar {
      width: 0;
      height: 0;
    }
  }

  &_autosize {
    resize: none !important;
  }
}

:global(.ant-form-item .ant-form-item-control-input-content input) {
  background-color: var(--color-neutral-background);
  color: var(--color-neutral-content);
  border-color: var(--color-neutral-border);
  border-radius: var(--corner-radius-smaller);
}

:global(.ant-form-item .ant-form-item-control-input-content input:focus) {
  border-color: var(--color-neutral-border-bold);
  box-shadow: 0 0 0 4px var(--color-primary-focus-outline);
}

:global(.ant-form-item .ant-form-item-control-input-content input:focus-visible) {
  border-color: var(--color-neutral-border-bold);
  box-shadow: 0 0 0 4px var(--color-primary-focus-outline);
}

:global(.ant-form-item .ant-form-item-control-input-content textarea) {
  background-color: var(--color-neutral-background);
  color: var(--color-neutral-content);
  border-color: var(--color-neutral-border);
  border-radius: var(--corner-radius-smaller);
}

:global(.ant-form-item .ant-form-item-control-input-content textarea:focus) {
  border-color: var(--color-neutral-border-bold);
  box-shadow: 0 0 0 4px var(--color-primary-focus-outline);
}

:global(.ant-form-item .ant-form-item-control-input-content textarea:focus-visible) {
  border-color: var(--color-neutral-border-bold);
  box-shadow: 0 0 0 4px var(--color-primary-focus-outline);
}

:global(.ant-form-item .ant-form-item-control .ant-btn-primary) {
  background-color: var(--color-primary-surface);
  color: var(--color-primary-surface-content);
  border-color: var(--color-primary-border);
  transition: all 150ms ease-out;
}

:global(.ant-form-item .ant-form-item-control .ant-btn-primary:hover) {
  background-color: var(--color-primary-surface-hover);
  color: var(--color-primary-surface-content);
  border-color: var(--color-primary-border);
}

:global(.ant-form-item .ant-form-item-control .ant-btn-primary:active) {
  background-color: var(--color-primary-surface-active);
  color: var(--color-primary-surface-content);
  border-color: var(--color-primary-border);
}

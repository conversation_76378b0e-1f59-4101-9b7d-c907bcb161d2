.range {
  --thickness: 6px;

  display: flex;
  align-items: center;

  &_align {
    &_horizontal {
      min-width: 120px;
      height: 28px;
      padding: 0 5px;
      flex-direction: row;
    }

    &_vertical {
      min-height: 120px;
      width: 28px;
      padding: 5px 0;
      flex-direction: column;
    }
  }

  &__body {
    position: relative;

    .range_align_horizontal & {
      height: var(--thickness);
      width: 120px;
      margin: 0 8px;
    }

    .range_align_vertical & {
      height: 120px;
      width: var(--thickness);
      margin: 8px 0;
    }
  }

  &__line {
    border-radius: 8px;
    background: var(--color-neutral-surface);

    .range_align_horizontal & {
      height: var(--thickness);
      width: 100%;
    }

    .range_align_vertical & {
      width: var(--thickness);
      height: 100%;
    }
  }

  &__indicator {
    border-radius: 8px;
    position: absolute;
    background-color: var(--color-primary-emphasis);

    .range_align_horizontal & {
      top: 0;
      height: var(--thickness);
    }

    .range_align_vertical & {
      left: 0;
      width: var(--thickness);
    }
  }

  &__range-handle {
    width: 0;
    height: 0;
    position: absolute;

    &::before {
      content: '';
      width: 16px;
      height: 16px;
      position: absolute;
      background: var(--color-primary-surface);
      border-radius: 100%;
      box-shadow: 0 5px 10px var(--color-primary-surface-content-subtle), inset 0 -1px 0 rgb(0 0 0 / 10%);
      transform: translate3d(-50%, -50%, 0);
    }

    &::after {
      content: '';
      width: 25px;
      height: 25px;
      position: absolute;
      transform: translate3d(-50%, -50%, 0);
    }

    .range_align_horizontal & {
      top: 50%;
    }

    .range_align_vertical & {
      left: 50%;
    }
  }

  &__icon {
    width: 28px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;

    svg {
      max-width: 100%;
      max-height: 100%;
    }
  }
}

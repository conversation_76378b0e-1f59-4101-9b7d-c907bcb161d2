/**
 * A `LabelStudio` configuration that includes all types of labels but does not contain nested elements
 */
export const allLabelsEmptyConfig = `<View>
  <Header value="Configuration Example with Various Label Types" />

  <Text name="text" value="$text"/>
  <Labels name="label_general" toName="text"/>

  <Image name="image" value="$image"/>
  <RectangleLabels name="label_rect" toName="image"/>
  <EllipseLabels name="label_ellipse" toName="image"/>
  <PolygonLabels name="label_poly" toName="image" />
  <KeyPointLabels name="label_keypoint" toName="image"/>
  <BrushLabels name="label_brush" toName="image"/>

  <HyperText name="html" value="$html"/>
  <HyperTextLabels name="label_text" toName="html"/>

  <Paragraphs name="paragraphs" value="$paragraphs"/>
  <ParagraphLabels name="label_paragraphs" toName="paragraphs"/>

  <TimeSeries name="timeseries" value="$timeseries" valueType="json">
    <Channel column="one" />
    <Channel column="two" />
  </TimeSeries>
  <TimeSeriesLabels name="label_ts" toName="timeseries"/>
</View>
`;

const timeseriesTime = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10];
export const allLabelsEmptyData = {
  text: "Hello World!",
  image:
    "https://htx-pub.s3.us-east-1.amazonaws.com/examples/images/nick-owuor-astro-nic-visuals-wDifg5xc9Z4-unsplash.jpg",
  html: "<p>Hello World!</p>",
  paragraphs: [
    {
      author: "Alice",
      text: "Hi, Bob.",
    },
  ],
  timeseries: {
    time: timeseriesTime,
    one: timeseriesTime.map((t) => 100 * Math.sin(t / 2)),
    two: timeseriesTime.map((t) => 100 * Math.cos(t / 3)),
  },
};

/**
 * This result includes labels generated from the tags in the `allLabelsEmptyConfig` configuration, but label values are not present in the configuration itself.
 */
export const resultWithNotExistedLabels = [
  {
    value: {
      start: 0,
      end: 5,
      text: "Hello",
      labels: ["General Label"],
    },
    id: "FiJ7_DQsDf",
    from_name: "label_general",
    to_name: "text",
    type: "labels",
    origin: "manual",
  },
  {
    original_width: 2242,
    original_height: 2802,
    image_rotation: 0,
    value: {
      x: 5,
      y: 6,
      width: 11,
      height: 7,
      rotation: 0,
      rectanglelabels: ["Rectangle Label"],
    },
    id: "xTuLLpaV-t",
    from_name: "label_rect",
    to_name: "image",
    type: "rectanglelabels",
    origin: "manual",
  },
  {
    original_width: 2242,
    original_height: 2802,
    image_rotation: 0,
    value: {
      x: 26,
      y: 8,
      radiusX: 4,
      radiusY: 3,
      rotation: 0,
      ellipselabels: ["Ellipse Label"],
    },
    id: "SbRdv9SSsE",
    from_name: "label_ellipse",
    to_name: "image",
    type: "ellipselabels",
    origin: "manual",
  },
  {
    original_width: 2242,
    original_height: 2802,
    image_rotation: 0,
    value: {
      points: [
        [6, 17],
        [19, 17],
        [18, 25],
        [6, 24],
      ],
      polygonlabels: ["Polygon Label"],
    },
    id: "A11cw_CpRi",
    from_name: "label_poly",
    to_name: "image",
    type: "polygonlabels",
    origin: "manual",
  },
  {
    original_width: 2242,
    original_height: 2802,
    image_rotation: 0,
    value: {
      x: 26,
      y: 20,
      width: 0.3,
      keypointlabels: ["KeyPoint Label"],
    },
    id: "esGZEVnsg7",
    from_name: "label_keypoint",
    to_name: "image",
    type: "keypointlabels",
    origin: "manual",
  },
  {
    original_width: 2242,
    original_height: 2802,
    image_rotation: 0,
    value: {
      format: "rle",
      rle: [
        1, 127, 109, 144, 57, 27, 255, 255, 255, 0, 255, 255, 224, 31, 255, 252, 3, 255, 255, 128, 127, 255, 240, 15,
        255, 254, 1, 255, 255, 192, 63, 255, 248, 7, 255, 255, 0, 255, 255, 224, 31, 255, 252, 3, 255, 255, 128, 127,
        255, 240, 15, 255, 254, 1, 255, 255, 192, 63, 255, 248, 7, 255, 255, 0, 255, 255, 224, 31, 255, 252, 3, 255,
        255, 128, 127, 255, 240, 15, 255, 254, 1, 255, 255, 192, 63, 255, 248, 7, 255, 255, 0, 255, 255, 224, 31, 255,
        252, 3, 150, 17, 128, 70, 221, 150, 223, 227, 125, 140, 75, 145, 73, 128, 70, 3, 26, 148, 123, 25, 143, 254, 62,
        184, 220, 227, 9, 228, 80, 224, 17, 167, 199, 157, 156, 255, 227, 230, 141, 174, 48, 174, 68, 230, 1, 26, 28,
        115, 241, 247, 104, 127, 248, 236, 35, 84, 228, 72, 224, 17, 177, 95, 127, 142, 86, 59, 104, 245, 245, 31, 252,
        123, 209, 223, 71, 59, 28, 21, 183, 248, 222, 99, 11, 228, 66, 224, 17, 158, 71, 143, 184, 255, 227, 228, 141,
        154, 48, 78, 68, 14, 1, 26, 4, 119, 155, 207, 254, 61, 216, 215, 249, 15, 184, 4, 103, 81, 199, 71, 201, 192,
        64, 255, 227, 245, 142, 170, 52, 238, 67, 206, 1, 27, 79, 1, 31, 255, 143, 182, 61, 216, 243, 47, 223, 199, 27,
        200, 110, 192, 35, 56, 142, 247, 128, 161, 255, 199, 175, 26, 167, 33, 171, 0, 140, 234, 59, 78, 2, 167, 255,
        30, 140, 106, 60, 134, 92, 2, 51, 56, 226, 35, 227, 224, 44, 127, 241, 248, 71, 51, 26, 55, 33, 119, 0, 141, 62,
        60, 104, 245, 227, 232, 224, 48, 127, 241, 245, 199, 183, 30, 85, 251, 248, 222, 121, 10, 88, 4, 101, 113, 214,
        199, 253, 192, 107, 255, 227, 203, 141, 23, 144, 157, 128, 70, 89, 29, 39, 1, 195, 255, 143, 2, 52, 62, 66, 78,
        1, 25, 52, 111, 177, 234, 112, 29, 63, 248, 250, 99, 146, 140, 238, 48, 30, 66, 38, 1, 26, 68, 126, 60, 7, 159,
        254, 54, 126, 66, 22, 1, 25, 60, 117, 92, 7, 207, 254, 51, 14, 65, 254, 1, 25, 76, 115, 156, 7, 239, 254, 61,
        248, 199, 121, 7, 120, 4, 100, 81, 190, 199, 163, 192, 129, 255, 227, 236, 140, 247, 144, 99, 128, 70, 105, 29,
        229, 187, 248, 241, 35, 212, 143, 150, 63, 94, 4, 55, 255, 27, 55, 32, 187, 0, 140, 110, 57, 120, 254, 184, 17,
        255, 252, 119, 177, 156, 114, 10, 176, 8, 199, 99, 147, 224, 74, 127, 241, 217, 70, 113, 200, 37, 192, 35, 29,
        141, 194, 60, 94, 4, 199, 255, 30, 252, 113, 17, 154, 242, 7, 112, 8, 212, 227, 195, 143, 74, 62, 56, 252, 248,
        19, 223, 252, 108, 92, 129, 172, 2, 51, 88, 236, 184, 20, 159, 252, 103, 188, 129, 124, 2, 52, 232, 238, 120,
        20, 223, 252, 125, 177, 158, 114, 4, 240, 8, 203, 163, 159, 143, 211, 129, 85, 255, 198, 197, 200, 17, 192, 35,
        75, 143, 203, 129, 91, 255, 199, 39, 200, 14, 192, 35, 26, 142, 139, 129, 97, 255, 199, 165, 24, 55, 32, 43, 0,
        140, 66, 55, 248, 253, 120, 22, 95, 252, 113, 156, 128, 156, 2, 52, 142, 5, 175, 255, 31, 172, 98, 156, 128,
        140, 2, 56, 46, 5, 183, 255, 28, 47, 32, 31, 0, 140, 90, 63, 78, 5, 183, 255, 31, 39, 32, 31, 0, 142, 91, 129,
        113, 255, 198, 93, 200, 5, 192, 35, 66, 224, 92, 255, 241, 190, 242, 1, 48, 8, 199, 99, 237, 224, 92, 255, 241,
        229, 114, 0, 240, 8, 193, 99, 205, 224, 93, 255, 241, 137, 242, 0, 112, 8, 194, 35, 177, 224, 94, 127, 241, 166,
        114, 0, 48, 8, 199, 35, 208, 224, 94, 255, 241, 202, 113, 255, 240, 8, 198, 99, 231, 224, 95, 255, 241, 181,
        113, 255, 176, 8, 216, 56, 24, 63, 252, 105, 28, 127, 220, 2, 58, 206, 6, 15, 255, 31, 156, 101, 124, 127, 204,
        2, 63, 62, 6, 23, 255, 30, 183, 31, 239, 0, 141, 199, 129, 137, 255, 198, 241, 199, 249, 192, 35, 14, 143, 183,
        129, 137, 255, 199, 239, 25, 23, 31, 227, 0, 142, 27, 129, 141, 255, 199, 105, 199, 247, 192, 35, 52, 224, 100,
        127, 241, 155, 113, 253, 112, 8, 196, 99, 227, 224, 100, 127, 241, 218, 241, 253, 112, 8, 239, 248, 25, 95, 252,
        102, 156, 127, 60, 2, 58, 94, 6, 95, 255, 29, 167, 31, 199, 0, 140, 90, 60, 126, 6, 111, 255, 25, 151, 31, 191,
        0, 140, 190, 62, 110, 6, 119, 255, 29, 135, 31, 183, 0, 140, 142, 57, 200, 254, 248, 26, 31, 252, 102, 60, 126,
        204, 2, 55, 94, 6, 151, 255, 29, 95, 31, 175, 0, 140, 198, 63, 110, 6, 159, 255, 24, 167, 31, 167, 0, 140, 98,
        62, 126, 6, 167, 255, 28, 71, 31, 163, 0, 140, 46, 61, 30, 6, 175, 255, 30, 255, 31, 159, 0, 140, 86, 60, 206,
        6, 191, 255, 25, 87, 31, 151, 0, 140, 190, 62, 110, 6, 199, 255, 27, 239, 31, 147, 0, 140, 118, 62, 222, 6, 207,
        255, 30, 239, 31, 143, 0, 140, 30, 61, 62, 6, 223, 255, 24, 207, 31, 135, 0, 140, 74, 60, 94, 6, 231, 255, 26,
        55, 31, 131, 0, 140, 166, 62, 62, 6, 239, 255, 27, 15, 31, 127, 0, 140, 82, 62, 62, 6, 247, 255, 27, 255, 31,
        127, 0, 140, 199, 129, 191, 255, 199, 201, 24, 71, 31, 123, 0, 141, 255, 129, 193, 255, 198, 233, 199, 222, 192,
        35, 203, 224, 112, 127, 241, 246, 198, 31, 199, 220, 192, 35, 55, 224, 113, 127, 241, 191, 241, 247, 48, 8, 243,
        248, 28, 95, 252, 122, 188, 125, 188, 2, 54, 14, 4, 167, 255, 31, 44, 125, 60, 4, 223, 254, 51, 110, 62, 206, 1,
        25, 44, 126, 60, 9, 95, 254, 58, 152, 252, 120, 9, 159, 252, 123, 92, 125, 156, 2, 53, 94, 4, 183, 255, 31, 180,
        107, 60, 4, 223, 254, 54, 222, 62, 198, 1, 26, 175, 2, 95, 255, 142, 126, 51, 232, 253, 120, 9, 127, 252, 125,
        209, 135, 241, 245, 240, 8, 213, 120, 19, 31, 252, 101, 209, 212, 240, 19, 63, 248, 226, 248, 250, 248, 4, 106,
        220, 9, 143, 254, 59, 248, 207, 120, 9, 191, 252, 100, 60, 125, 108, 2, 53, 110, 4, 207, 255, 25, 116, 119, 60,
        4, 207, 254, 57, 14, 62, 182, 1, 26, 183, 2, 103, 255, 141, 250, 51, 62, 2, 103, 255, 31, 15, 31, 91, 0, 141,
        87, 129, 51, 255, 199, 157, 24, 4, 117, 220, 4, 207, 254, 52, 238, 62, 174, 1, 26, 175, 2, 107, 255, 140, 122,
        50, 190, 2, 103, 255, 31, 100, 97, 188, 125, 76, 2, 53, 94, 4, 215, 255, 27, 116, 96, 17, 191, 112, 19, 63, 248,
        223, 248, 250, 152, 4, 106, 188, 9, 191, 254, 58, 184, 202, 163, 224, 224, 37, 255, 241, 249, 198, 35, 199, 211,
        192, 35, 85, 224, 78, 255, 241, 248, 112, 19, 63, 248, 226, 184, 250, 120, 4, 106, 188, 14, 191, 254, 63, 120,
        198, 56, 250, 88, 4, 106, 188, 14, 207, 254, 56, 142, 62, 150, 1, 26, 175, 3, 179, 255, 143, 95, 143, 165, 128,
        70, 171, 192, 237, 255, 227, 35, 227, 232, 224, 17, 170, 240, 59, 127, 248, 220, 184, 250, 56, 4, 106, 188, 14,
        223, 254, 60, 14, 62, 142, 1, 26, 175, 3, 187, 255, 140, 67, 143, 161, 128, 70, 171, 192, 238, 255, 227, 63,
        227, 232, 96, 17, 170, 240, 59, 191, 248, 213, 184, 250, 24, 4, 106, 188, 14, 239, 254, 55, 174, 62, 134, 1, 26,
        175, 3, 187, 255, 143, 194, 48, 238, 62, 126, 1, 26, 175, 3, 191, 255, 142, 15, 143, 159, 128, 70, 175, 192,
        239, 255, 227, 243, 140, 83, 143, 157, 128, 70, 211, 192, 240, 255, 227, 128, 227, 231, 96, 17, 193, 240, 60,
        63, 248, 243, 248, 249, 216, 4, 115, 188, 15, 31, 254, 49, 174, 62, 110, 1, 29, 191, 3, 199, 255, 141, 171, 143,
        153, 128, 70, 7, 31, 119, 3, 199, 255, 142, 227, 143, 153, 128, 70, 139, 192, 243, 255, 227, 12, 227, 229, 224,
        17, 205, 240, 60, 255, 248, 206, 56, 249, 88, 4, 97, 177, 247, 240, 60, 255, 248, 212, 56, 249, 88, 4, 112, 188,
        15, 79, 254, 54, 142, 62, 78, 1, 24, 180, 126, 252, 15, 79, 254, 56, 14, 62, 78, 1, 28, 207, 3, 215, 255, 142,
        99, 143, 145, 128, 70, 147, 192, 246, 255, 227, 175, 227, 228, 96, 17, 166, 240, 61, 191, 248, 237, 56, 249, 24,
        4, 105, 220, 15, 111, 254, 59, 78, 62, 70, 1, 26, 119, 3, 219, 255, 142, 211, 143, 145, 128, 70, 155, 192, 246,
        255, 227, 180, 227, 228, 96, 17, 166, 240, 61, 191, 248, 237, 56, 249, 24, 4, 105, 220, 15, 111, 254, 59, 78,
        62, 70, 1, 26, 111, 3, 219, 255, 142, 211, 143, 145, 128, 70, 157, 192, 246, 255, 227, 180, 227, 228, 96, 17,
        166, 240, 61, 191, 248, 237, 56, 249, 24, 4, 105, 188, 15, 111, 254, 59, 78, 62, 70, 1, 26, 111, 3, 219, 255,
        142, 211, 143, 145, 128, 70, 157, 192, 246, 255, 227, 180, 227, 228, 96, 17, 166, 240, 61, 191, 248, 237, 56,
        249, 24, 4, 105, 188, 15, 111, 254, 59, 78, 62, 70, 1, 26, 111, 3, 219, 255, 142, 211, 143, 145, 128, 70, 155,
        192, 246, 255, 227, 180, 227, 228, 96, 17, 166, 240, 61, 191, 248, 237, 56, 249, 24, 4, 105, 188, 15, 111, 254,
        59, 78, 62, 70, 1, 26, 111, 3, 219, 255, 142, 211, 143, 145, 128, 70, 163, 192, 246, 255, 227, 176, 227, 228,
        96, 17, 179, 240, 61, 191, 248, 230, 248, 249, 24, 4, 112, 60, 15, 111, 254, 56, 62, 62, 70, 1, 28, 223, 3, 219,
        255, 141, 179, 143, 145, 128, 71, 157, 192, 246, 255, 227, 84, 227, 227, 224, 17, 138, 112, 61, 255, 248, 207,
        56, 248, 248, 4, 108, 156, 15, 127, 254, 48, 254, 62, 62, 1, 29, 127, 3, 219, 255, 143, 15, 143, 143, 128, 70,
        55, 31, 215, 3, 219, 255, 141, 235, 143, 143, 128, 71, 63, 192, 247, 255, 227, 49, 227, 227, 224, 17, 216, 240,
        61, 191, 248, 249, 248, 249, 24, 4, 105, 92, 15, 111, 254, 58, 14, 62, 70, 1, 26, 23, 3, 219, 255, 140, 211,
        143, 145, 128, 70, 89, 192, 245, 255, 227, 171, 227, 228, 224, 17, 138, 112, 61, 127, 248, 201, 56, 249, 88, 7,
        3, 211, 255, 142, 255, 143, 151, 128, 71, 207, 192, 243, 255, 227, 188, 227, 229, 224, 17, 232, 112, 60, 255,
        248, 239, 56, 249, 120, 4, 115, 252, 15, 63, 254, 59, 206, 62, 94, 1, 26, 167, 3, 207, 255, 142, 243, 143, 151,
        128, 70, 25, 192, 243, 255, 227, 188, 227, 230, 96, 17, 223, 240, 60, 191, 248, 239, 56, 249, 152, 4, 110, 156,
        15, 47, 254, 59, 206, 62, 102, 1, 25, 55, 3, 203, 255, 142, 243, 143, 155, 128, 71, 127, 192, 241, 255, 227,
        180, 227, 230, 224, 17, 151, 112, 60, 127, 248, 231, 120, 249, 216, 4, 115, 220, 15, 15, 254, 56, 94, 62, 118,
        1, 26, 119, 3, 195, 255, 141, 187, 143, 157, 128, 70, 147, 192, 240, 255, 227, 86, 227, 231, 96, 17, 154, 112,
        60, 63, 248, 207, 248, 249, 216, 4, 99, 156, 15, 15, 254, 49, 14, 62, 126, 1, 192, 239, 255, 227, 196, 227, 232,
        96, 17, 247, 112, 59, 191, 248, 222, 56, 250, 24, 4, 123, 28, 14, 239, 254, 50, 238, 62, 134, 1, 28, 7, 3, 183,
        255, 143, 143, 143, 163, 128, 70, 13, 31, 23, 3, 179, 255, 142, 99, 143, 165, 128, 70, 89, 31, 159, 3, 175, 255,
        140, 183, 143, 167, 128, 70, 231, 192, 234, 255, 227, 155, 227, 234, 224, 17, 243, 112, 58, 63, 248, 253, 163,
        25, 227, 234, 224, 17, 179, 240, 58, 63, 248, 223, 248, 250, 248, 4, 124, 156, 14, 127, 254, 53, 142, 62, 190,
        1, 28, 159, 3, 159, 255, 141, 11, 143, 175, 128, 70, 141, 192, 231, 255, 227, 18, 227, 235, 224, 17, 129, 199,
        235, 192, 229, 255, 227, 194, 227, 236, 224, 17, 225, 240, 57, 127, 248, 221, 56, 251, 56, 4, 112, 188, 14, 95,
        254, 50, 126, 62, 206, 1, 24, 92, 124, 28, 14, 63, 254, 61, 190, 62, 222, 1, 25, 76, 126, 60, 14, 47, 254, 54,
        254, 62, 230, 1, 26, 31, 3, 135, 255, 143, 147, 143, 189, 128, 70, 181, 192, 224, 255, 227, 101, 227, 239, 224,
        17, 175, 112, 55, 191, 248, 247, 120, 252, 56, 4, 107, 220, 13, 223, 254, 53, 190, 63, 22, 1, 27, 23, 3, 111,
        255, 142, 207, 143, 201, 128, 70, 197, 192, 218, 255, 227, 46, 227, 242, 224, 17, 197, 112, 54, 63, 248, 248,
        184, 252, 216, 4, 100, 28, 13, 143, 254, 55, 142, 63, 62, 1, 28, 175, 3, 91, 255, 143, 166, 48, 158, 63, 62, 1,
        24, 199, 3, 91, 255, 141, 183, 143, 211, 128, 71, 21, 192, 212, 255, 227, 226, 227, 245, 96, 17, 137, 199, 229,
        192, 211, 255, 227, 98, 227, 245, 224, 17, 172, 240, 52, 191, 248, 237, 184, 253, 184, 4, 113, 124, 13, 31, 254,
        51, 126, 63, 118, 1, 29, 207, 3, 63, 255, 143, 163, 143, 223, 128, 70, 33, 30, 231, 3, 59, 255, 142, 3, 143,
        225, 128, 70, 85, 31, 167, 3, 51, 255, 143, 178, 48, 190, 63, 142, 1, 25, 228, 126, 188, 12, 191, 254, 55, 94,
        63, 158, 1, 25, 236, 126, 188, 12, 159, 254, 62, 120, 194, 56, 254, 152, 4, 104, 17, 251, 112, 50, 63, 248, 218,
        184, 254, 216, 4, 104, 92, 12, 127, 254, 59, 206, 63, 198, 1, 26, 20, 126, 252, 12, 79, 254, 62, 8, 196, 120,
        255, 56, 4, 104, 113, 251, 240, 48, 191, 248, 253, 99, 43, 227, 253, 224, 17, 163, 240, 48, 191, 248, 212, 184,
        255, 184, 4, 105, 28, 12, 15, 254, 56, 62, 63, 254, 1, 25, 212, 125, 156, 11, 223, 254, 57, 206, 64, 14, 1, 24,
        236, 122, 124, 11, 191, 254, 61, 30, 64, 30, 1, 24, 220, 125, 92, 11, 175, 254, 55, 174, 64, 38, 1, 25, 188,
        127, 92, 11, 143, 254, 62, 216, 194, 185, 0, 184, 4, 106, 28, 11, 143, 254, 54, 206, 64, 62, 1, 26, 143, 2, 219,
        255, 143, 123, 144, 19, 128, 70, 169, 192, 181, 255, 227, 89, 228, 5, 96, 17, 169, 199, 249, 192, 178, 255, 227,
        180, 228, 6, 96, 17, 162, 199, 227, 192, 176, 255, 227, 219, 140, 59, 144, 27, 128, 70, 83, 30, 247, 2, 187,
        255, 143, 214, 50, 190, 64, 126, 1, 24, 140, 120, 60, 10, 223, 254, 53, 94, 64, 142, 1, 24, 28, 115, 220, 10,
        191, 254, 56, 238, 64, 166, 1, 26, 52, 121, 220, 10, 143, 254, 56, 222, 64, 190, 1, 26, 156, 122, 252, 10, 79,
        254, 63, 152, 213, 185, 3, 88, 4, 107, 177, 240, 240, 40, 191, 248, 215, 57, 3, 152, 4, 96, 113, 156, 198, 253,
        30, 36, 127, 124, 9, 223, 254, 57, 110, 64, 254, 1, 26, 215, 2, 123, 255, 142, 79, 144, 67, 128, 70, 105, 31,
        79, 2, 107, 255, 143, 238, 53, 206, 65, 30, 1, 24, 220, 121, 252, 9, 143, 254, 62, 216, 207, 121, 4, 184, 4, 97,
        81, 216, 240, 37, 191, 248, 245, 35, 29, 228, 20, 96, 17, 169, 71, 177, 192, 146, 255, 227, 219, 141, 139, 144,
        87, 128, 70, 5, 26, 252, 124, 28, 8, 239, 254, 60, 216, 213, 99, 2, 228, 23, 224, 17, 182, 71, 217, 192, 137,
        255, 227, 233, 142, 78, 51, 254, 65, 166, 1, 25, 252, 112, 209, 228, 240, 33, 191, 248, 226, 249, 7, 56, 4, 98,
        17, 161, 70, 159, 26, 212, 115, 17, 247, 240, 31, 255, 248, 218, 121, 7, 248, 4, 102, 145, 220, 240, 31, 63,
        248, 251, 163, 62, 228, 33, 96, 17, 145, 198, 247, 30, 135, 1, 227, 255, 143, 110, 49, 254, 66, 54, 1, 28, 135,
        1, 219, 255, 142, 238, 48, 254, 66, 70, 1, 25, 228, 119, 156, 7, 47, 254, 61, 152, 214, 249, 9, 152, 4, 103,
        113, 225, 240, 27, 191, 248, 246, 163, 93, 140, 11, 144, 159, 128, 70, 89, 28, 12, 122, 188, 6, 143, 254, 62,
        104, 228, 227, 63, 140, 7, 144, 169, 128, 71, 61, 192, 103, 255, 227, 148, 228, 44, 96, 17, 164, 71, 153, 192,
        100, 255, 227, 124, 228, 45, 224, 17, 164, 199, 149, 192, 96, 255, 227, 246, 141, 47, 144, 189, 128, 70, 101,
        28, 52, 124, 28, 5, 207, 254, 62, 72, 203, 57, 12, 88, 4, 102, 81, 195, 71, 181, 192, 88, 255, 227, 137, 140,
        59, 144, 205, 128, 70, 25, 25, 20, 103, 17, 209, 199, 199, 192, 81, 255, 227, 169, 140, 127, 144, 215, 128, 70,
        3, 26, 164, 122, 156, 4, 239, 254, 61, 8, 206, 185, 14, 24, 4, 103, 81, 199, 71, 205, 192, 72, 255, 227, 242,
        142, 126, 52, 238, 67, 174, 1, 25, 196, 113, 177, 240, 240, 16, 191, 248, 248, 227, 148, 141, 18, 48, 78, 67,
        214, 1, 24, 116, 100, 145, 156, 198, 159, 27, 36, 111, 88, 78, 2, 58, 253, 119, 255, 31, 100, 113, 81, 190, 70,
        207, 26, 140, 103, 113, 146, 198, 31, 200, 138, 192, 35, 62, 143, 83, 85, 255, 199, 219, 27, 212, 97, 92, 137,
        60, 2, 53, 24, 232, 227, 241, 210, 127, 241, 250, 199, 71, 26, 127, 34, 99, 0, 140, 18, 52, 56, 229, 35, 228,
        207, 127, 241, 248, 199, 73, 26, 124, 96, 252, 137, 236, 2, 48, 248, 201, 99, 59, 141, 70, 54, 120, 223, 50, 92,
        4, 111, 209, 181, 70, 171, 25, 252, 101, 81, 138, 127, 255, 240, 15, 255, 254, 1, 255, 255, 192, 63, 255, 248,
        7, 255, 255, 0, 255, 255, 224, 31, 255, 252, 3, 255, 255, 128, 127, 255, 240, 15, 255, 254, 1, 255, 255, 192,
        63, 255, 248, 7, 255, 255, 0, 255, 255, 224, 31, 255, 252, 3, 255, 255, 128, 127, 255, 240, 15, 255, 254, 1,
        255, 255, 192, 63, 255, 248, 7, 255, 255, 0, 255, 255, 224, 31, 255, 252, 3, 255, 255, 128, 127, 255, 240, 15,
        255, 254, 1, 255, 255, 192, 63, 255, 248, 7, 255, 255, 0, 255, 255, 224, 31, 255, 252, 3, 255, 255, 128, 127,
        255, 240, 15, 255, 254, 1, 255, 255, 192, 63, 255, 248, 7, 255, 255, 0, 255, 255, 224, 31, 255, 252, 3, 255,
        255, 128, 127, 255, 240, 15, 255, 254, 1, 255, 255, 192, 63, 255, 248, 7, 255, 255, 0, 255, 255, 224, 31, 255,
        252, 3, 255, 255, 128, 127, 255, 240, 15, 255, 254, 1, 255, 255, 192, 63, 255, 248, 7, 255, 255, 0, 255, 255,
        224, 31, 255, 252, 3, 255, 255, 128, 127, 255, 240, 15, 255, 254, 1, 255, 255, 192, 63, 255, 248, 7, 255, 255,
        0, 255, 255, 224, 31, 255, 252, 3, 255, 255, 128, 127, 255, 240, 15, 255, 254, 1, 255, 255, 192, 63, 255, 248,
        7, 255, 255, 0, 255, 255, 224, 31, 255, 252, 3, 255, 255, 128, 127, 255, 240, 15, 255, 254, 1, 255, 255, 192,
        63, 255, 248, 7, 255, 255, 0, 255, 255, 224, 31, 255, 252, 3, 255, 255, 128, 127, 255, 240, 15, 255, 254, 1,
        255, 255, 192, 63, 255, 248, 7, 255, 255, 0, 255, 255, 224, 31, 255, 252, 3, 255, 255, 128, 127, 255, 240, 15,
        255, 254, 1, 255, 255, 192, 63, 255, 248, 7, 255, 255, 0, 255, 255, 224, 31, 255, 252, 3, 255, 255, 128, 127,
        255, 240, 15, 255, 254, 1, 255, 255, 192, 63, 255, 248, 7, 255, 255, 0, 255, 255, 224, 31, 255, 252, 3, 255,
        255, 128, 127, 255, 240, 15, 255, 254, 1, 255, 255, 192, 63, 255, 248, 7, 255, 255, 0, 255, 255, 224, 31, 255,
        252, 3, 255, 255, 128, 127, 255, 240, 15, 255, 254, 1, 255, 255, 192, 63, 255, 248, 7, 255, 255, 0, 255, 255,
        224, 31, 255, 252, 3, 255, 255, 128, 127, 255, 240, 15, 255, 254, 1, 255, 255, 192, 63, 255, 248, 7, 255, 255,
        0, 255, 255, 224, 31, 255, 252, 3, 255, 255, 128, 127, 255, 240, 15, 255, 254, 1, 255, 255, 192, 63, 255, 248,
        7, 255, 255, 0, 255, 255, 224, 31, 255, 252, 3, 255, 255, 128, 127, 255, 240, 15, 255, 254, 1, 255, 255, 192,
        63, 255, 248, 7, 255, 255, 0, 255, 255, 224, 31, 255, 252, 3, 255, 255, 128, 127, 255, 240, 15, 255, 254, 1,
        255, 255, 192, 63, 255, 248, 7, 255, 255, 0, 255, 255, 224, 31, 255, 252, 3, 255, 255, 128, 127, 255, 240, 15,
        255, 254, 1, 255, 255, 192, 63, 255, 248, 7, 255, 255, 0, 255, 255, 224, 31, 255, 252, 3, 255, 255, 128, 127,
        255, 240, 15, 255, 254, 1, 255, 255, 192, 63, 255, 248, 7, 255, 255, 0, 255, 255, 224, 31, 255, 252, 3, 255,
        255, 128, 127, 255, 240, 15, 255, 254, 1, 255, 255, 192, 63, 255, 248, 7, 255, 255, 0, 255, 255, 224, 31, 255,
        252, 3, 255, 255, 128, 127, 255, 240, 15, 255, 254, 1, 255, 255, 192, 63, 255, 248, 7, 255, 255, 0, 255, 255,
        224, 31, 255, 252, 3, 255, 255, 128, 127, 255, 240, 15, 255, 254, 1, 255, 255, 192, 63, 255, 248, 7, 255, 255,
        0, 255, 255, 224, 31, 255, 252, 3, 255, 255, 128, 127, 255, 240, 15, 255, 254, 1, 255, 255, 192, 63, 255, 248,
        7, 255, 255, 0, 255, 255, 224, 31, 255, 252, 3, 255, 255, 128, 127, 255, 240, 15, 255, 254, 1, 255, 255, 192,
        63, 255, 248, 7, 255, 255, 0, 255, 255, 224, 31, 255, 252, 3, 255, 255, 128, 127, 255, 240, 15, 255, 254, 1,
        255, 255, 192, 63, 255, 248, 7, 255, 255, 0, 255, 255, 224, 31, 255, 252, 3, 255, 255, 128, 127, 255, 240, 15,
        255, 254, 1, 255, 255, 192, 63, 255, 248, 7, 255, 255, 0, 255, 255, 224, 31, 255, 252, 3, 255, 255, 128, 127,
        255, 240, 15, 255, 254, 1, 255, 255, 192, 63, 255, 248, 7, 255, 255, 0, 255, 255, 224, 31, 255, 252, 3, 255,
        255, 128, 127, 255, 240, 15, 255, 254, 1, 255, 255, 192, 63, 255, 248, 7, 255, 255, 0, 255, 255, 224, 31, 255,
        252, 3, 255, 255, 128, 127, 255, 240, 15, 255, 254, 1, 255, 255, 192, 63, 255, 248, 7, 255, 255, 0, 255, 255,
        224, 31, 255, 252, 3, 255, 255, 128, 127, 255, 240, 15, 255, 254, 1, 255, 255, 192, 63, 255, 248, 7, 255, 255,
        0, 255, 255, 224, 31, 255, 252, 3, 255, 255, 128, 127, 255, 240, 15, 255, 254, 1, 255, 255, 192, 63, 255, 248,
        7, 255, 255, 0, 255, 255, 224, 31, 255, 252, 3, 255, 255, 128, 127, 255, 240, 15, 255, 254, 1, 255, 255, 192,
        63, 255, 248, 7, 255, 255, 0, 255, 255, 224, 31, 255, 252, 3, 255, 255, 128, 127, 255, 240, 15, 255, 254, 1,
        255, 255, 192, 63, 255, 248, 7, 255, 255, 0, 255, 255, 224, 31, 255, 252, 3, 255, 255, 128, 127, 255, 240, 15,
        255, 254, 1, 255, 255, 192, 63, 255, 248, 7, 255, 255, 0, 255, 255, 224, 31, 255, 252, 3, 255, 255, 128, 127,
        255, 240, 15, 255, 254, 1, 255, 255, 192, 63, 255, 248, 7, 255, 255, 0, 255, 255, 224, 31, 255, 252, 3, 255,
        255, 128, 127, 255, 240, 15, 255, 254, 1, 255, 255, 192, 63, 255, 248, 7, 255, 255, 0, 255, 255, 224, 31, 255,
        252, 3, 255, 255, 128, 127, 255, 240, 15, 255, 254, 1, 255, 255, 192, 63, 255, 248, 7, 255, 255, 0, 255, 255,
        224, 31, 255, 252, 3, 255, 255, 128, 127, 255, 240, 15, 255, 254, 1, 255, 255, 192, 63, 255, 248, 7, 255, 255,
        0, 255, 255, 224, 31, 255, 252, 3, 255, 255, 128, 127, 255, 240, 15, 255, 254, 1, 247, 76, 192, 0,
      ],
      brushlabels: ["Brush Label"],
    },
    id: "XNeIieDHF1",
    from_name: "label_brush",
    to_name: "image",
    type: "brushlabels",
    origin: "manual",
  },
  {
    value: {
      start: "/p[1]/text()[1]",
      startOffset: 6,
      end: "/p[1]/text()[1]",
      endOffset: 11,
      globalOffsets: {
        start: 6,
        end: 11,
      },
      text: "World",
      hypertextlabels: ["Text Label"],
    },
    id: "jA6ztM3mtW",
    from_name: "label_text",
    to_name: "html",
    type: "hypertextlabels",
    origin: "manual",
  },
  {
    value: {
      start: "0",
      end: "0",
      startOffset: 0,
      endOffset: 7,
      text: "Hi, Bob",
      paragraphlabels: ["Paragraphs Label"],
    },
    id: "O2V550GwUU",
    from_name: "label_paragraphs",
    to_name: "paragraphs",
    type: "paragraphlabels",
    origin: "manual",
  },
  {
    value: {
      start: 1,
      end: 3,
      instant: false,
      timeserieslabels: ["TimeSeries Label"],
    },
    id: "XLOpsmVRtb",
    from_name: "label_ts",
    to_name: "timeseries",
    type: "timeserieslabels",
    origin: "manual",
  },
];

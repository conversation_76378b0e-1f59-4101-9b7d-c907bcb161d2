{"name": "@heartexlabs/lsf-e2e", "version": "1.0.0", "description": "", "main": "codecept.conf.js", "private": true, "scripts": {"test": "codeceptjs run", "test:ui": "codecept-ui run", "test:local": "HEADLESS=true yarn test", "test:parallel": "codeceptjs run-workers", "test:ci": "HEADLESS=true COVERAGE=true yarn run test:parallel", "test:ci:no-worker": "HEADLESS=true codeceptjs run", "coverage:istanbul": "node ./coverage-to-istanbul.js", "coverage:report": "nyc report --reporter=html --include=../src/**/* --report-dir=./coverage --temp-dir=./output/coverage", "codeceptjs": "codeceptjs run --steps", "codeceptjs:headless": "HEADLESS=true codeceptjs run --steps", "codeceptjs:ui": "codecept-ui --app", "codeceptjs:demo": "codeceptjs run --steps -c node_modules/@codeceptjs/examples", "codeceptjs:demo:headless": "HEADLESS=true codeceptjs run --steps -c node_modules/@codeceptjs/examples", "codeceptjs:demo:ui": "codecept-ui --app  -c node_modules/@codeceptjs/examples", "show-trace": "playwright show-trace"}, "devDependencies": {"@codeceptjs/configure": "^0.7.0", "@codeceptjs/examples": "^1.2.1", "@codeceptjs/ui": "^0.4.6", "@types/mkdirp": "^1.0.1", "@types/node": "^16.11.6", "@types/puppeteer": "^5.4.3", "@types/rimraf": "^3.0.0", "@typescript-eslint/eslint-plugin": "^5.6.0", "@typescript-eslint/parser": "^5.6.0", "codeceptjs": "^3.3.3", "eslint": "^8.4.1", "eslint-plugin-codeceptjs": "^1.3.0", "mkdirp": "^1.0.4", "nyc": "^15.1.0", "playwright": "^1.16.3", "puppeteer": "^24.6.1", "rimraf": "^3.0.2", "strman": "2.0.1", "ts-node": "^10.0.0", "typescript": "^4.5.3", "v8-to-istanbul": "^9.0.0", "xml2js": "^0.4.23", "xmlbuilder": "^15.1.1"}, "resolutions": {"axios": "1.8.2", "cookie": "0.7.0", "debug": "^4.3.1", "electron": "^22.3.25", "**/express/path-to-regexp": "0.1.12", "follow-redirects": "^1.15.5", "ws": ">=7.5.10", "send": "0.19.0"}, "dependencies": {}}
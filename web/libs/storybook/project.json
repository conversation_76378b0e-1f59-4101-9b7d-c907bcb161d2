{"name": "storybook", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/storybook/src", "projectType": "library", "tags": [], "targets": {"test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "libs/storybook/jest.config.ts", "passWithNoTests": true}, "configurations": {"ci": {"ci": true, "codeCoverage": true}}}, "storybook": {"executor": "@nx/storybook:storybook", "options": {"port": 4400, "configDir": "libs/storybook/.storybook", "assets": [{"glob": "**/*", "input": "libs/ui/src/fonts", "output": "fonts"}]}, "configurations": {"ci": {"quiet": true}}}, "build-storybook": {"executor": "@nx/storybook:build", "outputs": ["{options.outputDir}"], "options": {"outputDir": "dist/libs/storybook/storybook", "configDir": "libs/storybook/.storybook"}, "configurations": {"ci": {"quiet": true}}}, "test-storybook": {"executor": "nx:run-commands", "options": {"command": "test-storybook -c libs/storybook/.storybook --url=http://localhost:4400"}}}}
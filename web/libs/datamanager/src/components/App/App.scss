.root {
  height: 100%;
  box-sizing: border-box;
  background-color: var(--color-neutral-background);
}

.tab-panel {
  display: flex;
  padding: 12px 16px;
  background-color: var(--color-neutral-background);
  color: var(--color-neutral-content-subtle);
  justify-content: space-between;
  border-bottom: 1px solid var(--color-neutral-border);

  &_newUI {
    padding: 8px;
  }
}

.grid {
  flex: 1;
}

.grid__item {
  padding: 10px;
  justify-content: space-between;
  box-sizing: border-box;
  box-shadow: -0.5px -0.5px 0 0.5px #ccc;
}

.container {
  padding: 2em;
  width: 90%;
  height: 600px;
  margin-left: auto;
  margin-right: auto;
}

.app-loader {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.ant-picker-cell {
  padding: 3px 0 !important;
  border: none !important;
}

.offscreen {
  bottom: 100%;
  right: 100%;
  opacity: 0;
  position: absolute;
  width: 100vw;
  height: 100vh;
  background-color: var(--color-neutral-background);
  z-index: 100000;
}

.offscreen-lsf.visible {
  inset: 0 auto auto 0;
  opacity: 1;
}

.fill-container {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.horizontal-shadow {
  &::after {
    top: 100%;
    width: 100%;
    left: 0;
    content: "";
    position: absolute;
    height: 4px;
    background: linear-gradient(to bottom, var(--black_15), transparent);
  }
}

.crash {
  width: 100vw;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;

  &__header {
    font-size: 22px;
    font-weight: 500;
  }

  &__description {
    margin-top: 32px;
    font-size: 16px;
  }
}

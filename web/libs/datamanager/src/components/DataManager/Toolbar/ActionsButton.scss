.actionButton {
  --compact-height: 32px;

  height: var(--compact-height);
  padding: 0 13px;
  display: flex;
  align-items: center;

  &__titleContainer {
    display: flex;
    align-items: center;
    gap: 8px;
    justify-content: space-between;
  }

  &_isTitle {
    font-weight: 500;
    font-size: 12px;
    line-height: 16px;
    letter-spacing: 0.5px;
    color: var(--grape_500);
    cursor: initial;
    padding: 16px 12px 8px;
    height: var(--compact-height);

    .actionButton {
      &__titleContainer:hover {
        background-color: transparent;
      }

      &__title {
        font-size: 12px;
      }
    }

    &:hover {
      background-color: transparent !important;
      color: var(--grape_500) !important;
    }
  }

  &_isSeparator {
    cursor: initial;
    height: 1px;
    background-color: rgb(137 128 152 / 12%) !important;
    padding: 0;

    .actionButton__titleContainer {
      display: none;
    }

    &.menu__item {
      height: 1px;
    }

    &:hover {
      background-color: transparent;
    }
  }

  &_hasSeperator {
    border-top: 1px solid rgb(137 128 152 / 12%);
  }

  &__title {
    flex-grow: 1;
    font-size: 16px;
  }

  &_hasSubMenu {
    color: var(--color-neutral-content-subtler);

    &:hover {
      color: var(--black);
      background-color: var(--black_4);
    }
  }

  &_danger {
    color: #d00;
  }

  &_disabled {
    background-color: var(--black_4);
    color: rgb(0 0 0 / 25%);
    cursor: initial;

    &:hover {
      background-color: var(--black_4) !important;
      color: rgb(0 0 0 / 25%) !important;
      cursor: initial;
    }
  }
}

.actionButton-submenu {
  margin: 0;
  padding: 0;
  list-style: none;
}

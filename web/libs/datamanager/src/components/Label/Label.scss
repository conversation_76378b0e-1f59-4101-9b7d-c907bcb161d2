@import "../../styles/waiting";

.label-view {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  color: var(--color-neutral-content);

  &__waiting {
    top: 50%;
    left: 50%;
    width: 150px;
    height: 10px;
    z-index: 100;
    position: absolute;
    border-radius: 4px;
    transform: translate(-50%, -50%);
    box-shadow: 0 5px 20px var(--black_20);

    @include waiting(var(--color-primary-surface));
  }

  &__header {
    flex: none;
    display: flex;
    min-height: 56px;
    padding: 0 1em;
    align-items: center;
    justify-content: space-between;
  }

  &__table {
    z-index: 2;
    display: flex;
    height: 100%;
    flex: 200px 0 0;
    position: relative;
    flex-direction: column;

    .resizer__handle {
      top: 0;
      bottom: 0;
    }
  }

  &__content {
    flex: 1;
    display: flex;
    height: calc(100% - 46px);
  }

  &__dataview {
    flex: 1;
    width: 100%;
    height: 100%;
    display: flex;
    min-width: 200px;

    .data-view-dm {
      margin-bottom: 0;
    }
  }

  &__lsf-wrapper {
    flex: 1;
    z-index: 1;
    min-width: 0;
    display: flex;
    position: relative;
    box-sizing: border-box;
    flex-direction: column;

    &_mode {
      &_explorer {
        margin-left: 1px;
        margin-top: -1px;
        border-top: 1px solid var(--black_15);
      }
    }
  }

  &_loading {
    .label-view__lsf-container {
      opacity: 0.2;
      pointer-events: none;
    }
  }

  &__lsf-container {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    overflow-y: auto;

    &>div {
      --main-bg-color: var(--color-neutral-surface);

      flex: 1;
      height: 100%;
    }

  }

  &__lsf-toolbar {
    height: 50px;
  }

  &__lsf-content {
    flex: 1;
    overflow: auto;
    box-sizing: border-box;
    padding-bottom: 50px;
  }
}

.menu-dm {
  flex: 1;
  margin: 0;
  padding: 8px;
  display: flex;
  flex-direction: column;
  list-style-type: none;

  ul,
  li {
    display: block;
    list-style-type: none;
  }

  li {
    margin: 0.5rem;
  }

  li+li {
    margin-top: 0;
  }

  &__item {
    height: 32px;
    display: flex;
    cursor: pointer;
    padding: 0 12px;
    border-radius: 3px;
    align-items: center;
    box-sizing: border-box;
    color: var(--color-neutral-content-subtler);
    font-size: 14px;
    white-space: nowrap;
    user-select: none;

    &-icon {
      margin-right: 10px;
      object-fit: contain;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    &:not(.menu-dm__item_look_danger):hover {
      color: var(--color-neutral-content);
      background-color: var(--color-primary-emphasis-subtle);
    }

    &_active {
      color: var(--color-neutral-content);
      background-color: var(--color-primary-emphasis);
    }

    &_active:not(.sidebar__pin, .menu-dm__item_clickable) {
      pointer-events: none;
    }

    &:hover &-icon,
    &_active &-icon {
      opacity: 1;
    }

    &_look {
      &_danger {
        color: var(--color-negative-content);

        &:hover {
          color: var(--color-neutral-content);
          background-color: var(--color-negative-emphasis-subtle);
        }
      }
    }
  }

  &__spacer {
    flex: 1;
  }

  &__divider {
    height: 1px;
    margin: 8px 0;
    background-color: var(--black_10);
  }

  &_size_compact &__item {
    height: 32px;
    font-size: 16px;
  }

  &_size_small &__item {
    height: 24px;
    font-size: 14px;
    padding: 0 10px;
  }

  &_collapsed {
    padding: 0;
  }
}

.menu-group-dm {
  &__title {
    padding: 4px 10px;
    font-size: 14px;
    color: var(--color-neutral-content-subtle);
  }

  &__list {
    padding: 0;
    margin-left: 10px;
    list-style-type: none;
  }
}

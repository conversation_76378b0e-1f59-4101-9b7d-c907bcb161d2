.counter {
  width: 114px;
  height: 40px;
  display: flex;
  position: relative;
  min-width: 114px;
  border-radius: 8px;
  background: var(--color-neutral-surface);
  box-sizing: border-box;
  box-shadow: 0 0 0 1px var(--color-neutral-border) inset;
  transition: box-shadow 80ms ease;

  &_disabled {
    opacity: 0.6;
    background-color: var(--color-neutral-background);
  }

  &__btn {
    min-width: 32px;
    min-height: 32px;
    margin: 4px;
    border-radius: 4px;
    background: var(--color-neutral-background);
    display: flex;
    color: var(--color-primary-content);
    border: none;
    outline: none;
    align-items: center;
    justify-content: center;
    transition: all 80ms ease;
    box-shadow: 0 1px 0 rgba(var(--color-neutral-shadow-raw) / 24%), 0 0 0 1px rgba(var(--color-neutral-shadow-raw) / 10%), 0 5px 10px rgba(var(--color-neutral-shadow-raw) / 10%);

    &_disabled {
      box-shadow: none;
      background: none;
      pointer-events: none;
      color: var(--color-neutral-content-subtlest);
    }

    &:active {
      background: var(--color-neutral-background);
      box-shadow: 0 1px 0 rgba(var(--color-neutral-shadow-raw) / 24%), 0 0 0 1px rgba(var(--color-neutral-shadow-raw) / 10%), 0 5px 10px rgba(var(--color-neutral-shadow-raw) / 10%);
    }

    &:active,
    &:hover {
      color: var(--color-primary-content-hover);
    }
  }

  &__input {
    flex: 1;
    width: 100%;
    border: none;
    padding: 0;
    z-index: 2;
    background: none;
    text-align: center;
    position: relative;
    outline: none;
    font-size: 16px;
    line-height: 22px;

    &_under {
      left: 40px;
      right: 40px;
      height: 100%;
      width: auto;
      display: flex;
      align-items: center;
      z-index: 1;
      opacity: 0.6;
      position: absolute;
    }

    &_withPostfix {
      text-align: left;
      padding: 0 16px;
    }
  }
}

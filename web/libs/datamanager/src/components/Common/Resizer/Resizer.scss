.resizer {
  --handle-size: 2px;
  --handle-size-hover: 3px;

  & button {
    &:focus {
      outline: none;
      box-shadow: none;
    }
  }

  &__handle {
    top: 25%;
    bottom: 25%;
    position: absolute;
    cursor: ew-resize;
    z-index: 100;
    right: 0;
    width: var(--handle-size);
    background: var(--color-neutral-border);

    &:hover,
    &_resizing {
      width: var(--handle-size-hover);
      background-color: var(--color-primary-border-subtle);
    }
  }
}

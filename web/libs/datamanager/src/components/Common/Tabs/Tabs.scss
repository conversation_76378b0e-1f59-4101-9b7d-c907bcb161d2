.tabs-dm {
  height: 36px;
  display: flex;
  align-items: stretch;
  justify-content: space-between;
  box-shadow: 0 -1px 0 var(--color-neutral-border) inset;
  background: var(--color-neutral-surface-inset);
  padding: 2px 0 0 2px;

  &-content {
    &__draggable {
      align-items: center;
      width: 150px;
      height: 36px;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }

  &__droppable {
    display: flex;
    overflow: hidden;
    gap: 2px;
  }

  &__list {
    display: flex;
    min-width: 0;
    gap: 2px;
  }

  &__item {
    background-color: var(--color-neutral-surface);
    color: var(--color-neutral-content-subtler);
    width: 100%;
    overflow: hidden;
    cursor: pointer;
    position: relative;
    box-shadow: -1px 0 0 var(--color-neutral-border) inset, 1px 0 0 var(--color-neutral-border) inset, 0 1px 0 var(--color-neutral-border-subtle) inset, 0 -3px 0 var(--color-neutral-border) inset;
    border-top-left-radius: var(--corner-radius-small);
    border-top-right-radius: var(--corner-radius-small);

    &_active {
      color: var(--color-neutral-content);
      cursor: default;
      background-color: var(--color-neutral-background);
      box-shadow: -1px 0 0 var(--color-neutral-border) inset, 1px 0 0 var(--color-neutral-border) inset, 0 1px 0 var(--color-neutral-border) inset;

      & .tabs-dm__item-right-button {
        display: flex;
      }
    }

    &_virtual {
      background: linear-gradient(180deg, rgb(243 243 243 / 0%) 0%, #F3F3F3 63.89%), repeating-linear-gradient(-45deg, var(--black_5), var(--black_5) 6px, transparent 6px, transparent 12px), #F3F3F3;
    }

    &_virtual.tabs-dm__item_active {
      background: linear-gradient(180deg, rgb(255 255 255 / 0%) 0%, #FFF 63.89%), repeating-linear-gradient(-45deg, var(--black_5), var(--black_5) 6px, transparent 6px, transparent 12px), var(--white);
    }

    &_hover .tabs-dm__item-right-button {
      display: flex;
    }
  }

  &__item-left {
    overflow: hidden;
    text-overflow: ellipsis;
    height: 36px;
    padding-left: 16px;
    padding-right: 4px;
    display: flex;
    align-items: center;

    &_edit {
      padding-left: 6px;
    }
  }

  &__item-right {
    position: absolute;
    right: 0;
    top: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 36px;
    width: 36px;
    max-width: 60%;
  }

  &__item-right-button {
    align-items: center;
    justify-content: center;
    display: none;
    height: 36px;
    width: 36px;
    margin: none;
    padding: none;
  }

  &__add {
    padding: 0;
    border: none;
    outline: none;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    align-self: center;
    color: var(--color-neutral-content-subtler);
    background: none;
    transition: all 150ms ease-out;

    &:focus {
      box-shadow: none;
    }

    &:hover {
      background-color: var(--color-primary-emphasis-subtle);
      color: var(--color-primary-content);
      border: 0;
    }
  }

  &__extra {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    position: absolute;
    bottom: 0;
    z-index: 1000;
    width: 100%;
    right: 0;
    left: 0;
    background: var(--color-neutral-background);
    border-top: 1px solid var(--color-neutral-border);
    padding: .625rem 1rem;
  }
}

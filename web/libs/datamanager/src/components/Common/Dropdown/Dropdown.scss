.dropdown-dm {
  --menu-animation-duration: 0.15s;
  --menu-animation-curve: cubic-bezier(0.21, 1.04, 0.68, 1);
  --menu-animation-start: -10px;

  top: calc(100% + 1px);
  z-index: 500;
  display: none;
  position: absolute;
  box-sizing: border-box;
  background-color: var(--color-neutral-background);
  box-shadow: 0 4px 16px rgba(var(--color-neutral-shadow-raw) / calc( 12% * var(--shadow-intensity) )), 0 2px 4px rgba(var(--color-neutral-shadow-raw) / calc(8% * var(--shadow-intensity)));
  will-change: transform, opacity;
  border-radius: var(--corner-radius-small);

  &_align {
    &_left {
      left: -20px;
    }

    &_right {
      right: -20px;
    }
  }

  &__trigger {
    position: relative;
    padding-right: 8px;
  }

  &.before-appear,
  &.before-disappear {
    transition-property: opacity, transform;
    transition-duration: var(--menu-animation-duration);
    transition-timing-function: var(--menu-animation-curve);
  }

  &.before-appear {
    opacity: 0;
    display: flex;
    transform: translate3d(0, var(--menu-animation-start), 0);
  }

  &.appear {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }

  &.visible {
    opacity: 1;
    display: flex;
  }

  &.before-disappear {
    opacity: 1;
    display: flex;
    transform: translate3d(0, 0, 0);
  }

  &.disappear {
    opacity: 0;
    transform: translate3d(0, var(--menu-animation-start), 0);
    z-index: -1 !important;
  }
}

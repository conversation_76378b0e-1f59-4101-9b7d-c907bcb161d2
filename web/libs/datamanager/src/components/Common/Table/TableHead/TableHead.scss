.table-head {
  display: flex;
  width: 100%;
  height: 50px;
  z-index: 150;
  font-weight: 500;
  overflow: visible;
  background-color: var(--color-neutral-background);
  min-width: fit-content;
  font-size: 14px;
  border-bottom: 1px solid var(--color-neutral-border);
  color: var(--color-neutral-content);
  position: sticky;

  &__extra {
    flex: 1;
    display: flex;
    padding-right: 14px;
    align-items: center;
    justify-content: flex-end;
  }

  &__column-extra {
    margin: 0 2px 0 10px;
    display: flex;
    align-items: center;
  }

  &__draggable {
    --scale: 1;

    transform: scale(var(--scale));
    cursor: move;
  }
}

.pagination-dm {
  --button-color: var(--accent_color);
  --border-radius: 5px;
  --height: 40px;
  --font-size: 16px;
  --line-height: 19px;

  height: var(--height);
  display: inline-flex;
  align-items: center;

  &_size {
    &_small {
      --height: 24px;
      --border-radius: 3px;
      --font-size: 12px;
      --line-height: 15px;
    }
  }

  &_disabled {
    --button-color: #ccc;

    background-color: #efefef;
    pointer-events: none;
  }

  &__label {
    padding-right: 16px;
    color: var(--black_40);
    font-size: var(--font-size);
    line-height: var(--line-height);

    .pagination-dm_size_small & {
      padding-right: 8px;
    }
  }

  &__navigation {
    display: flex;
    height: 100%;
    align-items: center;
    border-radius: var(--border-radius, 5px);
    background: var(--white);
    box-shadow: inset 0 -1px 0 var(--black_10), inset 0 0 0 1px var(--black_15);

    .pagination-dm_waiting & {
      pointer-events: none;
      animation: button-waiting 1s linear infinite;
      background-image: repeating-linear-gradient(-63.43deg,
          rgb(var(--white-raw) / 20%) 1px, #efefef 2px, #efefef 6px,
          rgb(var(--white-raw) / 20%) 7px,
          rgb(var(--white-raw) / 20%) 12px);
    }
  }

  &__divider {
    width: 1px;
    height: 30px;
    background-color: var(--black_10);

    .pagination-dm_size_small & {
      height: 20px;
    }
  }

  &__btn {
    width: 37px;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    user-select: none;
    cursor: pointer;

    .pagination-dm_waiting & {
      opacity: 0.5;
    }

    .pagination-dm_size_small & {
      width: 24px;
    }

    &_disabled {
      pointer-events: none;

      --button-color: #ccc;
    }

    &_arrow-left,
    &_arrow-right {
      position: relative;

      &::before,
      &::after {
        content: '';
        display: block;
        position: absolute;
        left: 50%;
        top: 50%;
        width: 9px;
        height: 9px;
        border: 2px solid var(--button-color);
        border-bottom: none;
        border-right: none;
      }

      &::after {
        display: none;
      }

      .pagination-dm_size_small & {
        &::before,
        &::after {
          width: 5px;
          height: 5px;
        }
      }
    }

    &_arrow-left {
      &::before,
      &::after {
        transform: translate3d(-50%, -50%, 0) rotate(-45deg);
      }
    }

    &_arrow-right {
      &::before,
      &::after {
        transform: translate3d(-50%, -50%, 0) rotate(135deg);
      }
    }

    &_arrow-left-double {
      &::after {
        display: block;
        margin-left: 7px;
      }
    }

    &_arrow-right-double {
      &::after {
        display: block;
        margin-left: -7px;
      }
    }
  }

  &__input {
    width: 100px;
    height: calc(100% - 2px);
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid #D9D9D9;
    border-top: none;
    border-bottom: none;
    background: #FAFAFA;
    margin: 1px 0;

    .pagination-dm_waiting & {
      opacity: 0.5;
    }

    .pagination-dm_size_small & {
      width: 70px;
    }

    input {
      width: 100%;
      height: calc(100% - 2px);
      border: none;
      margin: 1px 0;
      padding: 0;
      outline: none;
      text-align: center;
      background: none;
      font-weight: 500;
      font-size: var(--font-size);
      line-height: var(--line-height);
    }
  }

  &__page-indicator {
    flex: 1;
    user-select: none;
    font-weight: 500;
    font-size: var(--font-size);
    line-height: var(--line-height);

    span {
      font-weight: normal;
      opacity: 0.4;
    }
  }

  &__page-size {
    min-width: 170px;
    padding-left: 16px;

    .pagination-dm_size_small & {
      padding-left: 8px;
      min-width: 130px;
    }
  }
}

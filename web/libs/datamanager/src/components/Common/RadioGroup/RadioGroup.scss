.radio-group-dm {
  --radius: var(--corner-radius-small);
  --height: 40px;
  --padding: 3px;
  --font-size: 16px;
  --button-padding: 0 10px;
  --button-checked-shadow: 0 1px 0 var(--black_10), 0 0 0 1px var(--black_2), 0 5px 10px var(--black_15);

  height: var(--height);
  border-radius: var(--radius);
  padding: var(--padding);
  background: linear-gradient(0deg, var(--color-neutral-surface), var(--color-neutral-surface)), var(--color-neutral-background);
  border: 1px solid var(--color-neutral-border);

  &__buttons {
    height: calc(var(--height) - calc(var(--padding) * 2));
    display: grid;
    grid-auto-columns: 1fr;
    grid-auto-flow: column;
  }

  &__button {
    display: flex;
    opacity: 0.6;
    padding: var(--button-padding);
    font-weight: 500;
    position: relative;
    text-align: center;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size);
    border-radius: calc(var(--radius) - var(--padding));
    height: calc(var(--height) - calc(var(--padding) * 2) - 2px);
    color: var(--color-neutral-content-subtler);
    transition: all 100ms ease-out;
    cursor: pointer;

    &_checked {
      opacity: 1;
      background-color: var(--color-neutral-surface-hover);
      box-shadow: var(--button-checked-shadow);
      color: var(--color-neutral-content);
    }

    &_disabled {
      opacity: 0.3;
      color: var(--color-neutral-subtlest);
      cursor: not-allowed;
    }

    & input{
      cursor: pointer;
    }
  }

  &__input {
    top: 0;
    left: 0;
    opacity: 0;
    width: 100%;
    height: 100%;
    position: absolute;
  }

  &_size {
    &_medium {
      --height: 32px;
      --radius: 8px;
      --font-size: 14px;
    }

    &_small {
      --height: 24px;
      --radius: 4px;
      --padding: 2px;
      --font-size: 12px;
      --button-padding: 0 5px;
      --button-checked-shadow: 0 1px 0 var(--black_10), 0 0 0 1px var(--black_2), 0 2px 4px var(--black_15);
    }
  }
}

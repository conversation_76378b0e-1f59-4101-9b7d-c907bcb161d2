.range {
  display: inline-flex;
  align-items: center;
  user-select: none;

  &_align {
    &_horizontal {
      min-width: 120px;
      height: 28px;
      flex-direction: row;
    }

    &_vertical {
      min-height: 120px;
      width: 28px;
      flex-direction: column;
    }

    .range_with-icon &_horizontal {
      padding: 0 5px;
    }

    .range_with-icon &_vertical {
      padding: 5px 0;
    }
  }

  &__body {
    position: relative;

    .range_align_horizontal & {
      height: 4px;
      width: 100%;
    }

    .range_align_vertical & {
      height: 100%;
      width: 4px;
    }

    .range_align_horizontal &_with-icon {
      margin: 0 5px;
    }

    .range_align_vertical &_with-icon {
      margin: 5px 0;
    }
  }

  &__line {
    background: linear-gradient(0deg, var(--color-neutral-surface-active), var(--color-neutral-surface-active)), #FFF;
    box-shadow: inset 0 1px 0 rgba(var(--color-neutral-shadow-raw) / 5%), inset 0 0 0 1px rgba(var(--color-neutral-shadow-raw) / 5%);
    border-radius: 8px;

    .range_align_horizontal & {
      height: 4px;
      width: 100%;
    }

    .range_align_vertical & {
      width: 4px;
      height: 100%;
    }
  }

  &__indicator {
    border-radius: 8px;
    position: absolute;
    background-color: var(--color-primary-emphasis);

    .range_align_horizontal & {
      top: 0;
      height: 4px;
    }

    .range_align_vertical & {
      left: 0;
      width: 4px;
    }
  }

  &__range-handle {
    width: 0;
    height: 0;
    position: absolute;

    &::before {
      content: '';
      position: absolute;
      background: var(--color-primary-surface);
      box-shadow: 0 5px 10px rgba(var(--color-primary-shadow-raw) / 30%);
      border-radius: 8px;
      transform: translate3d(-50%, -50%, 0);
    }

    &::after {
      content: '';
      width: 25px;
      height: 25px;
      position: absolute;
      transform: translate3d(-50%, -50%, 0);
    }

    .range_align_horizontal & {
      top: 50%;
      cursor: col-resize;

      &::before {
        width: 16px;
        height: 16px;
      }
    }

    .range_align_vertical & {
      left: 50%;
      cursor: row-resize;

      &::before {
        width: 16px;
        height: 16px;
      }
    }
  }

  &__icon {
    width: 28px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

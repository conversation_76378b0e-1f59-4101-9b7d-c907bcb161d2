.input-dm,
.textarea-dm {
  width: 100%;
  background: var(--color-neutral-background);
  font-size: 14px;
  border: 1px solid var(--color-neutral-border);
  box-sizing: border-box;
  border-radius: 5px;
  padding: 0 16px;
  transition: box-shadow 80ms ease;
}

.input-dm {
  &_size {
    &_compact {
      height: 32px;
    }

    &_small {
      height: 24px;
      font-size: 12px;
      padding: 0 5px;
    }

    &_large {
      height: 40px;
      font-size: 16px;
    }
  }
}

.textarea-dm {
  padding: 12px 16px;
  min-height: 50px;
}

.input-dm:focus,
.textarea-dm:focus {
  outline: none;
  box-shadow: 0 0 0 6px var(--color-primary-focus-outline), inset 0 -1px 0 var(--black_10), inset 0 0 0 1px var(--black_15), inset 0 0 0 1px rgb(var(--accent_color-raw) / 20%);
  border-color: var(--color-primary-border);
}

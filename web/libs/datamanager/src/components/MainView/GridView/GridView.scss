
.grid-view {
  flex: 1;

  &__resize {
    width: 100%;
  }

  &__list {
    padding: 0 0 16px;
  }

  &_columnCount {
    @for $i from 1 to 15 {
      &_#{ $i } {
        & .grid-view__cell:nth-child(#{$i}n) {
          padding-right: 0;
        }
      }
    }
  }

  &__cell {
    padding: 16px 16px 0 0;
    box-sizing: border-box;
    transition: all 150ms ease-out;

    &-header {
      padding: 5px;
      width: 100%;
      display: flex;
      background: var(--color-neutral-background);
      justify-content: space-between;
      transition: all 150ms ease-out;
    }

    &-content {
      width: 100%;
      height: 100%;
      cursor: pointer;
      overflow: hidden;
      position: relative;
      border-radius: 8px;
      background: none;
      box-shadow: 0 0 0 4px transparent;
      transition: all 150ms ease-out;

      &::after {
        inset: 0;
        content: "";
        position: absolute;
        pointer-events: none;
        border-radius: 8px;
        border: 1px solid var(--color-neutral-border);
        transition: all 150ms ease-out;
      }

      &:hover {
        background-color: var(--color-primary-emphasis-subtle);
        box-shadow: 0 0 0 4px var(--color-primary-focus-outline);

        &::after {
          border: 1px solid var(--color-primary-border);
        }
      }
    }

    

    &_selected &-header {
      background-color: var(--color-primary-emphasis);
    }

    &_selected &-content {
      background-color: var(--color-primary-emphasis-subtle);
      box-shadow: 0 0 0 4px var(--color-primary-focus-outline);

      &::after {
        border-color: var(--color-primary-border-subtle);
      }
    }
  }

  .skeletonLoader {
    padding: 5px;
  }

  & .grid-image-wrapper {
    text-align: center;
    background: var(--color-neutral-surface);
  }
}

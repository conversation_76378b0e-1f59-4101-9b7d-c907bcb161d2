.modal {
  padding: 16px;
  position: relative;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-sizing: content-box;
  margin-bottom: 16px;
}

.tooltip {
  font-size: 12px;
  max-width: 300px;
  line-height: 16px;

  p {
    margin-bottom: 4px;
  }

  p:last-child {
    margin-bottom: 0;
  }
}

.actions {
  margin-left: auto;

  & > * {
    width: 20px;
    margin-left: 16px;
    text-align: center;
    cursor: pointer;
  }
}

.container {
  overflow: hidden;
  width: 100%;
  height: 100%;
  min-height: 100px;
  display: flex;
  position: relative;
}

.container button {
  padding: 0;
  flex: 20px 0 0;
  cursor: pointer;
  background: none;
  border-radius: var(--corner-radius-small);
  transition: all 150ms ease-out;

  &:hover {
    background: var(--color-primary-emphasis-subtle);
    color: var(--color-neutral-content);
  }
}

.image {
  pointer-events: none;
  user-select: none;
}

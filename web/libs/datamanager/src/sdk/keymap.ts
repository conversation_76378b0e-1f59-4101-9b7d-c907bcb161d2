export const keymap = {
  "dm.focus-previous": {
    title: "Focus previous task",
    shortcut: "shift+up",
  },
  "dm.focus-next": {
    title: "Focus previous task",
    shortcut: "shift+down",
  },
  "dm.close-labeling": {
    title: "Focus previous task",
    shortcut: "shift+left",
  },
  "dm.open-labeling": {
    title: "Focus previous task",
    shortcut: "shift+right",
  },
  "dm.toggle-bulk-sidebar-minimization": {
    title: "Toggle bulk sidebar minimization",
    shortcut: "shift+.",
  },
  "lsf.save-annotation": {
    title: "Save results",
    macos: "cmd+enter",
    other: "ctrl+enter",
  },
  "lsf.reject-task": {
    title: "Mark task as cancelled",
    macos: "cmd+shift+enter",
    other: "ctrl+space",
  },
  "lsf.undo": {
    title: "Undo last action",
    macos: "cmd+z",
    other: "ctrl+z",
  },
  "lsf.redo": {
    title: "Redo last action",
    macos: "cmd+shift+z",
    other: "ctrl+shidt+z",
  },
};

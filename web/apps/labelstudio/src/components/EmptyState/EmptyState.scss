.empty-state-default {
  max-width: 40rem;
  background: var(--color-primary-background);
  border: 1px solid var(--color-primary-border-subtlest);
  padding: 2rem;
  border-radius: 0.5rem;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;

  &__icon {
    margin-bottom: 0.75rem;
    background: var(--color-primary-emphasis);
    padding: 0.5rem;
    display: flex;
    border-radius: 2rem;

    & svg path {
      fill: var(--color-primary-icon);
    }
  }

  &__action {
    margin-bottom: 1.25rem;
  }

  &__title {
    font-size: 1.75rem;
    font-weight: 500;
    color: var(--color-primary-content);
    margin-bottom: 1rem;
    text-align: center;
  }

  &__description {
    font-size: 1rem;
    color: var(--color-neutral-content-subtle);
    margin-bottom: 1rem;
    text-align: center;
    line-height: 1.5em;
  }

  &__footer {
    font-size: 0.75rem;
    color: var(--color-neutral-content);
    text-align: center;
    line-height: 1.5em;
  }

  &__footer a {
    color: var(--grape_700);
    text-decoration: underline;

    &:hover {
      text-decoration: none;
    }
  }
}
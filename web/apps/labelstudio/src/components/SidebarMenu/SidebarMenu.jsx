import { cn } from "../../utils/bem";
import { Menu } from "../Menu/Menu";
import "./SidebarMenu.scss";

export const SidebarMenu = ({ children, menu, path, menuItems }) => {
  const rootClass = cn("sidebar-menu");

  // 调试信息
  console.log('SidebarMenu - menuItems:', menuItems);
  console.log('SidebarMenu - menuItems.length:', menuItems?.length);
  console.log('SidebarMenu - path:', path);

  return (
    <div className={rootClass}>
      {menuItems && menuItems.length > 0 ? (
        <div className={rootClass.elem("navigation")}>
          <Menu>{menuItems ? Menu.Builder(path, menuItems) : menu}</Menu>
        </div>
      ) : null}
      <div className={rootClass.elem("content")}>{children}</div>
    </div>
  );
};

.radio-group-ls {
  --radius: 8px;
  --padding: 4px;
  --font-size: 16px;
  --button-padding: 0 10px;
  --button-checked-shadow: 0 1px 0 rgb(0 0 0 / 10%), 0 0 0 1px rgb(0 0 0 / 2%), 0 5px 10px rgb(0 0 0 / 15%);

  border-radius: var(--radius);
  padding: var(--padding);
  background: var(--color-neutral-surface);
  border: 1px solid var(--color-neutral-border);
  box-sizing: border-box;

  &__buttons {
    height: calc(var(--height) - calc(var(--padding) * 2));
    display: grid;
    grid-auto-columns: 1fr; 
    grid-auto-flow: column;
    gap: 2px;
  }

  .radio-group-ls_horizontal &__buttons {
    display: grid;
    grid-auto-columns: min-content;
    column-gap: 16px;
    align-items: center;
    grid-auto-flow: column;
    margin: 0;
  }

  .radio-group-ls_simple &__buttons {
    all: unset;
    display: inline-block;
    margin-bottom: 16px;
  }

  & .label-ls__text {
    font-weight: 500;

    &::before {
      content: '';
      background: var(--color-neutral-background);
      border-radius: 100%;
      border: 1px solid var(--color-neutral-border);
      display: inline-block;
      width: 1.4em;
      height: 1.4em;
      position: relative;
      top: -10px;
      margin-right: 0.25rem;
      vertical-align: top;
      cursor: pointer;
      text-align: center;
      transition: all 300ms ease-out;
      box-shadow: inset 0 0 0 8px var(--color-neutral-background);
    }

    &:hover {
      &::before {
        border-color: var(--color-neutral-border-bold);
      }
    }
  }

  & input[type="radio"] {
    display: none;
  }

  &__button {
    display: flex;
    opacity: 0.6;
    padding: var(--button-padding);
    cursor: pointer;
    font-weight: 500;
    position: relative;
    text-align: center;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size);
    border-radius: 4px;
    height: calc(var(--height) - calc(var(--padding) * 2));
    transition: all 150ms ease-out;

    &:hover {
      opacity: 1;

      & .label-ls__text {
        &::before {
          box-shadow: inset 0 0 0 4px var(--color-neutral-background);
        }
      }
    }

    &_checked {
      opacity: 1;
      background-color: var(--color-neutral-surface-hover);
      box-shadow: var(--button-checked-shadow);

      & .label-ls__text {
        &::before {
          background-color: var(--color-primary-surface);
          box-shadow: inset 0 0 0 4px var(--color-neutral-background);
        }
      }
    }

    &_disabled {
      opacity: 0.3;
      cursor: not-allowed;
    }
  }

  .radio-group-ls_horizontal &__button {
    margin: 0;
  }

  .radio-group-ls_simple &__button {
    all: unset;
    display: block;
    margin-bottom: 0.5rem !important;
  }

  &__input {
    top: 0;
    left: 0;
    opacity: 0;
    width: 100%;
    height: 100%;
    position: absolute;
  }

  &_size {
    &_large {
      --height: 40px;
      --radius: 8px;
    }

    &_compact {
      --height: 32px;
      --radius: 8px;
    }

    &_small {
      --height: 24px;
      --radius: 4px;
      --padding: 2px;
      --font-size: 12px;
      --button-padding: 0 5px;
      --button-checked-shadow: 0 1px 0 rgb(0 0 0 / 10%), 0 0 0 1px rgb(0 0 0 / 2%), 0 2px 4px rgb(0 0 0 / 15%);
    }
  }

  &_simple {
    --height: auto;

    all: unset;
    display: block;
  }
}
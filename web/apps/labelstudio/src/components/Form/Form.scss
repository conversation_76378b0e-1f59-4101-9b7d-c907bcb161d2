.form {
  width: 100%;
  display: block;

  &__row {
    display: grid;
    justify-items: stretch;
    justify-content: space-between;
    grid-template-columns: repeat(var(--column-count, 5), 1fr);
    grid-gap: var(--row-gap, 8px) 8px;

    &:not(:first-child) {
      margin-top: 12px;
    }

    &__description {
      font-size: 0.875rem;
    }
  }

  &__submit {
    display: flex;
    margin-top: 32px;
    align-items: center;
    justify-content: space-between;

    & + .inline-error {
      margin-top: 32px;
    }

    &_size {
      &_small {
        margin-top: 16px;
      }
    }
  }

  &__info {
    display: flex;
    align-items: center;
    color: var(--color-negative-content);
    font-size: 14px;
    line-height: 22px;

    &_valid {
      color: var(--color-neutral-content-subtler);
    }
  }

  &__column {
    display: grid;
    grid-auto-flow: column;
    align-items: flex-start;
  }
}

.input-ls,
.textarea-ls,
.counter,
.select-ls__list {
  transition: all 100ms ease-out;
  outline: 0;
  color: var(--color-neutral-content);

  &:not(&_ghost):focus,
  &:not(:read-only):focus,
  &_focused {
    outline: 4px solid var(--color-primary-focus-outline);

    // box-shadow: 0 0 0 6px var(--color-primary-surface-content-subtle), inset 0 -1px 0 rgb(0 0 0 / 10%), inset 0 0 0 1px rgb(0 0 0 / 15%), inset 0 0 0 1px var(--color-primary-surface-content-subtle);
    border-color: var(--color-neutral-border-bolder);
  }

  &:focus-visible {
    outline: none;
  }

  &:read-only:focus {
    box-shadow: none;
    border-color: var(--border-color);
  }
}

.form-indicator {
  font-weight: 500;

  &__item {
    &_type {
      &_success {
        color: var(--color-positive-content);
      }

      &_fail {
        color: var(--color-negative-content);
      }
    }
  }
}

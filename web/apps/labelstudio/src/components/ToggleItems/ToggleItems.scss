.toggle-items {
  --button-checked-shadow: 0 1px 0 rgba(var(--color-neutral-shadow-raw) / 10%), 0 5px 10px rgba(var(--color-neutral-shadow-raw) / 10%);

  display: flex;
  justify-content: stretch;
  list-style: none;
  padding: 4px;
  margin: 0;
  background: var(--color-neutral-surface);
  border: 1px solid var(--color-neutral-border);
  border-radius: var(--corner-radius-small);
  font-weight: 500;

  &_big {
    font-size: 16px;
  }

  &_big &__item {
    padding: 4px 20px;
  }

  &__item {
    cursor: pointer;
    color: var(--color-neutral-content-subtler);
    border-radius: 4px;
    padding: 2px 16px;
    flex-grow: 1;
    text-align: center;

    &_active {
      color: var(--color-neutral-content);
      background: var(--color-neutral-surface-hover);
      box-shadow: var(--button-checked-shadow);
    }
  }
}

import { useTranslation } from 'react-i18next';
import { cn } from '../../utils/bem';
import './LanguageSwitcher.scss';

const LanguageSwitcher = () => {
  const { i18n } = useTranslation();
  
  const languages = [
    { code: 'en', name: 'English' },
    { code: 'zh', name: '中文' }
  ];

  const currentLanguage = i18n.language;
  const languageClass = cn('language-switcher');

  const handleLanguageChange = (langCode) => {
    i18n.changeLanguage(langCode);
  };

  return (
    <div className={languageClass}>
      {languages.map((lang) => (
        <button
          key={lang.code}
          className={languageClass.elem('button').mod({ 
            active: currentLanguage === lang.code 
          })}
          onClick={() => handleLanguageChange(lang.code)}
        >
          {lang.name}
        </button>
      ))}
    </div>
  );
};

export default LanguageSwitcher;

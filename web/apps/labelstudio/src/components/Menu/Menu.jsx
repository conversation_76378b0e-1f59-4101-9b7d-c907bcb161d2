import { forwardRef, useCallback, useMemo } from "react";
import { useTranslation } from "react-i18next";
import { cn } from "../../utils/bem";
import { useDropdown } from "../Dropdown/Dropdown";
import "./Menu.scss";
import { Block, Elem, MenuContext } from "./MenuContext";
import { MenuItem } from "./MenuItem";

export const Menu = forwardRef(
  ({ children, className, style, size, selectedKeys, closeDropdownOnItemClick, contextual }, ref) => {
    const dropdown = useDropdown();

    const selected = useMemo(() => {
      return new Set(selectedKeys ?? []);
    }, [selectedKeys]);

    const clickHandler = useCallback(
      (e) => {
        const elem = cn("main-menu").elem("item").closest(e.target);

        if (dropdown && elem && closeDropdownOnItemClick !== false) {
          dropdown.close();
        }
      },
      [dropdown],
    );

    const collapsed = useMemo(() => {
      return !!dropdown;
    }, [dropdown]);

    return (
      <MenuContext.Provider value={{ selected }}>
        <Block
          ref={ref}
          tag="ul"
          name="main-menu"
          mod={{ size, collapsed, contextual }}
          mix={className}
          style={style}
          onClick={clickHandler}
        >
          {children}
        </Block>
      </MenuContext.Provider>
    );
  },
);

Menu.Item = MenuItem;
Menu.Spacer = () => <Elem block="main-menu" tag="li" name="spacer" />;
Menu.Divider = () => <Elem block="main-menu" tag="li" name="divider" />;
// 翻译菜单项组件
const TranslatedMenuItem = ({ item, url, index }) => {
  const { t } = useTranslation();

  let pageLabel;
  let pagePath;

  if (Array.isArray(item)) {
    [pagePath, pageLabel] = item;
  } else {
    const { menuItem, title, path } = item;
    try {
      if (typeof menuItem === 'function') {
        pageLabel = title ?? menuItem();
      } else if (typeof menuItem === 'string') {
        pageLabel = title ?? t(menuItem);
      } else {
        pageLabel = title ?? menuItem;
      }
    } catch (error) {
      console.warn('Error processing menuItem:', error);
      pageLabel = 'Menu Item';
    }
    pagePath = path;
  }

  if (typeof pagePath === "function") {
    return (
      <Menu.Item key={index} onClick={pagePath}>
        {pageLabel}
      </Menu.Item>
    );
  }

  const location = `${url}${pagePath}`.replace(/([/]+)/g, "/");

  return (
    <Menu.Item key={index} to={location} exact>
      {pageLabel}
    </Menu.Item>
  );
};

Menu.Builder = (url, menuItems) => {
  console.log('Menu.Builder - url:', url, 'menuItems:', menuItems);
  return (menuItems ?? []).map((item, index) => {
    if (item === "SPACER") return <Menu.Spacer key={index} />;
    if (item === "DIVIDER") return <Menu.Divider key={index} />;

    return <TranslatedMenuItem key={index} item={item} url={url} index={index} />;
  });
};

Menu.Group = ({ children, title, className, style }) => {
  return (
    <Block name="menu-group" mix={className} style={style}>
      <Elem name="title">{title}</Elem>
      <Elem tag="ul" name="list">
        {children}
      </Elem>
    </Block>
  );
};

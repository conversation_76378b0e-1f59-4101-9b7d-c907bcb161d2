.main-menu {
  flex: 1;
  margin: 0;
  padding: 8px;
  display: flex;
  flex-direction: column;
  list-style-type: none;
  max-width: 100%;
  box-sizing: border-box;
  background: var(--color-neutral-background);
  border-radius: 0;
  transition: background-color 400ms ease-out;
  border-right: 1px solid var(--color-neutral-border);
  gap: 2px;

  &__item {
    height: 40px;
    display: flex;
    padding: 0 13px;
    border-radius: var(--corner-radius-smaller);
    align-items: center;
    box-sizing: border-box;
    color: var(--color-neutral-content-subtler);
    font-size: 1rem;
    white-space: nowrap;
    text-decoration: none;
    cursor: pointer;
    transition: all 150ms ease-out;

    &-icon {
      margin-right: 10px;
      object-fit: contain;
      opacity: 0.5;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 24px;
    }

    &-beta {
      background-color: var(--color-accent-plum-base);
      color: var(--color-accent-plum-subtlest);
      font-size: 12px;
      font-style: normal;
      font-weight: 500;
      line-height: 16px;
      margin-left: 8px;
      padding: 2px 8px;
      border-radius: 12px;
    }

    &_look {
      &_danger {
        color: var(--color-negative-content);
      }
    }

    &:not(.main-menu__item_look_danger):hover {
      color: var(--color-neutral-content);
      background: var(--color-primary-emphasis-subtle);
    }

    &:not(.main-menu__item_active):hover {
      color: var(--color-neutral-content);
      background: var(--color-primary-emphasis-subtle);
    }

    &_active {
      color: var(--color-neutral-content);
      font-weight: 500;
    }

    &_active:not(.sidebar__pin) {
      pointer-events: none;
      background: var(--color-neutral-emphasis);
    }

    &:hover &-icon,
    &_active &-icon {
      opacity: 1;
    }

    &_dangerous {
      color: var(--color-negative-content);

      &:hover {
        color: var(--color-neutral-content) !important;
        background-color: var(--color-negative-emphasis-subtle) !important;
      }
    }
  }

  &__spacer {
    flex: 1;
  }

  &__divider {
    height: 1px;
    margin: 8px 0;
    background-color: var(--color-neutral-border);
    transition: background-color 150ms ease-out;
  }

  &_size_compact {
    background: var(--color-neutral-background);
  }

  &_size_compact &__item,
  &_size_medium &__item {
    height: 32px;
    font-size: 16px;
  }

  &_size_small &__item {
    height: 24px;
    font-size: 14px;
    padding: 0 10px;
  }

  &_collapsed {
    padding: 0.5rem;

    &__item {
      border-radius: 0.25rem;
    }
  }

  &_contextual {
    box-shadow: 0 1px 2px rgb(38 38 38 / 30%), 0 1px 3px 1px rgb(38 38 38 / 15%);
    border-radius: 4px;
  }

  &_contextual &__item {
    height: 32px;
    padding: 16px 8px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-radius: 4px;
    cursor: pointer;

    &:hover {
      background-color: var(--color-primary-emphasis-subtle);
    }

    &_dangerous {
      color: var(--color-negative-content);

      &:hover {
        color: var(--color-neutral-content);
        background-color: var(--color-negative-emphasis-subtle);
      }
    }
  }

  &:first-child {
    padding-top: 8px;
  }

  &:last-child {
    padding-bottom: 8px;
  }
}

.menu-group {
  &__title {
    padding: 4px 10px;
    font-size: 14px;
    color: var(--color-neutral-content-subtler);
  }

  &__list {
    padding: 0;
    margin-left: 10px;
    list-style-type: none;
  }
}

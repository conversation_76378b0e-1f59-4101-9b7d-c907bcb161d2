$scroll-width: 5px;

@mixin styled-scroll() {
  &::-webkit-scrollbar {
    width: 6px;
    height: 6px;
    background-color: var(--color-neutral-border-bolder);
  }

  &::-webkit-scrollbar-thumb {
    background-color: var(--color-neutral-surface);
  }
}

.panel {
  display: none;
}

.wizard {
  flex: 1;
  min-height: 0;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.sidebar-menu__content > .wizard {
  height: calc(100% + 64px);
  margin: -32px -40px;
}

.wizard > .configure {
  flex: 1;
  min-height: 0;
  display: flex;
  align-items: stretch;

  > * {
    flex: 50%;
  }
}

.wizard .configure__container {
  display: flex;
  flex-direction: column;
  padding: 16px 16px - $scroll-width 16px 20px;
  overflow-y: scroll;
  background-color: var(--color-neutral-background);

  &::-webkit-scrollbar {
    width: $scroll-width;
  }

  &::-webkit-scrollbar-track {
    background: none;
  }
}

.templates-list {
  display: grid;
  height: 100%;
  grid-template: 1fr auto / 224px auto;

  &__sidebar {
    padding: 16px 8px 16px 32px;

    h3:not(:first-child) {
      margin-top: 2em;
    }

    .templates-list__custom-template {
      margin-top: 20px;
      padding-left: 8px;
      font-weight: 500;
      border: none;
      color: var(--grape_500);
      background: none;
      width: 100%;
      text-align: left;
    }

    ul {
      padding: 0;
      list-style: none;
      display: flex;
      flex-direction: column;
    }
  }

  &__group {
    margin: 0 0 4px;
    padding: 4px 8px;
    border-radius: 4px;
    cursor: pointer;
    color: var(--color-neutral-content-subtler);
    font-weight: 500;
    line-height: 24px;
    display: flex;
    align-items: center;
    transition: all 150ms ease-out;

    &:not(&_active):hover {
      background-color: var(--color-primary-emphasis-subtle);
      color: var(--color-neutral-content);
    }

    &_active {
      color: var(--color-neutral-content);
      background-color: var(--color-neutral-surface-active);
      cursor: default;
    }

    svg {
      margin-left: auto;
      padding-left: 16px;
      width: 24px;
      flex-shrink: 0;
    }
  }

  main {
    position: relative;
    flex-grow: 1;
    overflow-y: auto;
    height: 100%;

    ul {
      display: grid;
      flex-wrap: wrap;
      padding: 0;
      margin: 16px 8px;
      grid-template-columns: repeat(auto-fill, 276px);
      grid-auto-columns: 276px;
      grid-gap: 16px;
    }
  }

  &__template {
    display: flex;
    flex-direction: column;
    border-radius: 4px;
    cursor: pointer;
    position: relative;
    width: 276px;
    border: 1px solid var(--color-neutral-border);
    box-sizing: content-box;
    transition: all 300ms ease-out;
    overflow: hidden;

    &_active,
    &:hover {
      box-shadow: 0 0 0 6px var(--color-primary-focus-outline);
      background: var(--color-neutral-surface-hover);
      border-color: var(--color-primary-border-bolder);
    }

    &:hover {
      opacity: 0.9;
    }

    h3 {
      margin: 0;
      padding: 12px;
      font-size: 14px;
      font-weight: 500;
      border-top: 1px solid var(--color-neutral-content);
      color: var(--color-neutral-content);
    }

    img {
      width: 100%;
      object-fit: cover;
      border-radius: 4px 4px 0 0;
    }
  }

  footer,
  .modal__footer {
    grid-column: 1 / span 2;
    padding: 16px 32px;
    font-size: 14px;
    background: var(--color-neutral-surface-inset);
    box-shadow: inset 0 1px 0 rgb(0 0 0 / 5%);
    text-align: center;
    color: var(--color-neutral-content-subtler);

    & a {
      color: var(--color-primary-content-hover);
      text-decoration: underline;
    }
  }

  &__info-icon {
    vertical-align: -5px;
    height: 20px;
  }
}

.wizard a.back {
  align-self: center;
  width: 220px;
  height: 30px;
}

.wizard a.back::before {
  content: '\2190';
  margin-right: 8px;
}

.wizard .project__create {
  display: none;
}

.wizard .project__create p {
  margin: 0;
}

.wizard h1 {
  font-size: 1.75rem;
  width: 100%;
  display: flex;
  color: var(--color-neutral-content);
}

.wizard h1 a[name="config"] {
  margin-left: auto;
  font-size: 1rem;
  font-weight: 400;
  background: var(--color-neutral-surface-inset);
}

.wizard h1 a[name="config"]:hover {
  background: var(--color-primary-content);
}

.wizard h1 a[name="config"]:hover::before {
  content: "Advanced! ";
}

.wizard a.button-ls,
.wizard button,
.wizard input[type="button"] {
  line-height: 1;
}

.configure__container>header {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-bottom: 16px;
}

.configure__editor {
  display: flex;
  flex: 1;
  overflow-y: auto;

  @include styled-scroll;
}

.configure__tags-link {
  margin: 0;
  line-height: 21px;
  margin-top: -6px;

  & a {
    color: var(--color-primary-content);
  }
}

.configure__container>header .toggle-items {
  margin-left: auto;
}

.wizard .configure__code {
  overflow-x: hidden;
}

.wizard .configure__code,
.wizard .configure__visual {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.wizard .configure__visual {
  > * {
    border-top: 1px solid var(--color-neutral-border);
    padding-top: 16px;
    margin-bottom: 16px;
    color: var(--color-neutral-content);
  }

  a {
    color: var(--color-primary-content);

    &:hover {
      text-decoration: underline;
      color: var(--color-primary-content-hover);

    }
  }

  input, select {
    padding: 4px 8px;
    font: inherit;
    color: inherit;
    background-color: inherit;
    border-color: var(--color-neutral-border);
  }
}

.wizard .configure__object {
  h4 {
    margin-bottom: 8px;
    color: var(--color-neutral-content);
  }

  > p {
    line-height: 32px;
    color: var(--color-neutral-content-subtler);
  }

  > p.configure__object-error {
    line-height: 22px;
    margin-top: 8px;
    color: var(--color-negative-content);
  }

  input {
    margin-left: 8px;
  }
}

.wizard .configure__enable_manual_columns {
  border-bottom: 1px dashed var(--color-neutral-border);
  width: fit-content;
  cursor: pointer;

  &:hover {
    opacity: 0.8;
  }
}

.wizard .configure__labels {
  display: flex;
  max-height: 200px;
}

.wizard .configure__add-labels {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  flex: 1;

  span {
    color: var(--color-neutral-content-subtler);
  }

  textarea {
    flex-grow: 1;
    margin: 8px 0;
    border-color: var(--color-neutral-border);
    background: var(--color-neutral-surface);
    color: var(--color-neutral-content);
    font: inherit;
  }
}

.wizard .configure__visual h4 {
  margin: 0;
  font-weight: 500;
  color: var(--color-neutral-content);
}

/*** Configure Labels ***/
.wizard .configure__current-labels {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow-x: hidden;
  margin-right: 8px;

  h3 {
    font-size: 16px;
    margin: 0 32px 7px;
    color: var(--color-neutral-content);
  }
}

.wizard .configure__labels ul {
  overflow-y: scroll;
  padding: 0 2em;
  margin: 0;
  list-style: none;
  min-width: 140px;
  display: flex;
  flex-direction: column;
  align-items: stretch;
  flex: 1;

  @include styled-scroll;

  .configure__label {
    display: flex;
    align-items: stretch;
    position: relative;

    &:not(:first-child) {
      margin-top: 8px;
    }

    &_choice {
      label {
        display: none;
      }

      span {
        background: var(--color-neutral-background);
      }
    }

    label {
      width: 0;
      cursor: pointer;

      &::before {
        content: "";
        position: absolute;
        width: 100%;
        height: 100%;
        background: inherit;
        opacity: 0.2;
        left: 0;
        right: 0;
        border-radius: 0 4px 4px 0;
      }

      &::after {
        position: absolute;
        left: -4px;
        width: 4px;
        content: "";
        height: 100%;
        background: inherit;
        border-radius: 3px 0 0 3px;
        transition: 0.1s all;
        will-change: width, left;
      }
    }

    .configure__label-color {
      width: 1px;
      padding: 0;
      border: 0;
      position: absolute;
      opacity: 0;
    }

    span {
      padding: 0 8px;
      overflow-x: hidden;
      word-break: break-word;
      color: var(--color-neutral-content);
    }

    .configure__delete-label {
      min-width: 0;
      border: 0;
      border-radius: 0 4px 4px 0;
      background: none;
      cursor: pointer;
      padding: 2px 6px;
      font-size: 12px;
      position: absolute;
      top: 0;
      left: 100%;
      height: 100%;
      color: var(--color-negative-icon);
      opacity: 0.8;
      transition: all 150ms ease-out;

      &:hover {
        background: var(--color-negative-emphasis-subtle);
        opacity: 1;
      }
    }

    &:hover {
      background: var(--color-neutral-surface);

      label::before {
        border-radius: 0;
      }

      label::after {
        width: 12px;
        left: -12px;
      }
    }
  }
}

.wizard ul.configure__settings {
  padding: 0;

  ul {
    padding: 0;
  }

  li {
    list-style: none;
    margin: 8px 0 0 8px;
  }

  label {
    cursor: pointer;
    color: var(--color-neutral-content-subtler);
  }

  input[type="checkbox"] {
    margin-right: 8px;
  }

  input[type="text"],
  select {
    font: inherit;
    line-height: 1.2em;
    padding: 4px 8px;
    border-radius: 4px;
    border: 1px solid var(--color-neutral-border);
  }
}

.wizard .configure__preview {
  background-color: var(--color-neutral-surface);
  flex-grow: 10;
  min-width: 500px;
  padding: 16px 16px 0;
  overflow-y: auto;
  border-left: 1px solid var(--color-neutral-border);
  display: flex;
  flex-direction: column;
  color: var(--color-neutral-content);

  @include styled-scroll;

  h3 {
    margin: 8px 0 16px;
    font-size: 16px;
    color: var(--color-neutral-content);
  }

  iframe {
    display: block;
    width: 100%;
    height: 100%;
  }

  &-ui {
    flex: 1;
    min-height: 0;
  }

  &-error {
    color: var(--color-negative-content);
    border: 1px solid var(--color-negative-border);
    background-color: var(--color-negative-background);
    padding: 10px 15px;
    border-radius: 3px;
    margin-bottom: 16px;
    white-space: pre-line;

    h2 {
      font-size: 16px;
    }
  }

  .editor>.common {
    flex: 1;
    max-width: 100%;
  }


  .main-view {
    &__annotation{
      padding: 0;
    }
  }
}

div[class^="App_menu"]>div {
  width: 100%;
}

:global(input.lsf-configure__add) {
  border: 1px solid var(--color-primary-border);
  color: var(--color-primary-content);
  background: var(--color-neutral-background);
  transition: all 150ms ease-out;
}

:global(input.lsf-configure__add:hover) {
  color: var(--color-neutral-content);
  background: var(--color-primary-emphasis-subtle);
}

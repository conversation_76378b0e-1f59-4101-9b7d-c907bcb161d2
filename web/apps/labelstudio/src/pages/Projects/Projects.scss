.projects-page {
  min-height: calc(100vh - var(--header-height));

  &__loading {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: calc(100vh - var(--header-height));
  }

  &__list {
    padding: 40px;
    display: grid;
    box-sizing: border-box;
    grid-gap: 32px;
    grid-auto-rows: 1fr;
    grid-template-columns: repeat(4, 1fr);
  }

  &__link {
    color: var(--color-neutral-content);
    display: block;
    display: flex;
    text-decoration: none;

    &:hover {
      color: var(--color-neutral-content);
    }
  }

  &__pages {
    background: var(--color-neutral-background);
    border-top: 1px solid var(--color-neutral-border);
    bottom: 0;
    width: 100%;
    position: sticky;
    display: flex;
    padding: 10px 40px;
    justify-content: flex-end;
    box-sizing: border-box;
  }

  @media (width >= 1200px) {
    &__list {
      grid-template-columns: repeat(3, 1fr);
    }
  }

  @media (width >= 1360px) {
    &__list {
      grid-template-columns: repeat(4, 1fr);
    }
  }
}

.empty-projects-page {
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;

  &__heidi {
    height: 12rem;
    margin-top: 6rem;
  }

  &__header {
    font-size: 32px;
    font-weight: 700;
    margin: 16px;
    color: var(--color-neutral-content);
  }

  p {
    font-size: 1.25rem;
    color: var(--color-neutral-content-subtle);
    margin: 0;
  }

  &__action {
    margin: 2rem 0;
  }
}

.project-card {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: var(--color-neutral-background);
  border: 1px solid var(--color-neutral-border);
  border-radius: 0.5rem;
  overflow: hidden;
  color: var(--color-neutral-content-subtle);
  box-shadow: 0 4px 8px rgb(0 0 0 / 5%);
  transition: all 150ms ease-out;

  &:hover {
    background-color: var(--color-neutral-surface);
    border-color: var(--color-primary-border-subtle);
  }

  &__header {
    padding: 12px 16px;
    border-bottom: 1px solid var(--color-neutral-border);
    transition: all 150ms ease-out;
  }

  &__menu {
    margin-left: auto;

    .button-ls {
      margin-right: -10px;
      opacity: 0.5;

      --button-color: var(--text-color);

      &__icon {
        width: 18px;
        height: 18px;
      }
    }
  }

  &__title {
    display: flex;
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 0.25rem;
    line-height: 22px;
    align-items: center;

    &-text {
      max-width: 250px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }

  &__summary {
    flex: 1;
    font-size: 14px;
    line-height: 22px;
  }

  &__annotation {
    display: flex;
    justify-content: space-between;
  }

  &__detail {
    display: grid;
    grid-auto-flow: column;
    grid-gap: 16px;
    grid-auto-columns: max-content;

    &-item {
      --icon-color: var(--color-neutral-background);

      display: flex;
      align-items: center;

      & .project-card__icon {
        color: var(--icon-color);
      }

      &_type {
        &_completed {
          --icon-color: var(--color-positive-icon);
        }

        &_rejected {
          --icon-color: var(--color-negative-icon);
        }

        &_predictions {
          --icon-color: var(--color-primary-icon);
        }
      }
    }
  }

  &__icon {
    margin-right: 10px;
  }

  &__description {
    flex: 1;
    font-size: 14px;
    line-height: 16px;
    padding: 12px 16px 0;
    overflow: hidden;
    color: var(--color-neutral-content-subtle);
    text-overflow: ellipsis;
    display: -webkit-box;
    word-break: break-word;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
  }

  &__info {
    display: flex;
    align-items: center;
    padding: 16px 16px 10px;
    justify-content: space-between;
  }

  &__created-date {
    color: var(--color-neutral-content-subtler);
    font-size: 0.875rem;

    & > span {
      font-weight: 500;
    }
  }

  &_colored {
    --color-project-header-content: var(--color-sand-000);
    --color-project-header-icon: var(--color-neutral-icon);

    background-color: var(--background-color);
    border-color: var(--border-color);

    &:hover {
      border-color: var(--header-color);
    }

    & .project-card__header {
      background-color: var(--header-color);
      color: var(--color-project-header-content);
      box-shadow: none;
    }

    & .project-card__detail {
      color: var(--content-color);

      &-item {
        --icon-color: var(--icon-color);
      }
    }
  }
}

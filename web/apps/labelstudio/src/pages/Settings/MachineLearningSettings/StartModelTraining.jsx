import { useCallback, useState } from "react";
import { Button } from "../../../components";
import { useAPI } from "../../../providers/ApiProvider";
import { Description } from "../../../components/Description/Description";
import { Block } from "../../../utils/bem";
import { useTranslation } from "react-i18next";

export const StartModelTraining = ({ backend }) => {
  const { t } = useTranslation();
  const api = useAPI();
  const [response, setResponse] = useState(null);

  const onStartTraining = useCallback(
    async (backend) => {
      const res = await api.callApi("trainMLBackend", {
        params: {
          pk: backend.id,
        },
      });

      setResponse(res.response || {});
    },
    [api],
  );

  return (
    <Block name="test-request">
      <Description style={{ marginTop: 0, maxWidth: 680 }}>
        {t("machine_learning.start_training_description_modal")}
        <br />
        <br />
        {t("machine_learning.training_note")}
      </Description>

      {response || (
        <Button
          onClick={() => {
            onStartTraining(backend);
          }}
        >
          {t("machine_learning.start_training")}
        </Button>
      )}

      {response && (
        <>
          <pre>{t("machine_learning.request_sent")}</pre>
          <pre>{t("machine_learning.response")} {JSON.stringify(response, null, 2)}</pre>
        </>
      )}
    </Block>
  );
};

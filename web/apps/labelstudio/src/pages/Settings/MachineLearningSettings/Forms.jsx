import { useState } from "react";
import { But<PERSON> } from "../../../components";
import { <PERSON>rrorWrapper } from "../../../components/Error/Error";
import { InlineError } from "../../../components/Error/InlineError";
import { useTranslation } from "react-i18next";
import { Form, Input, Select, TextArea, Toggle } from "../../../components/Form";
import "./MachineLearningSettings.scss";

const CustomBackendForm = ({ action, backend, project, onSubmit }) => {
  const { t } = useTranslation();
  const [selectedAuthMethod, setAuthMethod] = useState("NONE");
  const [, setMLError] = useState();

  return (
    <Form
      action={action}
      formData={{ ...(backend ?? {}) }}
      params={{ pk: backend?.id }}
      onSubmit={async (response) => {
        if (!response.error_message) {
          onSubmit(response);
        }
      }}
    >
      <Input type="hidden" name="project" value={project.id} />

      <Form.Row columnCount={1}>
        <Input name="title" label={t("machine_learning.name")} placeholder={t("machine_learning.enter_name")} required />
      </Form.Row>

      <Form.Row columnCount={1}>
        <Input name="url" label={t("machine_learning.backend_url")} required />
      </Form.Row>

      <Form.Row columnCount={2}>
        <Select
          name="auth_method"
          label={t("machine_learning.auth_method")}
          options={[
            { label: t("machine_learning.no_auth"), value: "NONE" },
            { label: t("machine_learning.basic_auth"), value: "BASIC_AUTH" },
          ]}
          value={selectedAuthMethod}
          onChange={setAuthMethod}
        />
      </Form.Row>

      {(backend?.auth_method === "BASIC_AUTH" || selectedAuthMethod === "BASIC_AUTH") && (
        <Form.Row columnCount={2}>
          <Input name="basic_auth_user" label={t("machine_learning.username")} />
          {backend?.basic_auth_pass_is_set ? (
            <Input name="basic_auth_pass" label={t("machine_learning.password")} type="password" placeholder="********" />
          ) : (
            <Input name="basic_auth_pass" label={t("machine_learning.password")} type="password" />
          )}
        </Form.Row>
      )}

      <Form.Row columnCount={1}>
        <TextArea
          name="extra_params"
          label="Any extra params to pass during model connection"
          style={{ minHeight: 120 }}
        />
      </Form.Row>

      <Form.Row columnCount={1}>
        <Toggle
          name="is_interactive"
          label={t("machine_learning.interactive_preannotations")}
          description={t("machine_learning.interactive_description")}
        />
      </Form.Row>

      <Form.Actions>
        <Button type="submit" look="primary" onClick={() => setMLError(null)}>
          {t("machine_learning.validate_and_save")}
        </Button>
      </Form.Actions>

      <Form.ResponseParser>
        {(response) => (
          <>
            {response.error_message && (
              <ErrorWrapper
                error={{
                  response: {
                    detail: backend ? t("machine_learning.failed_to_save") : t("machine_learning.failed_to_add"),
                    exc_info: response.error_message,
                  },
                }}
              />
            )}
          </>
        )}
      </Form.ResponseParser>

      <InlineError />
    </Form>
  );
};

export { CustomBackendForm };

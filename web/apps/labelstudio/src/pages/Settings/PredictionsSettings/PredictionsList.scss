.prediction-card {
  width: 100%;
  padding: 1rem;
  border-radius: 4px;
  background: var(--color-neutral-surface);
  border: 1px solid var(--color-neutral-border);
  display: flex;
  justify-content: space-between;

  &__meta {
    display: flex;
    flex-flow: row wrap;
    gap: 20px; 
    color: var(--color-neutral-content-subtler);
    margin-top: 7px;
  }

  &__group {
    display: flex;
    align-items: center;
  }

  &__header {
    display: flex;
    height: 48px;
    padding: 0 15px;
    align-items: center;
    font-weight: 500;
    font-size: 16px;
    line-height: 18px;
    justify-content: space-between;
    border: 1px solid vare(--color-neutral-border);
  }

  &__title {
    font-weight: 500;
    color: var(--color-neutral-content);

    &-content {
      display: flex;
      align-items: center;
    }
    
  }

  &__content {
    padding: 1rem;
  }

  &:not(:first-child) {
    margin-top: 24px;
  }
}
import { useCallback, useContext, useEffect, useState } from "react";
import { Description } from "../../../components/Description/Description";
import { Divider } from "../../../components/Divider/Divider";
import { EmptyState } from "../../../components/EmptyState/EmptyState";
import { IconPredictions } from "@humansignal/ui";
import { useAPI } from "../../../providers/ApiProvider";
import { ProjectContext } from "../../../providers/ProjectProvider";
import { Spinner } from "../../../components/Spinner/Spinner";
import { PredictionsList } from "./PredictionsList";
import { Block, Elem } from "../../../utils/bem";
import { useTranslation } from "react-i18next";
import "./PredictionsSettings.scss";

export const PredictionsSettings = () => {
  const { t } = useTranslation();
  const api = useAPI();
  const { project } = useContext(ProjectContext);
  const [versions, setVersions] = useState([]);
  const [loading, setLoading] = useState(false);
  const [loaded, setLoaded] = useState(false);

  const fetchVersions = useCallback(async () => {
    setLoading(true);
    const versions = await api.callApi("projectModelVersions", {
      params: {
        pk: project.id,
        extended: true,
      },
    });

    if (versions) setVersions(versions.static);
    setLoading(false);
    setLoaded(true);
  }, [project, setVersions]);

  useEffect(() => {
    if (project.id) {
      fetchVersions();
    }
  }, [project]);

  return (
    <Block name="prediction-settings">
      <Elem name={"wrapper"}>
        {loading && <Spinner size={32} />}

        {loaded && versions.length > 0 && (
          <Elem name="title-block">
            <Elem name="title">{t("predictions.list_title", "预测列表")}</Elem>
            <Description style={{ marginTop: "1em" }}>
              {t("predictions.list_description", "项目中可用的预测列表。每个卡片都与一个单独的模型版本相关联。要了解如何导入预测，")}{" "}
              <a href="https://labelstud.io/guide/predictions.html" target="_blank" rel="noreferrer">
                {t("predictions.see_documentation", "请查看文档")}
              </a>
              。
            </Description>
          </Elem>
        )}

        {loaded && versions.length === 0 && (
          <EmptyState
            icon={<IconPredictions />}
            title={t("predictions.no_predictions_title", "尚未上传预测")}
            description={t("predictions.no_predictions_description", "预测可用于预标注数据或验证模型。您可以上传并选择来自多个模型版本的预测。您也可以在模型选项卡中连接实时模型。")}
            footer={
              <div>
                {t("predictions.need_help", "需要帮助？")}
                <br />
                <a href="https://labelstud.io/guide/predictions" target="_blank" rel="noreferrer">
                  {t("predictions.learn_more_docs", "在我们的文档中了解更多关于如何上传预测的信息")}
                </a>
              </div>
            }
          />
        )}

        <PredictionsList project={project} versions={versions} fetchVersions={fetchVersions} />

        <Divider height={32} />
      </Elem>
    </Block>
  );
};

// PredictionsSettings.title = () => {
//   const { t } = useTranslation();
//   return t("settings.predictions");
// };
PredictionsSettings.menuItem = "predictions_settings";
PredictionsSettings.path = "/predictions";

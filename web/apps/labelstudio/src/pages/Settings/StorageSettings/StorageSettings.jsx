import { Columns } from "../../../components/Columns/Columns";
import { Description } from "../../../components/Description/Description";
import { Block, cn } from "../../../utils/bem";
import { Elem } from "../../../utils/bem";
import { StorageSet } from "./StorageSet";
import "./StorageSettings.scss";
import { isInLicense, LF_CLOUD_STORAGE_FOR_MANAGERS } from "../../../utils/license-flags";
import { useTranslation } from "react-i18next";

const isAllowCloudStorage = !isInLicense(LF_CLOUD_STORAGE_FOR_MANAGERS);

export const StorageSettings = () => {
  const { t } = useTranslation();
  const rootClass = cn("storage-settings");

  return isAllowCloudStorage ? (
    <Block name="storage-settings">
      <Elem name={"wrapper"}>
        <h1>{t("settings.cloud_storage")}</h1>
        <Description style={{ marginTop: 0 }}>
          {t("storage.description", "使用云存储或数据库存储作为标注任务的数据源或已完成标注的目标。")}
        </Description>

        <Columns count={2} gap="40px" size="320px" className={rootClass}>
          <StorageSet title={t("storage.source_title", "源云存储")} buttonLabel={t("storage.add_source", "添加源存储")} rootClass={rootClass} />

          <StorageSet
            title={t("storage.target_title", "目标云存储")}
            target="export"
            buttonLabel={t("storage.add_target", "添加目标存储")}
            rootClass={rootClass}
          />
        </Columns>
      </Elem>
    </Block>
  ) : null;
};

StorageSettings.title = () => {
  const { t } = useTranslation();
  return t("settings.cloud_storage");
};
StorageSettings.menuItem = () => {
  const { t } = useTranslation();
  return t("settings.cloud_storage");
};
StorageSettings.path = "/storage";

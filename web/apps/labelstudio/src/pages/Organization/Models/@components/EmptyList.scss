.empty-models-list {
  width: 100%;
  height: calc(100vh - var(--header-height));
  display: flex;
  align-items: center;
  justify-content: center;

  &__content {
    display: flex;
    width: 596px;
    padding: 32px;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 16px;
    border-radius: 8px;
    border: 1px dashed var(--color-neutral-border);
    background: var(--color-neutral-emphasis-subtle);
  }

  &__heidi {
    width: 193px;
    color: var(--color-neutral-background-bold);

    svg {
      width: 100%;
      height: 100%;
    }
  }

  &__title {
    color: var(--surface-on, #262626);
    text-align: center;

    /* title/large */
    font-size: 22px;
    font-style: normal;
    font-weight: 400;
    line-height: 28px;

    /* 127.273% */
  }

  &__caption {
    color: var(--surface-on-dim, #666);
    text-align: center;

    /* body/large */
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: 24px;

    /* 150% */
    letter-spacing: 0.5px;
  }
}

.people-list {
  width: 100%;
  min-width: 650px;
  font-size: 16px;

  &__wrapper {
    border-radius: 4px;
    border: 1px solid var(--color-neutral-border);
    background: var(--color-neutral-background);
  }

  &__loading {
    width: 100%;
    display: flex;
    min-height: 250px;
    justify-content: center;
    align-items: center;
  }

  &__users {
    width: 100%;
    text-align: left;
    border-collapse: collapse;
  }

  &__header {
    font-weight: 500;
    display: flex;
  }

  &__column,
  &__field {
    z-index: 2;
    height: 48px;
    padding: 0 10px;
    border: none;
    color: var(--color-neutral-content);
    display: flex;
    position: relative;
    align-items: center;

    &.avatar {
      width: 28px;
      box-sizing: content-box;
    }

    &.name {
      flex: 1;
    }

    &.email {
      width: 200px;
    }

    &.last-activity {
      width: 160px;
    }
  }

  &__field {
    &.last-activity {
      color: var(--color-neutral-content);
    }
  }

  &__column {
    font-weight: 500;
    background: none;
  }

  &__user {
    cursor: pointer;
    display: flex;

    &:nth-child(odd):not(&_active) {
      background-color: var(--color-neutral-emphasis-subtle);
    }

    &_active {
      position: relative;
      background-color: var(--color-primary-surface);

      &::before,
      &::after {
        top: 0;
        bottom: 0;
        left: 0;
        content: '';
        display: block;
        position: absolute;
      }

      &::after {
        z-index: 1;
        background-color: var(--color-neutral-surface);
        width: calc(100% + 22px);
        box-shadow: inset 4px 0 0 var(--grape_500), 1px 0 0 1px var(--color-neutral-border) inset;
      }

      &::before {
        z-index: 0;
        height: 100%;
        width: calc(100% + 22px);
      }
    }
  }
}

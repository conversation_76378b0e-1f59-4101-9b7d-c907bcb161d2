@import '../themes/default/variables';

body {
  --header-height: 48px;
  --menu-animation-duration: 0.15s;
  --menu-animation-curve: cubic-bezier(0.21, 1.04, 0.68, 1);
  --menu-animation-start: -10px;
  --menu-sidebar-width: 240px;

  margin: 0;
  color: var(--color-neutral-content);
  background: var(--color-neutral-background);
  width: 100vw;
  max-width: 100%;
  min-height: 100vh;
  scrollbar-color: var(--color-neutral-border-bold) var(--color-neutral-background);
}

.app-wrapper {
  width: 100vw;
  max-width: 100%;
  min-height: 100vh;
}

.global-error {
  padding: 32px;

  &__heidi {
    display: block;
    margin: 32px auto;
  }

  h1 {
    text-transform: uppercase;
    text-align: center;
    font-size: 20px;
  }

  h2 {
    font-size: 20px;
    color: var(--color-negative-content);
  }

  &__details {
    background: var(--color-neutral-background);
    max-height: 320px;
    overflow-y: auto;
    white-space: pre-wrap;
    margin: 16px 0;
    padding: 16px;
  }

  &__actions {
    display: flex;
    gap: 8px;

    >* {
      line-height: 1em;
    }
  }

  &__slack {
    margin-right: auto;
    display: flex;
    align-items: center;

    img {
      height: 16px;
      width: 16px;
      margin-right: 8px;
    }
  }
}

.color {
  margin: 4px 8px;
  position: relative;

  &::before {
    width: 24px;
    height: 24px;
    display: block;
    margin: 0 auto;
    content: '';
    border-radius: 100%;
    background-color: var(--background);
    color: var(--color-neutral-content);
    box-shadow: inset 0 0 0 1px rgb(0 0 0 / 15%);
  }
}

{"name": "labelstudio", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/labelstudio/src", "projectType": "application", "targets": {"build": {"executor": "@nx/webpack:webpack", "outputs": ["{options.outputPath}"], "defaultConfiguration": "production", "options": {"compiler": "babel", "outputPath": "dist/apps/labelstudio", "index": "apps/labelstudio/src/index.html", "baseHref": "/", "main": "apps/labelstudio/src/main.tsx", "tsConfig": "apps/labelstudio/tsconfig.app.json", "assets": [], "styles": [], "scripts": [], "isolatedConfig": true, "webpackConfig": "webpack.config.js"}, "configurations": {"development": {"extractLicenses": false, "optimization": false, "sourceMap": true, "vendorChunk": true}, "production": {"fileReplacements": [{"replace": "apps/labelstudio/src/environments/environment.ts", "with": "apps/labelstudio/src/environments/environment.prod.ts"}], "optimization": true, "sourceMap": false, "namedChunks": false, "extractLicenses": true, "vendorChunk": false}}}, "serve": {"executor": "@nx/webpack:dev-server", "defaultConfiguration": "development", "options": {"buildTarget": "labelstudio:build", "hmr": true}, "configurations": {"development": {"buildTarget": "labelstudio:build:development"}, "production": {"buildTarget": "labelstudio:build:production", "hmr": false}}}, "serve-static": {"executor": "@nx/web:file-server", "options": {"buildTarget": "labelstudio:build"}}, "unit": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "apps/labelstudio/jest.config.ts", "passWithNoTests": true}, "configurations": {"ci": {"ci": true, "codeCoverage": true}}}, "version": {"executor": "nx:run-commands", "options": {"cwd": "apps/labelstudio", "command": "node ../../tools/version/version.mjs"}}}, "tags": []}
{"compilerOptions": {"target": "ESNEXT", "module": "commonjs", "allowJs": true, "checkJs": false, "jsx": "preserve", "strict": true, "rootDirs": ["./src"], "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true}, "exclude": ["./dist", "./lib"], "files": [], "include": [], "references": [{"path": "./tsconfig.app.json"}, {"path": "./tsconfig.spec.json"}], "extends": "../../tsconfig.base.json"}
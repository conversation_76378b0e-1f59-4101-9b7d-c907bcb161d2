{"name": "playground", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/playground/src", "projectType": "application", "targets": {"build": {"executor": "@nx/webpack:webpack", "outputs": ["{options.outputPath}"], "defaultConfiguration": "production", "options": {"compiler": "babel", "outputPath": "dist/apps/playground/playground-assets", "index": "apps/playground/src/index.html", "baseHref": "/", "main": "apps/playground/src/main.tsx", "tsConfig": "apps/playground/tsconfig.app.json", "assets": [], "styles": [], "scripts": [], "isolatedConfig": true, "webpackConfig": "webpack.config.js"}, "configurations": {"development": {"extractLicenses": false, "optimization": false, "sourceMap": true, "vendorChunk": true, "baseHref": "/"}, "production": {"fileReplacements": [{"replace": "apps/playground/src/environments/environment.ts", "with": "apps/playground/src/environments/environment.prod.ts"}], "optimization": true, "sourceMap": false, "namedChunks": false, "extractLicenses": true, "vendorChunk": false, "baseHref": "/playground-assets/"}}}, "serve": {"executor": "@nx/webpack:dev-server", "defaultConfiguration": "development", "options": {"buildTarget": "playground:build", "port": 4200, "hmr": true}, "configurations": {"development": {"buildTarget": "playground:build:development"}, "production": {"buildTarget": "playground:build:production", "hmr": false}}}, "serve-static": {"executor": "@nx/web:file-server", "options": {"buildTarget": "playground:build"}}, "unit": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "apps/playground/jest.config.ts", "passWithNoTests": true}, "configurations": {"ci": {"ci": true, "codeCoverage": true}}}}, "tags": []}
.root {
  :global(.react-codemirror2 .CodeMirror) {
    border: none;
    border-radius: 0;
  }

  :global(.lsf-tabs-panel__body) {
    height: 100%;
  }

  :global(.lsf-panel-tabs__tab) {
    color: var(--color-neutral-content-subtler);
  }

  :global(.lsf-panel-tabs__tab_active) {
    transform: none;
    border-width: 0;
    color: var(--color-neutral-content);
    box-shadow: 1px -1px 0 rgba(var(--color-neutral-shadow-raw) / 4%), -1px -1px 0 rgba(var(--color-neutral-shadow-raw) / 4%);
  }

  :global(.lsf-tabs__tabs-row) {
    border: none;
  }

  :global(.lsf-sidepanels_collapsed .lsf-sidepanels__wrapper .lsf-tabs__contents) {
    border: none;
  }

  :global(.lsf-sidepanels_collapsed) {
    flex: 1;
  }

  :global(.lsf-wrapper) {
    flex: 1;
    display: flex;
    flex-direction: column;
  }
}

body {
  overflow: hidden;
}
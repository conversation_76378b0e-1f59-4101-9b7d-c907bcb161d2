{"segments": {}, "flags": {"feat_all_optic_71_dashboard_multiple_labeling_group_support_v1_01092023_short": {"key": "feat_all_optic_71_dashboard_multiple_labeling_group_support_v1_01092023_short", "on": false, "prerequisites": [], "targets": [], "contextTargets": [], "rules": [], "fallthrough": {"variation": 0}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": false, "usingEnvironmentId": false}, "clientSide": false, "salt": "8d390bd4d06a4a739d720491669e99ec", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 4, "deleted": false}, "feat_front_dev_1752_notification_links_in_label_and_review_streams": {"key": "feat_front_dev_1752_notification_links_in_label_and_review_streams", "on": false, "prerequisites": [], "targets": [], "contextTargets": [], "rules": [], "fallthrough": {"variation": 0}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": false, "usingEnvironmentId": false}, "clientSide": false, "salt": "dde132d08b0841e8ac318318bc54e87f", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 2, "deleted": false}, "feat_front_dev_3260_alternative_shortcuts_for_video_naviagtion": {"key": "feat_front_dev_3260_alternative_shortcuts_for_video_naviagtion", "on": false, "prerequisites": [], "targets": [], "contextTargets": [], "rules": [], "fallthrough": {"variation": 0}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": false, "usingEnvironmentId": false}, "clientSide": false, "salt": "0cbf5484f8e04f70838106e3490ecb32", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 2, "deleted": false}, "feat_front_dev_399_lock_interface_when_trial_expired_short": {"key": "feat_front_dev_399_lock_interface_when_trial_expired_short", "on": false, "prerequisites": [], "targets": [], "contextTargets": [], "rules": [], "fallthrough": {"variation": 0}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": false, "usingEnvironmentId": false}, "clientSide": false, "salt": "fd80549f8ea84dbfbbf2bbe6daeffa01", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 2, "deleted": false}, "feat_optic_1098_annotation_history_lead_time_charts": {"key": "feat_optic_1098_annotation_history_lead_time_charts", "on": false, "prerequisites": [], "targets": [], "contextTargets": [], "rules": [], "fallthrough": {"variation": 0}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": false, "usingEnvironmentId": false}, "clientSide": false, "salt": "b876638e49394959acae27c1d37c4eee", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 3, "deleted": false}, "ff_back_2004_async_review_24032022_short": {"key": "ff_back_2004_async_review_24032022_short", "on": false, "prerequisites": [], "targets": [], "contextTargets": [], "rules": [], "fallthrough": {"variation": 0}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": false, "usingEnvironmentId": false}, "clientSide": false, "salt": "2145a3e956e645f299e16e90b6e54e89", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 4, "deleted": false}, "ff_back_2884_comments_notifications_02092022_short": {"key": "ff_back_2884_comments_notifications_02092022_short", "on": false, "prerequisites": [], "targets": [], "contextTargets": [], "rules": [], "fallthrough": {"variation": 0}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": false, "usingEnvironmentId": false}, "clientSide": false, "salt": "1a1ba4d3a3ea454ebce1626dabe53aa5", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 4, "deleted": false}, "ff_back_DEV_1711_review_queue_140222_short": {"key": "ff_back_DEV_1711_review_queue_140222_short", "on": false, "prerequisites": [], "targets": [], "contextTargets": [], "rules": [], "fallthrough": {"variation": 0}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": false, "usingEnvironmentId": false}, "clientSide": false, "salt": "f06fc45aed1147768fc83f97abbd0107", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 5, "deleted": false}, "ff_back_DEV_3374_review_query_160922_short": {"key": "ff_back_DEV_3374_review_query_160922_short", "on": false, "prerequisites": [], "targets": [], "contextTargets": [], "rules": [], "fallthrough": {"variation": 0}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": false, "usingEnvironmentId": false}, "clientSide": false, "salt": "028a3e3aff9b41869d62269fcf599e64", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 3, "deleted": false}, "ff_back_dev_1417_start_training_mlbackend_webhooks_250122_long": {"key": "ff_back_dev_1417_start_training_mlbackend_webhooks_250122_long", "on": true, "prerequisites": [], "targets": [], "contextTargets": [], "rules": [], "fallthrough": {"variation": 0}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": false, "usingEnvironmentId": false}, "clientSide": false, "salt": "5e82b5b9641a4474bb97f54919b9f47a", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 5, "deleted": false}, "ff_back_dev_1948_reviewed_status_16052022_short": {"key": "ff_back_dev_1948_reviewed_status_16052022_short", "on": false, "prerequisites": [], "targets": [], "contextTargets": [], "rules": [], "fallthrough": {"variation": 0}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": false, "usingEnvironmentId": false}, "clientSide": false, "salt": "fbdfa98516e2449e9cf58d5368f73771", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 5, "deleted": false}, "ff_back_dev_2362_project_credentials_060722_short": {"key": "ff_back_dev_2362_project_credentials_060722_short", "on": false, "prerequisites": [], "targets": [], "contextTargets": [], "rules": [], "fallthrough": {"variation": 0}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": false, "usingEnvironmentId": false}, "clientSide": false, "salt": "05092a9cc7fc428b9b6520ffc0d506af", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 3, "deleted": false}, "ff_back_dev_2762_textarea_weights_30062022_short": {"key": "ff_back_dev_2762_textarea_weights_30062022_short", "on": true, "prerequisites": [], "targets": [], "contextTargets": [], "rules": [], "fallthrough": {"variation": 0}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": false, "usingEnvironmentId": false}, "clientSide": false, "salt": "8371d41f39024d86b1315cd37c2dc553", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 5, "deleted": false}, "ff_back_dev_4664_remove_storage_file_on_export_delete_29032023_short": {"key": "ff_back_dev_4664_remove_storage_file_on_export_delete_29032023_short", "on": false, "prerequisites": [], "targets": [], "contextTargets": [], "rules": [], "fallthrough": {"variation": 0}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": false, "usingEnvironmentId": false}, "clientSide": false, "salt": "b67cb5a44e0c45259e82cc9404421fcb", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 2, "deleted": false}, "ff_back_experimental_features": {"key": "ff_back_experimental_features", "on": false, "prerequisites": [], "targets": [], "contextTargets": [], "rules": [], "fallthrough": {"variation": 0}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": false, "usingEnvironmentId": false}, "clientSide": false, "salt": "aaf0e626e3bc44de99c620ec24df9f96", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 3, "deleted": false}, "ff_fix_back_dev_3342_storage_scan_with_invalid_annotations": {"key": "ff_fix_back_dev_3342_storage_scan_with_invalid_annotations", "on": true, "prerequisites": [], "targets": [], "contextTargets": [], "rules": [], "fallthrough": {"variation": 0}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": false, "usingEnvironmentId": false}, "clientSide": false, "salt": "bb6f4889c59d47faa4145a8a5a694d37", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 4, "deleted": false}, "ff_front_1170_outliner_030222_short": {"key": "ff_front_1170_outliner_030222_short", "on": true, "prerequisites": [], "targets": [], "contextTargets": [], "rules": [], "fallthrough": {"variation": 0}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": false, "usingEnvironmentId": false}, "clientSide": false, "salt": "bb4b6effe38f42f69b92585f690e33b0", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 5, "deleted": false}, "ff_front_DEV_1713_audio_ui_150222_short": {"key": "ff_front_DEV_1713_audio_ui_150222_short", "on": false, "prerequisites": [], "targets": [], "contextTargets": [], "rules": [], "fallthrough": {"variation": 0}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": false, "usingEnvironmentId": false}, "clientSide": false, "salt": "b9a9a797e1884581b1ab307952adbb2c", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 2, "deleted": false}, "ff_front_dev_1442_unselect_shape_on_click_outside_080622_short": {"key": "ff_front_dev_1442_unselect_shape_on_click_outside_080622_short", "on": false, "prerequisites": [], "targets": [], "contextTargets": [], "rules": [], "fallthrough": {"variation": 0}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": false, "usingEnvironmentId": false}, "clientSide": false, "salt": "27301e1167d14c31b04495b0bde029d2", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 7, "deleted": false}, "ff_front_dev_1480_created_on_in_review_180122_short": {"key": "ff_front_dev_1480_created_on_in_review_180122_short", "on": false, "prerequisites": [], "targets": [], "contextTargets": [], "rules": [], "fallthrough": {"variation": 0}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": false, "usingEnvironmentId": false}, "clientSide": false, "salt": "de557f03d92c49b9926cc15472bf2edd", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 6, "deleted": false}, "ff_front_dev_1536_taxonomy_user_labels_150222_long": {"key": "ff_front_dev_1536_taxonomy_user_labels_150222_long", "on": true, "prerequisites": [], "targets": [], "contextTargets": [], "rules": [], "fallthrough": {"variation": 0}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": false, "usingEnvironmentId": false}, "clientSide": false, "salt": "396498db51644f0abf7f5de063bea3d0", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 6, "deleted": false}, "ff_front_dev_1658_notification_center_170222_short": {"key": "ff_front_dev_1658_notification_center_170222_short", "on": false, "prerequisites": [], "targets": [], "contextTargets": [], "rules": [], "fallthrough": {"variation": 0}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": false, "usingEnvironmentId": false}, "clientSide": false, "salt": "3828ae1441cc4a83b2121270fba672d1", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 3, "deleted": false}, "ff_front_dev_1682_model_version_dropdown_070622_short": {"key": "ff_front_dev_1682_model_version_dropdown_070622_short", "on": false, "prerequisites": [], "targets": [], "contextTargets": [], "rules": [], "fallthrough": {"variation": 0}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": false, "usingEnvironmentId": false}, "clientSide": false, "salt": "58f4f819d52b461b9e833cdb896311d6", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 9, "deleted": false}, "ff_front_dev_2186_comments_for_update": {"key": "ff_front_dev_2186_comments_for_update", "on": false, "prerequisites": [], "targets": [], "contextTargets": [], "rules": [], "fallthrough": {"variation": 1}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": false, "usingEnvironmentId": false}, "clientSide": false, "salt": "29db86dba7204e519da03f91d8fc59ed", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 7, "deleted": false}, "ff_front_dev_2669_paragraph_author_filter_210622_short": {"key": "ff_front_dev_2669_paragraph_author_filter_210622_short", "on": true, "prerequisites": [], "targets": [], "contextTargets": [], "rules": [], "fallthrough": {"variation": 0}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": false, "usingEnvironmentId": false}, "clientSide": false, "salt": "7d2dab6b947d47b0a833dc98367a6cd4", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 5, "deleted": false}, "ff_front_dev_2671_anchor_rotate_bbox_010722_short": {"key": "ff_front_dev_2671_anchor_rotate_bbox_010722_short", "on": false, "prerequisites": [], "targets": [], "contextTargets": [], "rules": [], "fallthrough": {"variation": 0}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": false, "usingEnvironmentId": false}, "clientSide": false, "salt": "0f7496343ddf4a819ef9f560b479d4c0", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 2, "deleted": false}, "ff_front_dev_2715_audio_3_280722_short": {"key": "ff_front_dev_2715_audio_3_280722_short", "on": true, "prerequisites": [], "targets": [], "contextTargets": [], "rules": [], "fallthrough": {"variation": 0}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": false, "usingEnvironmentId": false}, "clientSide": false, "salt": "a8eb8122a118496eab36bacd9dffff6b", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 4, "deleted": false}, "ff_front_optic_1494_saved_templates_to_custom_templates": {"key": "ff_front_optic_1494_saved_templates_to_custom_templates", "on": true, "prerequisites": [], "targets": [], "contextTargets": [], "rules": [], "fallthrough": {"variation": 0}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": false, "usingEnvironmentId": false}, "clientSide": false, "salt": "bd4fd2af88bc47fa8d0b7c7648158e4d", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 4, "deleted": false}, "ff_front_optic_1610_ask_ai_questions": {"key": "ff_front_optic_1610_ask_ai_questions", "on": false, "prerequisites": [], "targets": [], "contextTargets": [], "rules": [], "fallthrough": {"variation": 0}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": false, "usingEnvironmentId": false}, "clientSide": false, "salt": "5a8d9cd75b6c4a5ea5985316ab34b25d", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 3, "deleted": false}, "fflag-feat-dev-2887-comments-ui-editor-short": {"key": "fflag-feat-dev-2887-comments-ui-editor-short", "on": false, "prerequisites": [], "targets": [], "contextTargets": [], "rules": [], "fallthrough": {"variation": 0}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": false, "usingEnvironmentId": false}, "clientSide": false, "salt": "7b49c8b7fb8a4bb6b2836e3cdeeee5df", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 9, "deleted": false}, "fflag-feat-dev-3034-comments-with-drafts-short": {"key": "fflag-feat-dev-3034-comments-with-drafts-short", "on": false, "prerequisites": [], "targets": [], "contextTargets": [], "rules": [], "fallthrough": {"variation": 0}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": false, "usingEnvironmentId": false}, "clientSide": false, "salt": "4070f7702bc84aa4a4ccf28fb92d3e7a", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 6, "deleted": false}, "fflag-feat-front-dev-2866-free-trial-invite-short": {"key": "fflag-feat-front-dev-2866-free-trial-invite-short", "on": false, "prerequisites": [], "targets": [], "contextTargets": [], "rules": [], "fallthrough": {"variation": 0}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": false, "usingEnvironmentId": false}, "clientSide": false, "salt": "82b06caa7c0242f780dbdf5e1f537b45", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 2, "deleted": false}, "fflag-feat-front-dev-3051-trial-experience": {"key": "fflag-feat-front-dev-3051-trial-experience", "on": false, "prerequisites": [], "targets": [], "contextTargets": [], "rules": [], "fallthrough": {"variation": 0}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": false, "usingEnvironmentId": false}, "clientSide": false, "salt": "9fe785a26fea4f3bad0c25781f980ac4", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 3, "deleted": false}, "fflag__feature_develop__prompts__dia_1829_jwt_token_auth": {"key": "fflag__feature_develop__prompts__dia_1829_jwt_token_auth", "on": true, "prerequisites": [], "targets": [], "contextTargets": [], "rules": [], "fallthrough": {"variation": 0}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": false, "usingEnvironmentId": false}, "clientSide": false, "salt": "e907cff690ed4dc490a6104c7dca73d9", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 7, "deleted": false}, "fflag__feature_develop__prompts__dia_1868_azure_ai_foundry": {"key": "fflag__feature_develop__prompts__dia_1868_azure_ai_foundry", "on": true, "prerequisites": [], "targets": [], "contextTargets": [], "rules": [], "fallthrough": {"variation": 0}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": false, "usingEnvironmentId": false}, "clientSide": false, "salt": "b08fc450f77d4fd7828957aeba9cdfdb", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 3, "deleted": false}, "fflag_all_feat_dia_1777_ls_homepage_short": {"key": "fflag_all_feat_dia_1777_ls_homepage_short", "on": true, "prerequisites": [], "targets": [], "contextTargets": [], "rules": [], "fallthrough": {"variation": 0}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": false, "usingEnvironmentId": false}, "clientSide": false, "salt": "3f344436b8be40069db2f0031edb8dfa", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 5, "deleted": false}, "fflag_feat_all_dia_1576_prompts_easy_breezy_onboarding_long": {"key": "fflag_feat_all_dia_1576_prompts_easy_breezy_onboarding_long", "on": false, "prerequisites": [], "targets": [], "contextTargets": [], "rules": [], "fallthrough": {"variation": 0}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": false, "usingEnvironmentId": false}, "clientSide": false, "salt": "6fd7f4be8bda46e897631d662f980c06", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 2, "deleted": false}, "fflag_feat_all_dia_1576_prompts_easy_breezy_onboarding_short_async_presets_ks": {"key": "fflag_feat_all_dia_1576_prompts_easy_breezy_onboarding_short_async_presets_ks", "on": false, "prerequisites": [], "targets": [], "contextTargets": [], "rules": [], "fallthrough": {"variation": 0}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": false, "usingEnvironmentId": false}, "clientSide": false, "salt": "7c6d7ec1634e4d24acdf53fed7b23c54", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 2, "deleted": false}, "fflag_feat_all_dia_1700_pinned_organization_sidebar": {"key": "fflag_feat_all_dia_1700_pinned_organization_sidebar", "on": false, "prerequisites": [], "targets": [], "contextTargets": [], "rules": [], "fallthrough": {"variation": 0}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": false, "usingEnvironmentId": false}, "clientSide": false, "salt": "b5d2f96acd364480a4b631b6836b3f21", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 2, "deleted": false}, "fflag_feat_all_dia_2067_tasks_table_component": {"key": "fflag_feat_all_dia_2067_tasks_table_component", "on": false, "prerequisites": [], "targets": [], "contextTargets": [], "rules": [], "fallthrough": {"variation": 0}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": false, "usingEnvironmentId": false}, "clientSide": false, "salt": "2130aa7856c54c8ba9d3745630bd8ae8", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 2, "deleted": false}, "fflag_feat_all_dia_835_prompter_workflow_long": {"key": "fflag_feat_all_dia_835_prompter_workflow_long", "on": false, "prerequisites": [], "targets": [], "contextTargets": [], "rules": [], "fallthrough": {"variation": 0}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": false, "usingEnvironmentId": false}, "clientSide": false, "salt": "efa1b3e7ae7e4b5fa37657178a5b233e", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 4, "deleted": false}, "fflag_feat_all_leap_1081_reviewer_flow_updates": {"key": "fflag_feat_all_leap_1081_reviewer_flow_updates", "on": true, "prerequisites": [], "targets": [], "contextTargets": [], "rules": [], "fallthrough": {"variation": 0}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": false, "usingEnvironmentId": false}, "clientSide": false, "salt": "e2b43ff7cff547c1b68bf2539a0782a1", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 8, "deleted": false}, "fflag_feat_all_leap_1181_bulk_annotation_short": {"key": "fflag_feat_all_leap_1181_bulk_annotation_short", "on": true, "prerequisites": [], "targets": [], "contextTargets": [], "rules": [], "fallthrough": {"variation": 0}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": false, "usingEnvironmentId": false}, "clientSide": false, "salt": "978a68420b714121be3a153a4c472abe", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 6, "deleted": false}, "fflag_feat_all_leap_1429_flexible_reject_mode_250924_short": {"key": "fflag_feat_all_leap_1429_flexible_reject_mode_250924_short", "on": true, "prerequisites": [], "targets": [], "contextTargets": [], "rules": [], "fallthrough": {"variation": 0}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": false, "usingEnvironmentId": false}, "clientSide": false, "salt": "dc85fa6d78d84d09a54702d35d2184c1", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 4, "deleted": false}, "fflag_feat_all_leap_1430_per_field_comments_100924_short": {"key": "fflag_feat_all_leap_1430_per_field_comments_100924_short", "on": true, "prerequisites": [], "targets": [], "contextTargets": [], "rules": [], "fallthrough": {"variation": 0}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": false, "usingEnvironmentId": false}, "clientSide": false, "salt": "0f9325fad4f248f5af2514b9c03a513b", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 7, "deleted": false}, "fflag_feat_all_leap_1534_custom_task_lock_timeout_short": {"key": "fflag_feat_all_leap_1534_custom_task_lock_timeout_short", "on": false, "prerequisites": [], "targets": [], "contextTargets": [], "rules": [], "fallthrough": {"variation": 0}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": false, "usingEnvironmentId": false}, "clientSide": false, "salt": "736e713d05a544a78e8ad1c9dbcc8376", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 3, "deleted": false}, "fflag_feat_all_leap_1682_plugins_v0": {"key": "fflag_feat_all_leap_1682_plugins_v0", "on": true, "prerequisites": [], "targets": [], "contextTargets": [], "rules": [], "fallthrough": {"variation": 0}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": false, "usingEnvironmentId": false}, "clientSide": false, "salt": "92d6c7305394440696aa8abf0e5d2143", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 3, "deleted": false}, "fflag_feat_all_leap_1732_pausing_an_annotator_short": {"key": "fflag_feat_all_leap_1732_pausing_an_annotator_short", "on": true, "prerequisites": [], "targets": [], "contextTargets": [], "rules": [], "fallthrough": {"variation": 0}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": false, "usingEnvironmentId": false}, "clientSide": false, "salt": "bf2998352dcb4350846a1621b3c44626", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 3, "deleted": false}, "fflag_feat_all_leap_1821_annotation_limit_short": {"key": "fflag_feat_all_leap_1821_annotation_limit_short", "on": true, "prerequisites": [], "targets": [], "contextTargets": [], "rules": [], "fallthrough": {"variation": 0}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": false, "usingEnvironmentId": false}, "clientSide": false, "salt": "55ab9d58aa70410d8bf4a8b3dd1712bb", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 3, "deleted": false}, "fflag_feat_all_leap_1825_annotator_evaluation_short": {"key": "fflag_feat_all_leap_1825_annotator_evaluation_short", "on": true, "prerequisites": [], "targets": [], "contextTargets": [], "rules": [], "fallthrough": {"variation": 0}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": false, "usingEnvironmentId": false}, "clientSide": false, "salt": "d22daddab39444af9f4e3802a4d6bbf2", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 3, "deleted": false}, "fflag_feat_all_leap_2042_average_agreement_score_popover": {"key": "fflag_feat_all_leap_2042_average_agreement_score_popover", "on": true, "prerequisites": [], "targets": [], "contextTargets": [], "rules": [], "fallthrough": {"variation": 0}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": false, "usingEnvironmentId": false}, "clientSide": false, "salt": "68a21fc8190441fb85b49b49a32583b0", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 3, "deleted": false}, "fflag_feat_all_leap_883_custom_script_270524_short": {"key": "fflag_feat_all_leap_883_custom_script_270524_short", "on": true, "prerequisites": [], "targets": [], "contextTargets": [], "rules": [], "fallthrough": {"variation": 0}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": false, "usingEnvironmentId": false}, "clientSide": false, "salt": "6c077db957b14df68a3790866fec95d2", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 7, "deleted": false}, "fflag_feat_all_lops_315_temp_datasets_limitations_short": {"key": "fflag_feat_all_lops_315_temp_datasets_limitations_short", "on": false, "prerequisites": [], "targets": [], "contextTargets": [], "rules": [], "fallthrough": {"variation": 0}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": false, "usingEnvironmentId": false}, "clientSide": false, "salt": "2fe0488c9cf342f3a4d2b21ca8a39f77", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 3, "deleted": false}, "fflag_feat_all_lops_e_3_datasets_short": {"key": "fflag_feat_all_lops_e_3_datasets_short", "on": true, "prerequisites": [], "targets": [{"contextKind": "user", "variation": 0, "values": ["<EMAIL>"]}], "contextTargets": [{"contextKind": "user", "variation": 0, "values": []}], "rules": [], "fallthrough": {"variation": 1}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": false, "usingEnvironmentId": false}, "clientSide": false, "salt": "9048a4ea89c0437b90600d9bdaf62eaa", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 6, "deleted": false}, "fflag_feat_all_lsdv_4915_async_task_import_13042023_short": {"key": "fflag_feat_all_lsdv_4915_async_task_import_13042023_short", "on": false, "prerequisites": [], "targets": [], "contextTargets": [], "rules": [], "fallthrough": {"variation": 0}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": false, "usingEnvironmentId": false}, "clientSide": false, "salt": "838fc12f47a84129bb42f3935db8f389", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 2, "deleted": false}, "fflag_feat_all_lsdv_e_294_llm_annotations_180723_long": {"key": "fflag_feat_all_lsdv_e_294_llm_annotations_180723_long", "on": true, "prerequisites": [], "targets": [], "contextTargets": [], "rules": [], "fallthrough": {"variation": 0}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": false, "usingEnvironmentId": false}, "clientSide": false, "salt": "640d40057d9f408587c8996ac2d3710c", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 8, "deleted": false}, "fflag_feat_all_lsdv_e_295_project_level_roles_via_saml_scim_ldap_short": {"key": "fflag_feat_all_lsdv_e_295_project_level_roles_via_saml_scim_ldap_short", "on": false, "prerequisites": [], "targets": [], "contextTargets": [], "rules": [], "fallthrough": {"variation": 0}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": false, "usingEnvironmentId": false}, "clientSide": false, "salt": "ca5b9081fa654bc08621311cc409a341", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 2, "deleted": false}, "fflag_feat_all_optic_1178_reduce_memory_leak_short": {"key": "fflag_feat_all_optic_1178_reduce_memory_leak_short", "on": true, "prerequisites": [], "targets": [], "contextTargets": [], "rules": [], "fallthrough": {"variation": 0}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": false, "usingEnvironmentId": false}, "clientSide": false, "salt": "0ccfe8ad9ac14a69b65ce25641afa41f", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 3, "deleted": false}, "fflag_feat_all_optic_1181_membership_performance": {"key": "fflag_feat_all_optic_1181_membership_performance", "on": false, "prerequisites": [], "targets": [], "contextTargets": [], "rules": [], "fallthrough": {"variation": 0}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": false, "usingEnvironmentId": false}, "clientSide": false, "salt": "3691490c5dc2478e9f6ea4ee816efb87", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 2, "deleted": false}, "fflag_feat_all_optic_1354_sum_annotation_region_count_short": {"key": "fflag_feat_all_optic_1354_sum_annotation_region_count_short", "on": false, "prerequisites": [], "targets": [], "contextTargets": [], "rules": [], "fallthrough": {"variation": 0}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": false, "usingEnvironmentId": false}, "clientSide": false, "salt": "bbb5c996193946d9935d9438e0c5a33a", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 2, "deleted": false}, "fflag_feat_all_optic_1811_automax_project_setup_streaming": {"key": "fflag_feat_all_optic_1811_automax_project_setup_streaming", "on": true, "prerequisites": [], "targets": [], "contextTargets": [], "rules": [], "fallthrough": {"variation": 0}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": false, "usingEnvironmentId": false}, "clientSide": false, "salt": "4bbabd8130cd43fcb0b30f2eb3ef7f56", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 3, "deleted": false}, "fflag_feat_all_optic_520_annotator_report_short": {"key": "fflag_feat_all_optic_520_annotator_report_short", "on": false, "prerequisites": [], "targets": [], "contextTargets": [], "rules": [], "fallthrough": {"variation": 0}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": false, "usingEnvironmentId": false}, "clientSide": false, "salt": "44d91ccbbe2d49608400584056a90597", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 3, "deleted": false}, "fflag_feat_all_optic_991_dashboard_v2_short": {"key": "fflag_feat_all_optic_991_dashboard_v2_short", "on": true, "prerequisites": [], "targets": [], "contextTargets": [], "rules": [], "fallthrough": {"variation": 0}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": false, "usingEnvironmentId": false}, "clientSide": false, "salt": "8f0220b6594841d0837773d258eebf6a", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 3, "deleted": false}, "fflag_feat_back_dev_3756_queue_enrollment_min_short": {"key": "fflag_feat_back_dev_3756_queue_enrollment_min_short", "on": false, "prerequisites": [], "targets": [], "contextTargets": [], "rules": [], "fallthrough": {"variation": 0}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": true, "usingEnvironmentId": false}, "clientSide": false, "salt": "081f810caa714e5cab227bb557c798a2", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 2, "deleted": false}, "fflag_feat_back_lsdv_3958_server_side_encryption_for_target_storage_short": {"key": "fflag_feat_back_lsdv_3958_server_side_encryption_for_target_storage_short", "on": false, "prerequisites": [], "targets": [], "contextTargets": [], "rules": [], "fallthrough": {"variation": 0}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": false, "usingEnvironmentId": false}, "clientSide": false, "salt": "850718b1924948e6a8fac22840a18993", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 5, "deleted": false}, "fflag_feat_back_lsdv_4932_enable_memory_profiler": {"key": "fflag_feat_back_lsdv_4932_enable_memory_profiler", "on": false, "prerequisites": [], "targets": [], "contextTargets": [], "rules": [], "fallthrough": {"variation": 1}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": false, "usingEnvironmentId": false}, "clientSide": false, "salt": "5b7df006ae16414982f3395daadc9245", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 4, "deleted": false}, "fflag_feat_back_lsdv_5307_import_reviews_drafts_29062023_short": {"key": "fflag_feat_back_lsdv_5307_import_reviews_drafts_29062023_short", "on": false, "prerequisites": [], "targets": [], "contextTargets": [], "rules": [], "fallthrough": {"variation": 0}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": false, "usingEnvironmentId": false}, "clientSide": false, "salt": "3f7fe060bc424b98829c06ca3e3bd62c", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 3, "deleted": false}, "fflag_feat_back_optic_1157_set_ground_truths_action": {"key": "fflag_feat_back_optic_1157_set_ground_truths_action", "on": true, "prerequisites": [], "targets": [], "contextTargets": [], "rules": [], "fallthrough": {"variation": 0}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": false, "usingEnvironmentId": false}, "clientSide": false, "salt": "7640d1db441f4a919924518c3dfa65f9", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 3, "deleted": false}, "fflag_feat_back_optic_1579_force_memory_profiler": {"key": "fflag_feat_back_optic_1579_force_memory_profiler", "on": false, "prerequisites": [], "targets": [], "contextTargets": [], "rules": [], "fallthrough": {"variation": 1}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": false, "usingEnvironmentId": false}, "clientSide": false, "salt": "ca386034d55e4907a022740cbe2250e8", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 2, "deleted": false}, "fflag_feat_back_optic_428_scim_multi_mapping": {"key": "fflag_feat_back_optic_428_scim_multi_mapping", "on": false, "prerequisites": [], "targets": [], "contextTargets": [], "rules": [], "fallthrough": {"variation": 0}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": false, "usingEnvironmentId": false}, "clientSide": false, "salt": "361255f1f8a445b285c66737152783cb", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 8, "deleted": false}, "fflag_feat_backend_optic_427_org_member_role_throttling_short": {"key": "fflag_feat_backend_optic_427_org_member_role_throttling_short", "on": false, "prerequisites": [], "targets": [], "contextTargets": [], "rules": [], "fallthrough": {"variation": 0}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": false, "usingEnvironmentId": false}, "clientSide": false, "salt": "47de2782717d44c0a1d770a1e64e3760", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 2, "deleted": false}, "fflag_feat_bros_35_api_users_performance": {"key": "fflag_feat_bros_35_api_users_performance", "on": false, "prerequisites": [], "targets": [], "contextTargets": [], "rules": [], "fallthrough": {"variation": 0}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": false, "usingEnvironmentId": false}, "clientSide": false, "salt": "813608a46e2d482ea1898f57bd84e43f", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 2, "deleted": false}, "fflag_feat_dev_2755_regions_list_grouped_by_labels_with_ordered_collapse_short": {"key": "fflag_feat_dev_2755_regions_list_grouped_by_labels_with_ordered_collapse_short", "on": false, "prerequisites": [], "targets": [], "contextTargets": [], "rules": [], "fallthrough": {"variation": 0}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": false, "usingEnvironmentId": false}, "clientSide": false, "salt": "78df3c3594d1489c876dc8bb1bc5608e", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 5, "deleted": false}, "fflag_feat_dia_1528_gemini_models_support_vertex_ai_support_short": {"key": "fflag_feat_dia_1528_gemini_models_support_vertex_ai_support_short", "on": false, "prerequisites": [], "targets": [], "contextTargets": [], "rules": [], "fallthrough": {"variation": 0}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": false, "usingEnvironmentId": false}, "clientSide": false, "salt": "ca9de1fc67254ad695559d73292c7ae9", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 2, "deleted": false}, "fflag_feat_dia_1661_improve_prompts_discovery_and_conversion": {"key": "fflag_feat_dia_1661_improve_prompts_discovery_and_conversion", "on": false, "prerequisites": [], "targets": [], "contextTargets": [], "rules": [], "fallthrough": {"variation": 0}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": false, "usingEnvironmentId": false}, "clientSide": false, "salt": "dd91c724ccc84009874e458acc785d1f", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 3, "deleted": false}, "fflag_feat_dia_1697_product_tour_short": {"key": "fflag_feat_dia_1697_product_tour_short", "on": false, "prerequisites": [], "targets": [], "contextTargets": [], "rules": [], "fallthrough": {"variation": 0}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": false, "usingEnvironmentId": false}, "clientSide": false, "salt": "c189d5e451df4baead35fbb4c0664652", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 3, "deleted": false}, "fflag_feat_dia_1920_project_creation_sample_data_short": {"key": "fflag_feat_dia_1920_project_creation_sample_data_short", "on": false, "prerequisites": [], "targets": [], "contextTargets": [], "rules": [], "fallthrough": {"variation": 0}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": false, "usingEnvironmentId": false}, "clientSide": false, "salt": "9f81fdc5e214419f8715744315e2b050", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 2, "deleted": false}, "fflag_feat_dia_1925_view_sample_raw_json_short": {"key": "fflag_feat_dia_1925_view_sample_raw_json_short", "on": false, "prerequisites": [], "targets": [], "contextTargets": [], "rules": [], "fallthrough": {"variation": 0}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": false, "usingEnvironmentId": false}, "clientSide": false, "salt": "ada8018a7d124ed3976aba1e0ae85c81", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 2, "deleted": false}, "fflag_feat_dia_2092_multitasks_per_storage_link": {"key": "fflag_feat_dia_2092_multitasks_per_storage_link", "on": true, "prerequisites": [], "targets": [], "contextTargets": [], "rules": [], "fallthrough": {"variation": 0}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": false, "usingEnvironmentId": false}, "clientSide": false, "salt": "6961906955144de79ef62428d656b457", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 4, "deleted": false}, "fflag_feat_front_dev-2536_comment_notifications_short": {"key": "fflag_feat_front_dev-2536_comment_notifications_short", "on": false, "prerequisites": [], "targets": [], "contextTargets": [], "rules": [], "fallthrough": {"variation": 0}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": false, "usingEnvironmentId": false}, "clientSide": false, "salt": "ae80730e3ab04b27b8d601288ca4d306", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 5, "deleted": false}, "fflag_feat_front_dev_3051_trial_experience_short": {"key": "fflag_feat_front_dev_3051_trial_experience_short", "on": false, "prerequisites": [], "targets": [], "contextTargets": [], "rules": [], "fallthrough": {"variation": 0}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": false, "usingEnvironmentId": false}, "clientSide": false, "salt": "d47457aa23254d0e8352d7be1b33e2e7", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 4, "deleted": false}, "fflag_feat_front_dev_3077_repeater_tag_loading_performance_short": {"key": "fflag_feat_front_dev_3077_repeater_tag_loading_performance_short", "on": true, "prerequisites": [], "targets": [], "contextTargets": [], "rules": [], "fallthrough": {"variation": 0}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": false, "usingEnvironmentId": false}, "clientSide": false, "salt": "0ec959822b674511aae50586b7eae9fc", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 3, "deleted": false}, "fflag_feat_front_dev_3873_labeling_ui_improvements_short": {"key": "fflag_feat_front_dev_3873_labeling_ui_improvements_short", "on": true, "prerequisites": [], "targets": [], "contextTargets": [], "rules": [], "fallthrough": {"variation": 0}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": true, "usingEnvironmentId": false}, "clientSide": false, "salt": "ea02c8fb80a34239a5aedc42049069df", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 5, "deleted": false}, "fflag_feat_front_dia_1747_projects_list_banner": {"key": "fflag_feat_front_dia_1747_projects_list_banner", "on": false, "prerequisites": [], "targets": [], "contextTargets": [], "rules": [], "fallthrough": {"variation": 0}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": false, "usingEnvironmentId": false}, "clientSide": false, "salt": "21bfa474c82b4ab0b89314fbecd164cc", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 3, "deleted": false}, "fflag_feat_front_leap_1173_disable_postpone_skip_short": {"key": "fflag_feat_front_leap_1173_disable_postpone_skip_short", "on": false, "prerequisites": [], "targets": [], "contextTargets": [], "rules": [], "fallthrough": {"variation": 0}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": false, "usingEnvironmentId": false}, "clientSide": false, "salt": "23ef99b0cc8a4a3c99a09e47268cabd6", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 4, "deleted": false}, "fflag_feat_front_leap_1198_unsaved_changes_180724": {"key": "fflag_feat_front_leap_1198_unsaved_changes_180724", "on": true, "prerequisites": [], "targets": [], "contextTargets": [], "rules": [], "fallthrough": {"variation": 0}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": false, "usingEnvironmentId": false}, "clientSide": false, "salt": "fd634284baf64b30aae8a7fcfb13db18", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 5, "deleted": false}, "fflag_feat_front_leap_1424_grid_preview_short": {"key": "fflag_feat_front_leap_1424_grid_preview_short", "on": true, "prerequisites": [], "targets": [], "contextTargets": [], "rules": [], "fallthrough": {"variation": 0}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": false, "usingEnvironmentId": false}, "clientSide": false, "salt": "dfdd8f564ddc4111b73f59594a17502d", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 3, "deleted": false}, "fflag_feat_front_leap_1973_adjustable_spans_090425_short": {"key": "fflag_feat_front_leap_1973_adjustable_spans_090425_short", "on": true, "prerequisites": [], "targets": [], "contextTargets": [], "rules": [], "fallthrough": {"variation": 0}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": false, "usingEnvironmentId": false}, "clientSide": false, "salt": "7c699fb3534c4dd694aa9d59256e0f15", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 4, "deleted": false}, "fflag_feat_front_leap_2036_annotations_summary": {"key": "fflag_feat_front_leap_2036_annotations_summary", "on": false, "prerequisites": [], "targets": [], "contextTargets": [], "rules": [], "fallthrough": {"variation": 0}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": false, "usingEnvironmentId": false}, "clientSide": false, "salt": "a0c67bbfb44743d6903c9445033c59c3", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 2, "deleted": false}, "fflag_feat_front_leap_482_self_serve_short": {"key": "fflag_feat_front_leap_482_self_serve_short", "on": false, "prerequisites": [], "targets": [], "contextTargets": [], "rules": [], "fallthrough": {"variation": 0}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": false, "usingEnvironmentId": false}, "clientSide": false, "salt": "775b9819acb6465baae3d091647ef9b4", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 6, "deleted": false}, "fflag_feat_front_lops_134_find_similarity_29082023_short": {"key": "fflag_feat_front_lops_134_find_similarity_29082023_short", "on": true, "prerequisites": [], "targets": [], "contextTargets": [], "rules": [], "fallthrough": {"variation": 1}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": false, "usingEnvironmentId": false}, "clientSide": false, "salt": "d1390aeb04d14bc581c5e244a2871588", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 4, "deleted": false}, "fflag_feat_front_lops_86_datasets_storage_edit_short": {"key": "fflag_feat_front_lops_86_datasets_storage_edit_short", "on": false, "prerequisites": [], "targets": [], "contextTargets": [], "rules": [], "fallthrough": {"variation": 0}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": false, "usingEnvironmentId": false}, "clientSide": false, "salt": "30e0e8abb2fb410ba50bd4dba2f4a460", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 3, "deleted": false}, "fflag_feat_front_lsdv_4583_6_images_preloading_short": {"key": "fflag_feat_front_lsdv_4583_6_images_preloading_short", "on": true, "prerequisites": [], "targets": [], "contextTargets": [], "rules": [], "fallthrough": {"variation": 0}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": false, "usingEnvironmentId": false}, "clientSide": false, "salt": "9b2f7864ca074f5f8a0a4bfcae57bf86", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 5, "deleted": false}, "fflag_feat_front_lsdv_4583_multi_image_segmentation_short": {"key": "fflag_feat_front_lsdv_4583_multi_image_segmentation_short", "on": true, "prerequisites": [], "targets": [], "contextTargets": [], "rules": [], "fallthrough": {"variation": 0}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": false, "usingEnvironmentId": false}, "clientSide": false, "salt": "2fd5d4989a174d679bde905ada2548af", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 5, "deleted": false}, "fflag_feat_front_lsdv_5451_async_taxonomy_110823_short": {"key": "fflag_feat_front_lsdv_5451_async_taxonomy_110823_short", "on": true, "prerequisites": [], "targets": [], "contextTargets": [], "rules": [], "fallthrough": {"variation": 0}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": false, "usingEnvironmentId": false}, "clientSide": false, "salt": "384b6f4ac48b4f1fa88d7563e50973e1", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 5, "deleted": false}, "fflag_feat_front_lsdv_5452_taxonomy_labeling_110823_short": {"key": "fflag_feat_front_lsdv_5452_taxonomy_labeling_110823_short", "on": true, "prerequisites": [], "targets": [], "contextTargets": [], "rules": [], "fallthrough": {"variation": 0}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": false, "usingEnvironmentId": false}, "clientSide": false, "salt": "d8b8859f42d044eb912ca2734826a58e", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 4, "deleted": false}, "fflag_feat_front_lsdv_e_278_contextual_scrolling_short": {"key": "fflag_feat_front_lsdv_e_278_contextual_scrolling_short", "on": true, "prerequisites": [], "targets": [], "contextTargets": [], "rules": [], "fallthrough": {"variation": 0}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": false, "usingEnvironmentId": false}, "clientSide": false, "salt": "04cfe1dec05344db9c20e81663546744", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 6, "deleted": false}, "fflag_feat_front_lsdv_e_297_increase_oss_to_enterprise_adoption_short": {"key": "fflag_feat_front_lsdv_e_297_increase_oss_to_enterprise_adoption_short", "on": true, "prerequisites": [], "targets": [], "contextTargets": [], "rules": [], "fallthrough": {"variation": 0}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": false, "usingEnvironmentId": false}, "clientSide": false, "salt": "3e7648d6922e412a9e512fbfed2074c9", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 16, "deleted": false}, "fflag_feat_front_optic_1217_theme_toggle_short": {"key": "fflag_feat_front_optic_1217_theme_toggle_short", "on": true, "prerequisites": [], "targets": [], "contextTargets": [], "rules": [], "fallthrough": {"variation": 0}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": false, "usingEnvironmentId": false}, "clientSide": false, "salt": "1b3bca98ba6c4a7bb5559e50c280cf4a", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 3, "deleted": false}, "fflag_feat_front_optic_1351_use_new_projects_counts_api_short": {"key": "fflag_feat_front_optic_1351_use_new_projects_counts_api_short", "on": false, "prerequisites": [], "targets": [], "contextTargets": [], "rules": [], "fallthrough": {"variation": 0}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": false, "usingEnvironmentId": false}, "clientSide": false, "salt": "8fb14818f38c44e39f11993e13ab535a", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 3, "deleted": false}, "fflag_feat_front_optic_1417_improve_project_list_cache_short": {"key": "fflag_feat_front_optic_1417_improve_project_list_cache_short", "on": false, "prerequisites": [], "targets": [], "contextTargets": [], "rules": [], "fallthrough": {"variation": 0}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": false, "usingEnvironmentId": false}, "clientSide": false, "salt": "3d06ae3d3c014cd4a82dcf269d077ea5", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 4, "deleted": false}, "fflag_feat_front_optic_1479_improve_image_tag_memory_usage_short": {"key": "fflag_feat_front_optic_1479_improve_image_tag_memory_usage_short", "on": true, "prerequisites": [], "targets": [], "contextTargets": [], "rules": [], "fallthrough": {"variation": 0}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": false, "usingEnvironmentId": false}, "clientSide": false, "salt": "21716e7aae704a6cb04f7d72ee5cff33", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 3, "deleted": false}, "fflag_feat_front_optic_1553_url_based_region_visibility_short": {"key": "fflag_feat_front_optic_1553_url_based_region_visibility_short", "on": true, "prerequisites": [], "targets": [], "contextTargets": [], "rules": [], "fallthrough": {"variation": 0}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": false, "usingEnvironmentId": false}, "clientSide": false, "salt": "b4340048580d4eb3b2bc524d748ded76", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 3, "deleted": false}, "fflag_feat_front_optic_1746_improve_global_error_messages_short": {"key": "fflag_feat_front_optic_1746_improve_global_error_messages_short", "on": true, "prerequisites": [], "targets": [], "contextTargets": [], "rules": [], "fallthrough": {"variation": 0}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": false, "usingEnvironmentId": false}, "clientSide": false, "salt": "e31a1913483c4765a2eda65c37577ff3", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 3, "deleted": false}, "fflag_feat_front_optic_1856_ask_ai_support_ticket_creation_short": {"key": "fflag_feat_front_optic_1856_ask_ai_support_ticket_creation_short", "on": false, "prerequisites": [], "targets": [], "contextTargets": [], "rules": [], "fallthrough": {"variation": 0}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": false, "usingEnvironmentId": false}, "clientSide": false, "salt": "32e80d52fc3b4aed8d3c6a2dcd69963c", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 2, "deleted": false}, "fflag_feat_front_optic_66_lazy_chart_evaluation_19092023_short": {"key": "fflag_feat_front_optic_66_lazy_chart_evaluation_19092023_short", "on": true, "prerequisites": [], "targets": [], "contextTargets": [], "rules": [], "fallthrough": {"variation": 0}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": false, "usingEnvironmentId": false}, "clientSide": false, "salt": "fa6d1be06b5e4078930ac6bb768c1f5d", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 4, "deleted": false}, "fflag_feat_front_optic_767_annotator_project_multiselect_short": {"key": "fflag_feat_front_optic_767_annotator_project_multiselect_short", "on": false, "prerequisites": [], "targets": [], "contextTargets": [], "rules": [], "fallthrough": {"variation": 0}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": false, "usingEnvironmentId": false}, "clientSide": false, "salt": "bdc3dbe4886b4e34a39856f59ad5e71a", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 2, "deleted": false}, "fflag_feat_front_optic_optic_1442_hubspot_talk_to_sales_modal_short": {"key": "fflag_feat_front_optic_optic_1442_hubspot_talk_to_sales_modal_short", "on": false, "prerequisites": [], "targets": [], "contextTargets": [], "rules": [], "fallthrough": {"variation": 0}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": false, "usingEnvironmentId": false}, "clientSide": false, "salt": "d5eba9242d7b470abdf91e06b284e5dd", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 2, "deleted": false}, "fflag_feat_front_prod_281_project_list_search_19072023_short": {"key": "fflag_feat_front_prod_281_project_list_search_19072023_short", "on": false, "prerequisites": [], "targets": [], "contextTargets": [], "rules": [], "fallthrough": {"variation": 0}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": false, "usingEnvironmentId": false}, "clientSide": false, "salt": "0fc1088b43f04858ac2e98e141a46559", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 3, "deleted": false}, "fflag_feat_optic_1025_zendesk_widget_integration": {"key": "fflag_feat_optic_1025_zendesk_widget_integration", "on": false, "prerequisites": [], "targets": [], "contextTargets": [], "rules": [], "fallthrough": {"variation": 1}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": false, "usingEnvironmentId": false}, "clientSide": false, "salt": "98f5d577937c47528ce3105baf9f7f91", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 3, "deleted": false}, "fflag_feat_optic_161_project_settings_for_low_agreement_threshold_score_short": {"key": "fflag_feat_optic_161_project_settings_for_low_agreement_threshold_score_short", "on": false, "prerequisites": [], "targets": [], "contextTargets": [], "rules": [], "fallthrough": {"variation": 1}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": false, "usingEnvironmentId": false}, "clientSide": false, "salt": "b0c52722429b4113a3c0b41306279dfb", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 5, "deleted": false}, "fflag_feat_optic_2123_audio_spectrograms": {"key": "fflag_feat_optic_2123_audio_spectrograms", "on": false, "prerequisites": [], "targets": [], "contextTargets": [], "rules": [], "fallthrough": {"variation": 0}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": false, "usingEnvironmentId": false}, "clientSide": false, "salt": "22c39d2de39640dbbce81c06559cfce7", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 2, "deleted": false}, "fflag_feat_optic_2125_timeseries_sync": {"key": "fflag_feat_optic_2125_timeseries_sync", "on": false, "prerequisites": [], "targets": [], "contextTargets": [], "rules": [], "fallthrough": {"variation": 0}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": false, "usingEnvironmentId": false}, "clientSide": false, "salt": "c97ee12f1c2a46918bffcda57fbd822b", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 2, "deleted": false}, "fflag_feat_optic_378_limit_projects_per_page_to_ten_short": {"key": "fflag_feat_optic_378_limit_projects_per_page_to_ten_short", "on": false, "prerequisites": [], "targets": [], "contextTargets": [], "rules": [], "fallthrough": {"variation": 1}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": false, "usingEnvironmentId": false}, "clientSide": false, "salt": "ba4bc211dec84a63b9bd523ad75a1192", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 2, "deleted": false}, "fflag_feat_optic_650_target_storage_task_format_long": {"key": "fflag_feat_optic_650_target_storage_task_format_long", "on": false, "prerequisites": [], "targets": [], "contextTargets": [], "rules": [], "fallthrough": {"variation": 0}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": false, "usingEnvironmentId": false}, "clientSide": false, "salt": "91ce1d8a88364b879d17d679d50360b4", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 3, "deleted": false}, "fflag_feat_optic_67_drag_and_drop_charts": {"key": "fflag_feat_optic_67_drag_and_drop_charts", "on": false, "prerequisites": [], "targets": [], "contextTargets": [], "rules": [], "fallthrough": {"variation": 0}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": false, "usingEnvironmentId": false}, "clientSide": false, "salt": "4923bc1812ba4b1fa10b701540668464", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 2, "deleted": false}, "fflag_feat_root_11_support_jsonl_cloud_storage": {"key": "fflag_feat_root_11_support_jsonl_cloud_storage", "on": false, "prerequisites": [], "targets": [], "contextTargets": [], "rules": [], "fallthrough": {"variation": 0}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": false, "usingEnvironmentId": false}, "clientSide": false, "salt": "85e018dcd2e64c689a61ee7ed3c5edb2", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 2, "deleted": false}, "fflag_feat_root_16_support_parquet_cloud_storage": {"key": "fflag_feat_root_16_support_parquet_cloud_storage", "on": false, "prerequisites": [], "targets": [], "contextTargets": [], "rules": [], "fallthrough": {"variation": 0}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": false, "usingEnvironmentId": false}, "clientSide": false, "salt": "19f34d7d3a734e56828582f4db176382", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 2, "deleted": false}, "fflag_feature_all_optic_1421_cold_start_v2": {"key": "fflag_feature_all_optic_1421_cold_start_v2", "on": false, "prerequisites": [], "targets": [], "contextTargets": [], "rules": [], "fallthrough": {"variation": 0}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": false, "usingEnvironmentId": false}, "clientSide": false, "salt": "d57e006e30b849a0b33264c536a6f685", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 2, "deleted": false}, "fflag_feature_all_optic_1541_performance_score_on_latest_review_short": {"key": "fflag_feature_all_optic_1541_performance_score_on_latest_review_short", "on": false, "prerequisites": [], "targets": [], "contextTargets": [], "rules": [], "fallthrough": {"variation": 0}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": false, "usingEnvironmentId": false}, "clientSide": false, "salt": "9a52adb8dcdf49e4b1c9d18ec8441ec9", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 2, "deleted": false}, "fflag_fix_all_leap_877_annotator_membership_api_03042024_short": {"key": "fflag_fix_all_leap_877_annotator_membership_api_03042024_short", "on": false, "prerequisites": [], "targets": [], "contextTargets": [], "rules": [], "fallthrough": {"variation": 0}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": false, "usingEnvironmentId": false}, "clientSide": false, "salt": "f04c36c72422448ba39bc3cc00622b31", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 3, "deleted": false}, "fflag_fix_all_lsdv_4711_cors_errors_accessing_task_data_short": {"key": "fflag_fix_all_lsdv_4711_cors_errors_accessing_task_data_short", "on": true, "prerequisites": [], "targets": [], "contextTargets": [], "rules": [], "fallthrough": {"variation": 0}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": false, "usingEnvironmentId": false}, "clientSide": false, "salt": "73eefabc9339475a972105320feba11a", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 3, "deleted": false}, "fflag_fix_all_lsdv_4813_async_export_conversion_22032023_short": {"key": "fflag_fix_all_lsdv_4813_async_export_conversion_22032023_short", "on": false, "prerequisites": [], "targets": [], "contextTargets": [], "rules": [], "fallthrough": {"variation": 0}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": false, "usingEnvironmentId": false}, "clientSide": false, "salt": "081693f1f8494644a4aa0cd80c5367f5", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 5, "deleted": false}, "fflag_fix_all_lsdv_4896_dm_actions_to_reviewers_20230403_short": {"key": "fflag_fix_all_lsdv_4896_dm_actions_to_reviewers_20230403_short", "on": false, "prerequisites": [], "targets": [], "contextTargets": [], "rules": [], "fallthrough": {"variation": 0}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": false, "usingEnvironmentId": false}, "clientSide": false, "salt": "76c7753b7a984dac8845a7673ee2dc06", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 3, "deleted": false}, "fflag_fix_all_lsdv_4971_async_reimport_09052023_short": {"key": "fflag_fix_all_lsdv_4971_async_reimport_09052023_short", "on": false, "prerequisites": [], "targets": [], "contextTargets": [], "rules": [], "fallthrough": {"variation": 0}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": false, "usingEnvironmentId": false}, "clientSide": false, "salt": "b86bffea16ca48008e2bf5fbea526178", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 2, "deleted": false}, "fflag_fix_all_optic_18_dashboard_label_distribution_chart_async_22082023_short": {"key": "fflag_fix_all_optic_18_dashboard_label_distribution_chart_async_22082023_short", "on": false, "prerequisites": [], "targets": [], "contextTargets": [], "rules": [], "fallthrough": {"variation": 0}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": false, "usingEnvironmentId": false}, "clientSide": false, "salt": "9d72a066d0444d80815883d0208996dc", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 4, "deleted": false}, "fflag_fix_all_optic_79_task_count_is_wrong_short": {"key": "fflag_fix_all_optic_79_task_count_is_wrong_short", "on": false, "prerequisites": [], "targets": [], "contextTargets": [], "rules": [], "fallthrough": {"variation": 0}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": false, "usingEnvironmentId": true}, "clientSide": true, "salt": "a5735954e251447386d319899754e210", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 4, "deleted": false}, "fflag_fix_back_dev_3668_review_stream_optimizaion_short": {"key": "fflag_fix_back_dev_3668_review_stream_optimizaion_short", "on": false, "prerequisites": [], "targets": [], "contextTargets": [], "rules": [], "fallthrough": {"variation": 0}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": true, "usingEnvironmentId": false}, "clientSide": false, "salt": "58a0578267584d09a8467abe746a4897", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 2, "deleted": false}, "fflag_fix_back_dev_4174_overlap_issue_experiments_10012023_short": {"key": "fflag_fix_back_dev_4174_overlap_issue_experiments_10012023_short", "on": false, "prerequisites": [], "targets": [], "contextTargets": [], "rules": [], "fallthrough": {"variation": 0}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": false, "usingEnvironmentId": false}, "clientSide": false, "salt": "8bcf48d51d93450c89981129796f1e48", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 5, "deleted": false}, "fflag_fix_back_dev_4185_next_task_additional_logging_long": {"key": "fflag_fix_back_dev_4185_next_task_additional_logging_long", "on": false, "prerequisites": [], "targets": [], "contextTargets": [], "rules": [], "fallthrough": {"variation": 0}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": false, "usingEnvironmentId": false}, "clientSide": false, "salt": "50327b7ef2a146dc9aff924769111066", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 2, "deleted": false}, "fflag_fix_back_leap_1818_set_convert_background_failure_logging_02062025_short": {"key": "fflag_fix_back_leap_1818_set_convert_background_failure_logging_02062025_short", "on": true, "prerequisites": [], "targets": [], "contextTargets": [], "rules": [], "fallthrough": {"variation": 0}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": false, "usingEnvironmentId": false}, "clientSide": false, "salt": "d6b27cf5610448b1bea29d4af46e0043", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 3, "deleted": false}, "fflag_fix_back_leap_612_explore_review_09042024_short": {"key": "fflag_fix_back_leap_612_explore_review_09042024_short", "on": false, "prerequisites": [], "targets": [], "contextTargets": [], "rules": [], "fallthrough": {"variation": 0}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": false, "usingEnvironmentId": false}, "clientSide": false, "salt": "a39035a36ba447c5a8089446f649ae94", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 2, "deleted": false}, "fflag_fix_back_lsdv_4523_show_overlap_first_order_27022023_short": {"key": "fflag_fix_back_lsdv_4523_show_overlap_first_order_27022023_short", "on": false, "prerequisites": [], "targets": [], "contextTargets": [], "rules": [], "fallthrough": {"variation": 0}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": false, "usingEnvironmentId": false}, "clientSide": false, "salt": "69f53396c78443738f4cbb0afa203960", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 2, "deleted": false}, "fflag_fix_back_lsdv_4604_excess_sql_queries_in_api_short": {"key": "fflag_fix_back_lsdv_4604_excess_sql_queries_in_api_short", "on": true, "prerequisites": [], "targets": [], "contextTargets": [], "rules": [], "fallthrough": {"variation": 0}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": false, "usingEnvironmentId": false}, "clientSide": false, "salt": "439816e9dbcc4c27b600cc97c9a25e6b", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 5, "deleted": false}, "fflag_fix_back_lsdv_4929_limit_exports_10042023_short": {"key": "fflag_fix_back_lsdv_4929_limit_exports_10042023_short", "on": true, "prerequisites": [], "targets": [], "contextTargets": [], "rules": [], "fallthrough": {"variation": 0}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": false, "usingEnvironmentId": false}, "clientSide": false, "salt": "bb069bd904a64607b36ea05d01651565", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 3, "deleted": false}, "fflag_fix_back_lsdv_5425_1_enable_permissions_restrictions_for_annotators_long": {"key": "fflag_fix_back_lsdv_5425_1_enable_permissions_restrictions_for_annotators_long", "on": false, "prerequisites": [], "targets": [], "contextTargets": [], "rules": [], "fallthrough": {"variation": 0}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": false, "usingEnvironmentId": false}, "clientSide": false, "salt": "b7efe0dcbda547e9937a2340e40b13d4", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 6, "deleted": false}, "fflag_fix_back_optic_1380_dashboard_task_chart_grouped_by_completed_at_reviewed_at_short": {"key": "fflag_fix_back_optic_1380_dashboard_task_chart_grouped_by_completed_at_reviewed_at_short", "on": true, "prerequisites": [], "targets": [], "contextTargets": [], "rules": [], "fallthrough": {"variation": 0}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": false, "usingEnvironmentId": false}, "clientSide": false, "salt": "5d10b576ced444f9ae782e07cd3d5ef2", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 3, "deleted": false}, "fflag_fix_back_optic_1407_optimize_tasks_api_pagination_counts": {"key": "fflag_fix_back_optic_1407_optimize_tasks_api_pagination_counts", "on": true, "prerequisites": [], "targets": [], "contextTargets": [], "rules": [], "fallthrough": {"variation": 0}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": false, "usingEnvironmentId": false}, "clientSide": false, "salt": "77f607e6b5354766ae6dbd701f57b849", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 5, "deleted": false}, "fflag_fix_font_lsdv_3009_draft_saving_stuck_130223_short": {"key": "fflag_fix_font_lsdv_3009_draft_saving_stuck_130223_short", "on": false, "prerequisites": [], "targets": [], "contextTargets": [], "rules": [], "fallthrough": {"variation": 0}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": false, "usingEnvironmentId": false}, "clientSide": false, "salt": "d2e1fba99485401ab1df61c4d47b4f73", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 3, "deleted": false}, "fflag_fix_front_dev_1284_auto_detect_undo_281022_short": {"key": "fflag_fix_front_dev_1284_auto_detect_undo_281022_short", "on": true, "prerequisites": [], "targets": [], "contextTargets": [], "rules": [], "fallthrough": {"variation": 0}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": true, "usingEnvironmentId": false}, "clientSide": false, "salt": "ae9a4fcd489640f1985457056b426a4e", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 3, "deleted": false}, "fflag_fix_front_dev_2918_labeling_filtered_paragraphs_250822_short": {"key": "fflag_fix_front_dev_2918_labeling_filtered_paragraphs_250822_short", "on": false, "prerequisites": [], "targets": [], "contextTargets": [], "rules": [], "fallthrough": {"variation": 0}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": false, "usingEnvironmentId": false}, "clientSide": false, "salt": "77a3e3714ab54abfbcf476f654783502", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 2, "deleted": false}, "fflag_fix_front_dev_3377_image_regions_shift_on_resize_280922_short": {"key": "fflag_fix_front_dev_3377_image_regions_shift_on_resize_280922_short", "on": true, "prerequisites": [], "targets": [], "contextTargets": [], "rules": [], "fallthrough": {"variation": 0}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": true, "usingEnvironmentId": false}, "clientSide": false, "salt": "d3f36f0fca084c568cef2d0ffe92c574", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 3, "deleted": false}, "fflag_fix_front_dev_3391_interactive_view_all": {"key": "fflag_fix_front_dev_3391_interactive_view_all", "on": false, "prerequisites": [], "targets": [], "contextTargets": [], "rules": [], "fallthrough": {"variation": 0}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": true, "usingEnvironmentId": false}, "clientSide": false, "salt": "4a8dad5b7a9547b19136f766ff03376e", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 3, "deleted": false}, "fflag_fix_front_dev_3793_relative_coords_short": {"key": "fflag_fix_front_dev_3793_relative_coords_short", "on": true, "prerequisites": [], "targets": [], "contextTargets": [], "rules": [], "fallthrough": {"variation": 0}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": false, "usingEnvironmentId": false}, "clientSide": false, "salt": "cf155cb2859f4f21a69cc0c90b0559e6", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 6, "deleted": false}, "fflag_fix_front_leap_218_improve_performance_of_taxonomy_search_short": {"key": "fflag_fix_front_leap_218_improve_performance_of_taxonomy_search_short", "on": true, "prerequisites": [], "targets": [], "contextTargets": [], "rules": [], "fallthrough": {"variation": 0}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": false, "usingEnvironmentId": false}, "clientSide": false, "salt": "c4514d3597fa4f0bac01d6230b348462", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 5, "deleted": false}, "fflag_fix_front_leap_32_zoom_perf_190923_short": {"key": "fflag_fix_front_leap_32_zoom_perf_190923_short", "on": true, "prerequisites": [], "targets": [{"contextKind": "user", "variation": 1, "values": ["mi<PERSON><PERSON><PERSON>@heartex.e2e"]}], "contextTargets": [{"contextKind": "user", "variation": 1, "values": []}], "rules": [], "fallthrough": {"variation": 0}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": false, "usingEnvironmentId": false}, "clientSide": false, "salt": "27d9dd66f4474b61b9ac5c0f03582209", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 7, "deleted": false}, "fflag_fix_front_leap_443_select_annotation_once": {"key": "fflag_fix_front_leap_443_select_annotation_once", "on": false, "prerequisites": [], "targets": [], "contextTargets": [], "rules": [], "fallthrough": {"variation": 0}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": false, "usingEnvironmentId": false}, "clientSide": false, "salt": "7fa01b9222564e5b9e3c415d48effd01", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 3, "deleted": false}, "fflag_fix_front_lsdv_4600_lead_time_27072023_short": {"key": "fflag_fix_front_lsdv_4600_lead_time_27072023_short", "on": true, "prerequisites": [], "targets": [], "contextTargets": [], "rules": [], "fallthrough": {"variation": 1}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": false, "usingEnvironmentId": false}, "clientSide": false, "salt": "c732cda2ecf64b589fe2bdc90208e3ec", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 4, "deleted": false}, "fflag_fix_front_lsdv_4620_memory_leaks_100723_short": {"key": "fflag_fix_front_lsdv_4620_memory_leaks_100723_short", "on": true, "prerequisites": [], "targets": [], "contextTargets": [], "rules": [], "fallthrough": {"variation": 0}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": false, "usingEnvironmentId": false}, "clientSide": false, "salt": "991cf0497e2b4505a928709b04218633", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 7, "deleted": false}, "fflag_fix_front_lsdv_4930_selection_tool_fixes_240423_short": {"key": "fflag_fix_front_lsdv_4930_selection_tool_fixes_240423_short", "on": true, "prerequisites": [], "targets": [], "contextTargets": [], "rules": [], "fallthrough": {"variation": 0}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": false, "usingEnvironmentId": false}, "clientSide": false, "salt": "ea3270a37cdb42088d980ed1e4014bef", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 3, "deleted": false}, "fflag_fix_front_lsdv_4998_missed_dynamic_children_030523_short": {"key": "fflag_fix_front_lsdv_4998_missed_dynamic_children_030523_short", "on": true, "prerequisites": [], "targets": [], "contextTargets": [], "rules": [], "fallthrough": {"variation": 0}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": false, "usingEnvironmentId": false}, "clientSide": false, "salt": "fcec745463d34dafa13c350d9a6e852c", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 3, "deleted": false}, "fflag_fix_front_optic_1418_cache_members_manager_apis_short": {"key": "fflag_fix_front_optic_1418_cache_members_manager_apis_short", "on": false, "prerequisites": [], "targets": [], "contextTargets": [], "rules": [], "fallthrough": {"variation": 0}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": false, "usingEnvironmentId": false}, "clientSide": false, "salt": "15587c19c881430081d2fa63699543b9", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 3, "deleted": false}, "fflag_fix_front_optic_1608_improve_video_frame_seek_precision_short": {"key": "fflag_fix_front_optic_1608_improve_video_frame_seek_precision_short", "on": true, "prerequisites": [], "targets": [], "contextTargets": [], "rules": [], "fallthrough": {"variation": 0}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": false, "usingEnvironmentId": false}, "clientSide": false, "salt": "840ef429000b4d5e9656830ed46692cf", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 5, "deleted": false}, "fflag_fix_leap_2052_detect_empty_filters_at_next_task_endpoint_short": {"key": "fflag_fix_leap_2052_detect_empty_filters_at_next_task_endpoint_short", "on": true, "prerequisites": [], "targets": [], "contextTargets": [], "rules": [], "fallthrough": {"variation": 0}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": false, "usingEnvironmentId": false}, "clientSide": false, "salt": "42b74824c185432fb0f4d76c2595480b", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 3, "deleted": false}, "fflag_fix_leap_246_multi_object_hotkeys_160124_short": {"key": "fflag_fix_leap_246_multi_object_hotkeys_160124_short", "on": true, "prerequisites": [], "targets": [], "contextTargets": [], "rules": [], "fallthrough": {"variation": 0}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": false, "usingEnvironmentId": false}, "clientSide": false, "salt": "800fcd0d7d4541349fef5f59174d58b5", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 3, "deleted": false}, "fflag_fix_leap_466_text_sanitization": {"key": "fflag_fix_leap_466_text_sanitization", "on": true, "prerequisites": [], "targets": [], "contextTargets": [], "rules": [], "fallthrough": {"variation": 0}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": false, "usingEnvironmentId": false}, "clientSide": false, "salt": "224d52df373e4061b517235600063392", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 5, "deleted": false}, "fflag_fix_optic_1259_lse_projects_read_apis_use_replica_short": {"key": "fflag_fix_optic_1259_lse_projects_read_apis_use_replica_short", "on": false, "prerequisites": [], "targets": [], "contextTargets": [], "rules": [], "fallthrough": {"variation": 0}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": false, "usingEnvironmentId": false}, "clientSide": false, "salt": "380538fdddcb4899b65e93d8d6e99482", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 2, "deleted": false}, "fflag_fix_optic_214_extra_blank_dashboard_charts_short": {"key": "fflag_fix_optic_214_extra_blank_dashboard_charts_short", "on": false, "prerequisites": [], "targets": [], "contextTargets": [], "rules": [], "fallthrough": {"variation": 0}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": false, "usingEnvironmentId": false}, "clientSide": false, "salt": "eedc4fd2b3624c4680c31864f07ef37c", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 2, "deleted": false}, "fflag_fix_optic_391_tasks_outside_low_agreement_project_counts_short": {"key": "fflag_fix_optic_391_tasks_outside_low_agreement_project_counts_short", "on": false, "prerequisites": [], "targets": [], "contextTargets": [], "rules": [], "fallthrough": {"variation": 0}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": false, "usingEnvironmentId": false}, "clientSide": false, "salt": "98f119d83a38497884bb16f1b57156d2", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 2, "deleted": false}, "fflag_front_dia_1150_ddisco_sneak_preview": {"key": "fflag_front_dia_1150_ddisco_sneak_preview", "on": false, "prerequisites": [], "targets": [], "contextTargets": [], "rules": [], "fallthrough": {"variation": 0}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": false, "usingEnvironmentId": false}, "clientSide": false, "salt": "6965c0995db048378c3513804fd02682", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 3, "deleted": false}, "fflag_optic_all_optic_1938_storage_proxy": {"key": "fflag_optic_all_optic_1938_storage_proxy", "on": true, "prerequisites": [], "targets": [], "contextTargets": [], "rules": [], "fallthrough": {"variation": 0}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": false, "usingEnvironmentId": false}, "clientSide": false, "salt": "1e0f07f7fc144f2dbce9df4460434415", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 8, "deleted": false}, "fix_backend_dev_3134_exclude_deactivated_users": {"key": "fix_backend_dev_3134_exclude_deactivated_users", "on": false, "prerequisites": [], "targets": [], "contextTargets": [], "rules": [], "fallthrough": {"variation": 0}, "offVariation": 1, "variations": [true, false], "clientSideAvailability": {"usingMobileKey": true, "usingEnvironmentId": false}, "clientSide": false, "salt": "fabfc44d04c6452aa4e1dee24b55af80", "trackEvents": false, "trackEventsFallthrough": false, "debugEventsUntilDate": null, "version": 4, "deleted": false}}}
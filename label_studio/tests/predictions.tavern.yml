---
test_name: crud
strict: false
marks:
- usefixtures:
  - django_live_url
stages:
- id: signup
  type: ref
- id: create_project
  type: ref
- id: create_task
  type: ref
- id: create_prediction
  name: create_prediction
  request:
    json:
      result:
        - value:
            choices:
            - Negative
          id: qpQHs3Yy4K
          from_name: sentiment
          to_name: text
          type: choices
      model_version: 'test'
      task: '{task_pk}'
    method: POST
    url: '{django_live_url}/api/predictions'
  response:
    save:
      json:
        prediction_pk: id
    status_code: 201
- id: get_prediction_by_id
  name: get_prediction_by_id
  request:
    method: GET
    url: '{django_live_url}/api/predictions/{prediction_pk}/'
  response:
    json:
      id: !int '{prediction_pk}'
      result:
        - value:
            choices:
            - Negative
          id: qpQHs3Yy4K
          from_name: sentiment
          to_name: text
          type: choices
    status_code: 200

- id: get_prediction_for_task
  name: get_prediction_for_task
  request:
    method: GET
    url: '{django_live_url}/api/predictions/?task={task_pk}'
  response:
    json:
      - id: !int '{prediction_pk}'
        result:
          - value:
              choices:
              - Negative
            id: qpQHs3Yy4K
            from_name: sentiment
            to_name: text
            type: choices
    status_code: 200
- id: update_prediction
  name: update_prediction
  request:
    json:
      model_version: 'test1'
    method: PATCH
    url: '{django_live_url}/api/predictions/{prediction_pk}/'
  response:
    status_code: 200
- id: get_prediction_by_id_after_update
  name: get_prediction_by_id_after_update
  request:
    method: GET
    url: '{django_live_url}/api/predictions/{prediction_pk}/'
  response:
    json:
      model_version: 'test1'
    status_code: 200
- id: delete_prediction
  name: delete_prediction
  request:
    method: DELETE
    url: '{django_live_url}/api/predictions/{prediction_pk}/'
  response:
    status_code: 204
- id: prediction_not_found_after_delete
  name: prediction_not_found_after_delete
  request:
    method: GET
    url: '{django_live_url}/api/predictions/{prediction_pk}/'
  response:
    status_code: 404


---
test_name: change_project_model_version_to_non_existent
strict: false
marks:
- usefixtures:
  - django_live_url
stages:
- id: signup
  type: ref
- id: create_project
  type: ref
- id: create_task
  type: ref
- name: change_project_model_to_non_existent
  request:
    method: PATCH
    url: '{django_live_url}/api/projects/{project_pk}'
    data:
      model_version: "test"
  response:
    status_code: 400
- id: create_prediction
  name: create_prediction
  request:
    json:
      result:
        - value:
            choices:
            - Negative
          id: qpQHs3Yy4K
          from_name: sentiment
          to_name: text
          type: choices
          model_version: "test"
      model_version: "test"
      task: '{task_pk}'
    method: POST
    url: '{django_live_url}/api/predictions'
  response:
    save:
      json:
        prediction_pk: id
    status_code: 201
- name: change_project_model_to_existent
  request:
    method: PATCH
    url: '{django_live_url}/api/projects/{project_pk}'
    data:
      model_version: "test"
  response:
    status_code: 200
- name: change_project_model_to_null
  request:
    method: PATCH
    url: '{django_live_url}/api/projects/{project_pk}'
    data:
      model_version: null
  response:
    status_code: 200


---
test_name: create_prediction_minified_format
strict: false
marks:
- usefixtures:
  - django_live_url
stages:
- id: signup
  type: ref
- id: create_project
  type: ref
- id: create_task
  type: ref
- id: create_prediction
  name: create_prediction
  request:
    json:
      result: Negative
      score: 0.987
      model_version: test_model
      task: '{task_pk}'
    method: POST
    url: '{django_live_url}/api/predictions'
  response:
    save:
      json:
        prediction_pk: id
    status_code: 201
- id: get_prediction_by_id
  name: get_prediction_by_id
  request:
    method: GET
    url: '{django_live_url}/api/predictions/{prediction_pk}/'
  response:
    json:
      id: !int '{prediction_pk}'
      result:
        - value:
            choices:
              - Negative
          from_name: label
          to_name: text
          type: choices
      score: 0.987
      model_version: test_model
    status_code: 200

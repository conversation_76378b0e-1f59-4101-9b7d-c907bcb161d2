---

name: Shared stages
description:
  Reusable test stages

stages:
  - id: signup
    name: Sign up
    request:
      url: "{django_live_url}/user/signup"
      data:
        email: <EMAIL>
        password: 12345678
      method: POST
    response:
      status_code: 302
  - id: login
    name: Login
    request:
      url: "{django_live_url}/user/login"
      data:
        email: <EMAIL>
        password: 12345678
      method: POST
    response:
      status_code: 302
  - id: logout
    name: logout
    request:
      url: "{django_live_url}/logout"
      method: GET
    response:
      status_code: 302
  - id: create_project
    name: create_project
    request:
      data:
        label_config: <View><Text name="text" value="$text"/><Choices name="label" toName="text"><Choice
          value="pos"/><Choice value="neg"/></Choices></View>
        title: Test Draft 1
        show_collab_predictions: true
      method: POST
      url: '{django_live_url}/api/projects'
    response:
      save:
        json:
          project_pk: id
      status_code: 201
  - id: create_project_empty_config
    name: create_project
    request:
      data:
        label_config: <View></View>
        title: Test Draft 1
        show_collab_predictions: true
      method: POST
      url: '{django_live_url}/api/projects'
    response:
      save:
        json:
          project_pk: id
      status_code: 201
  - id: create_project_image
    name: stage
    request:
      data:
        is_published: true
        label_config: <View><Image name="image" value="$image_url"/><Choices name="label"
          toName="image"><Choice value="pos"/><Choice value="neg"/></Choices></View>
        title: test_project_image_label_config
      method: POST
      url: '{django_live_url}/api/projects'
    response:
      save:
        json:
          project_pk: id
      status_code: 201
  - id: get_my_user
    name: Get my user
    request:
      url: "{django_live_url}/api/current-user/whoami"
      method: GET
    response:
      status_code: 200
      save:
        json:
          user_pk: id
          active_organization_pk: active_organization
  - id: create_task
    name: create_task
    request:
      json:
        data:
          text: 'Test text'
        project: '{project_pk}'
      method: POST
      url: '{django_live_url}/api/tasks'
    response:
      save:
        json:
          task_pk: id
      status_code: 201
  - id: get_user_token
    name: Get my user
    request:
      url: "{django_live_url}/api/current-user/token"
      method: GET
    response:
      status_code: 200
      save:
        json:
          user_token: token
  - id: import_task
    name: Import task
    request:
      url: "{django_live_url}/api/projects/{project_pk}/tasks"
      json:
        data:
          text: Test example phrase
      method: POST
      headers:
        content-type: application/json
    response:
      status_code: 201
      save:
        json:
          task_pk: id

  - id: create_annotation
    name: Create annotation
    request:
      url: "{django_live_url}/api/tasks/{task_pk}/annotations"
      method: POST
      headers:
        content-type: application/json
      json:
        result: [{'value': {'choices': ['pos']}, 'from_name': 'label', 'to_name': 'text', 'type': 'choices'}]
        lead_time: 34.56
    response:
      status_code: 201
      save:
        json:
          annotation_pk: id

  - id: get_invite_url
    name: Get invite link
    request:
      url: "{django_live_url}/api/invite"
      method: GET
    response:
      status_code: 200
      save:
        json:
          invite_url: invite_url
          invite_token: token

  - id: delete_all_tasks
    name: delete all tasks
    request:
      method: DELETE
      url: '{django_live_url}/api/projects/{project_pk}/tasks'
    response:
      status_code: 204

  - id: delete_annotation
    name: delete_annotation
    request:
      method: DELETE
      url: '{django_live_url}/api/annotations/{annotation_pk}/'
    response:
      status_code: 204


# TODO: add teardown/clean up methods
# - id: delete_task
#   name: Delete task
#   request:
#     url: "{django_live_url}/api/projects/{project_pk}"
#     method: DELETE
#     headers:
#       content-type: application/json
#   response:
#     status_code: 201


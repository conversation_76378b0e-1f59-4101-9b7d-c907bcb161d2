---
test_name: simple_export
strict: false
marks:
- usefixtures:
  - django_live_url
stages:
- id: signup
  type: ref
- name: stage
  request:
    headers:
      content-type: application/json
    json:
      label_config: <View><Text name="text" value="$text"/><Choices name="label" toName="text"><Choice
        value="pos"/><Choice value="neg"/></Choices></View>
    method: POST
    url: '{django_live_url}/api/projects'
  response:
    save:
      json:
        pk: id
    status_code: 201
- name: stage
  request:
    files:
      file: tests/test_suites/samples/lines.txt
    headers:
      content-type: multipart/form-data
    method: POST
    url: '{django_live_url}/api/projects/{pk}/import'
  response:
    json:
      annotation_count: 0
      prediction_count: 0
      task_count: 3
    status_code: 201
- name: stage
  request:
    method: GET
    url: '{django_live_url}/api/projects/{pk}/next'
  response:
    save:
      json:
        task_pk: id
    status_code: 200
- name: stage
  request:
    headers:
      content-type: application/json
    json:
      lead_time: 34.56
      result:
      - from_name: label
        to_name: text
        type: choices
        value:
          choices:
          - pos
    method: POST
    url: '{django_live_url}/api/tasks/{task_pk}/annotations'
  response:
    save:
      json:
        annotation_pk: id
    status_code: 201
- name: stage
  request:
    method: GET
    url: '{django_live_url}/api/projects/{pk}/export'
  response:
    status_code: 200
- name: stage
  request:
    method: GET
    url: '{django_live_url}/api/projects/{pk}/export?ids[]={task_pk}'
  response:
    status_code: 200
- name: stage
  request:
    method: GET
    url: '{django_live_url}/api/projects/{pk}/export?ids[]=9999999'
  response:
    status_code: 200


---
test_name: YOLO_export_option_polygons
strict: true
marks:
- usefixtures:
  - django_live_url
stages:
- id: signup
  type: ref
- name: stage
  request:
    headers:
      content-type: application/json
    json:
      label_config: <View><Header value="Select label and click the image to start"/><Image name="image" value="$image" /><PolygonLabels name="label" toName="image" strokeWidth="3" pointSize="small"><Label value="Airplane" background="red"/><Label value="Car" background="blue"/></PolygonLabels></View>
    method: POST
    url: '{django_live_url}/api/projects'
  response:
    save:
      json:
        pk: id
    status_code: 201
- name: stage_export
  request:
    method: GET
    url: '{django_live_url}/api/projects/{pk}/export/formats'
  response:
    json:
      - title: JSON
        description: List of items in raw JSON format stored in one JSON file. Use to export
          both the data and the annotations for a dataset. It's Label Studio Common Format
        link: https://labelstud.io/guide/export.html#JSON
        name: JSON
      - title: JSON-MIN
        description: List of items where only "from_name", "to_name" values from the raw
          JSON format are exported. Use to export only the annotations for a dataset.
        link: https://labelstud.io/guide/export.html#JSON-MIN
        name: JSON_MIN
      - title: CSV
        description: Results are stored as comma-separated values with the column names
          specified by the values of the "from_name" and "to_name" fields.
        link: https://labelstud.io/guide/export.html#CSV
        name: CSV
      - title: TSV
        description: Results are stored in tab-separated tabular file with column names
          specified by "from_name" "to_name" values
        link: https://labelstud.io/guide/export.html#TSV
        name: TSV
      - title: COCO
        description: Popular machine learning format used by the COCO dataset for object
          detection and image segmentation tasks with polygons and rectangles.
        link: https://labelstud.io/guide/export.html#COCO
        tags:
        - image segmentation
        - object detection
        - keypoints
        name: COCO
      - title: COCO with Images
        description: COCO format with images downloaded.
        link: https://labelstud.io/guide/export.html#COCO
        tags:
        - image segmentation
        - object detection
        - keypoints
        name: COCO_WITH_IMAGES
      - title: YOLO
        description: Popular TXT format is created for each image file. Each txt file contains
          annotations for the corresponding image file, that is object class, object coordinates,
          height & width.
        link: https://labelstud.io/guide/export.html#YOLO
        tags:
        - image segmentation
        - object detection
        - keypoints
        name: YOLO
      - title: YOLO with Images
        description: YOLO format with images downloaded.
        link: https://labelstud.io/guide/export.html#YOLO
        tags:
        - image segmentation
        - object detection
        - keypoints
        name: YOLO_WITH_IMAGES
      - title: YOLOv8 OBB
        description: Popular TXT format is created for each image file. Each txt file contains
          annotations for the corresponding image file. The YOLO OBB format designates bounding
          boxes by their four corner points with coordinates normalized between 0 and 1,
          so it is possible to export rotated objects.
        link: https://labelstud.io/guide/export.html#YOLO
        tags:
        - image segmentation
        - object detection
        name: YOLO_OBB
      - title: YOLOv8 OBB with Images
        description: YOLOv8 OBB format with images downloaded.
        link: https://labelstud.io/guide/export.html#YOLO
        tags:
        - image segmentation
        - object detection
        name: YOLO_OBB_WITH_IMAGES
      - title: CONLL2003
        description: Popular format used for the CoNLL-2003 named entity recognition challenge.
        link: https://labelstud.io/guide/export.html#CONLL2003
        tags:
        - sequence labeling
        - text tagging
        - named entity recognition
        name: CONLL2003
        disabled: true
      - title: Pascal VOC XML
        description: Popular XML format used for object detection and polygon image segmentation
          tasks.
        link: https://labelstud.io/guide/export.html#Pascal-VOC-XML
        tags:
        - image segmentation
        - object detection
        name: VOC
        disabled: true
      - title: Brush labels to NumPy
        description: Export your brush labels as NumPy 2d arrays. Each label outputs as
          one image.
        link: https://labelstud.io/guide/export.html#Brush-labels-to-NumPy-amp-PNG
        tags:
        - image segmentation
        name: BRUSH_TO_NUMPY
        disabled: true
      - title: Brush labels to PNG
        description: Export your brush labels as PNG images. Each label outputs as one image.
        link: https://labelstud.io/guide/export.html#Brush-labels-to-NumPy-amp-PNG
        tags:
        - image segmentation
        name: BRUSH_TO_PNG
        disabled: true
      - title: ASR Manifest
        description: Export audio transcription labels for automatic speech recognition
          as the JSON manifest format expected by NVIDIA NeMo models.
        link: https://labelstud.io/guide/export.html#ASR-MANIFEST
        tags:
        - speech recognition
        name: ASR_MANIFEST
        disabled: true
      - title: "Brush labels to COCO"
        description: "Export your brush labels as COCO format for segmentation tasks. Converts RLE encoded masks to COCO polygons."
        link: "https://labelstud.io/guide/export.html#COCO"
        tags:
          - "image segmentation"
          - "brush annotations"
        name: "BRUSH_TO_COCO"
        disabled: true


    status_code: 200


---
test_name: mig_export
strict: false
marks:
- usefixtures:
  - django_live_url
stages:
- id: signup
  type: ref
- name: stage
  request:
    headers:
      content-type: application/json
    json:
      label_config:
        <View>
          <Image name="image" valueList="$images"/>
            <RectangleLabels name="labels" toName="image">
              <Label value="Cat"/>
              <Label value="Dog"/>
            </RectangleLabels>
        </View>
    method: POST
    url: '{django_live_url}/api/projects'
  response:
    save:
      json:
        pk: id
    status_code: 201
- name: stage_export
  request:
    method: GET
    url: '{django_live_url}/api/projects/{pk}/export/formats'
  response:
    strict:
      - json:list_any_order
    json:
      - title: 'JSON'
        description: !anystr
        link: 'https://labelstud.io/guide/export.html#JSON'
        name: 'JSON'
      - title: 'JSON-MIN'
        description: !anystr
        link: 'https://labelstud.io/guide/export.html#JSON-MIN'
        name: 'JSON_MIN'
      - title: 'CSV'
        description: !anystr
        link: 'https://labelstud.io/guide/export.html#CSV'
        name: 'CSV'
      - title: 'TSV'
        description: !anystr
        link: 'https://labelstud.io/guide/export.html#TSV'
        name: 'TSV'
      - title: 'COCO'
        description: !anystr
        link: 'https://labelstud.io/guide/export.html#COCO'
        tags: ['image segmentation', 'object detection']
        name: 'COCO'
        disabled: true
      - title: 'YOLO'
        description: !anystr
        link: 'https://labelstud.io/guide/export.html#YOLO'
        tags: ['image segmentation', 'object detection']
        name: 'YOLO'
        disabled: true
      - title: 'YOLOv8 OBB'
        description: !anystr
        link: 'https://labelstud.io/guide/export.html#YOLO'
        tags: ['image segmentation', 'object detection']
        name: 'YOLO_OBB'
        disabled: true
      - title: 'CONLL2003'
        description: !anystr
        link: 'https://labelstud.io/guide/export.html#CONLL2003'
        tags: ['sequence labeling', 'text tagging', 'named entity recognition']
        name: 'CONLL2003'
        disabled: true
      - title: 'Pascal VOC XML'
        description: !anystr
        link: 'https://labelstud.io/guide/export.html#Pascal-VOC-XML'
        tags: ['image segmentation', 'object detection']
        name: 'VOC'
        disabled: true
      - title: 'Brush labels to NumPy'
        description: !anystr
        link: 'https://labelstud.io/guide/export.html#Brush-labels-to-NumPy-amp-PNG'
        tags: ['image segmentation']
        name: 'BRUSH_TO_NUMPY'
        disabled: true
      - title: 'Brush labels to PNG'
        description: !anystr
        link: 'https://labelstud.io/guide/export.html#Brush-labels-to-NumPy-amp-PNG'
        tags: ['image segmentation']
        name: 'BRUSH_TO_PNG'
        disabled: true
      - title: 'ASR Manifest'
        description: !anystr
        link: 'https://labelstud.io/guide/export.html#ASR-MANIFEST'
        tags: ['speech recognition']
        name: 'ASR_MANIFEST'
        disabled: true
    status_code: 200

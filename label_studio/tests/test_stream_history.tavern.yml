---
test_name: history
strict: true
marks:
- usefixtures:
  - django_live_url
stages:
- id: signup
  type: ref
- id: create_project
  type: ref
- id: import_task
  type: ref
# history is empty
- name: history
  request:
    method: GET
    url: '{django_live_url}/api/projects/{project_pk}/label-stream-history/'
  response:
    status_code: 200
    json: []

- name: get_next_task
  request:
    method: POST
    url: '{django_live_url}/api/dm/actions?project={project_pk}&id=next_task'
  response:
    status_code: 200

# only task in history
- name: history
  request:
    method: GET
    url: '{django_live_url}/api/projects/{project_pk}/label-stream-history/'
  response:
    status_code: 200
    json:
      - taskId: !int '{task_pk}'
        annotationId: null

- name: get_next_task
  request:
    method: POST
    url: '{django_live_url}/api/dm/actions?project={project_pk}&id=next_task'
  response:
    status_code: 200

# no duplication, still one task
- name: history
  request:
    method: GET
    url: '{django_live_url}/api/projects/{project_pk}/label-stream-history/'
  response:
    status_code: 200
    json:
      - taskId: !int '{task_pk}'
        annotationId: null

- id: create_annotation
  type: ref

# task and annotation in history
- name: history
  request:
    method: GET
    url: '{django_live_url}/api/projects/{project_pk}/label-stream-history/'
  response:
    status_code: 200
    json:
      - taskId: !int '{task_pk}'
        annotationId: !int '{annotation_pk}'

- id: delete_annotation
  type: ref

# annotation empty again
- name: history
  request:
    method: GET
    url: '{django_live_url}/api/projects/{project_pk}/label-stream-history/'
  response:
    status_code: 200
    json:
      - taskId: !int '{task_pk}'
        annotationId: null

- id: delete_all_tasks
  type: ref

# history is empty again
- name: history
  request:
    method: GET
    url: '{django_live_url}/api/projects/{project_pk}/label-stream-history/'
  response:
    status_code: 200
    json: []

---
test_name: history_limit
strict: true
marks:
- usefixtures:
  - django_live_url
  - label_stream_history_limit
stages:
- id: signup
  type: ref
- id: create_project
  type: ref
- id: import_task
  type: ref

- name: get_next_task
  request:
    method: POST
    url: '{django_live_url}/api/dm/actions?project={project_pk}&id=next_task'
  response:
    status_code: 200

# only task in history
- name: history
  request:
    method: GET
    url: '{django_live_url}/api/projects/{project_pk}/label-stream-history/'
  response:
    status_code: 200
    json:
      - taskId: !int '{task_pk}'
        annotationId: null

- id: create_annotation
  type: ref

# task and annotation in history
- name: history
  request:
    method: GET
    url: '{django_live_url}/api/projects/{project_pk}/label-stream-history/'
  response:
    status_code: 200
    json:
      - taskId: !int '{task_pk}'
        annotationId: !int '{annotation_pk}'

- id: import_task
  type: ref

- name: get_next_task
  request:
    method: POST
    url: '{django_live_url}/api/dm/actions?project={project_pk}&id=next_task'
  response:
    status_code: 200

# still one task in history
- name: history
  request:
    method: GET
    url: '{django_live_url}/api/projects/{project_pk}/label-stream-history/'
  response:
    status_code: 200
    json:
      - taskId: !int '{task_pk}'
        annotationId: null

---
test_name: image_upload
strict: false
marks:
- usefixtures:
  - django_live_url
stages:
- id: signup
  type: ref
- name: stage
  request:
    data:
      label_config: <View></View>
      title: Image upload
    method: POST
    url: '{django_live_url}/api/projects'
  response:
    save:
      json:
        project_pk: id
    status_code: 201
- name: stage
  request:
    files:
      image_0: tests/test_suites/samples/test_image.jpg
      image_1: tests/test_suites/samples/test_image.png
    headers:
      content-type: multipart/form-data
    method: POST
    url: '{django_live_url}/api/projects/{project_pk}/import'
  response:
    json:
      annotation_count: 0
      prediction_count: 0
      task_count: 2
    status_code: 201
- name: stage
  request:
    method: GET
    url: '{django_live_url}/api/projects/{project_pk}/tasks'
  response:
    status_code: 200

---
test_name: consistency_of_data_columns
strict: false
marks:
- usefixtures:
  - django_live_url
stages:
- id: signup
  type: ref
- name: stage
  request:
    data:
      label_config: <View></View>
      title: Check consistency of imported data columns
    method: POST
    url: '{django_live_url}/api/projects'
  response:
    save:
      json:
        project_pk: id
    status_code: 201
- name: stage
  request:
    files:
      csv_0: tests/test_suites/samples/test_1.csv
    headers:
      content-type: multipart/form-data
    method: POST
    url: '{django_live_url}/api/projects/{project_pk}/tasks/bulk'
  response:
    json:
      annotation_count: 0
      prediction_count: 0
      task_count: 2
    status_code: 201
- name: stage
  request:
    files:
      csv_0: tests/test_suites/samples/test_2.csv
    headers:
      content-type: multipart/form-data
    method: POST
    url: '{django_live_url}/api/projects/{project_pk}/tasks/bulk'
  response:
    json:
      annotation_count: 0
      prediction_count: 0
      task_count: 3
    status_code: 201
- name: stage
  request:
    method: GET
    url: '{django_live_url}/api/projects/{project_pk}/summary'
  response:
    json:
      all_data_columns:
        key1: 5
        key2: 5
        value1: 5
        value100: 3
        value2: 2
      common_data_columns:
      - key1
      - key2
      - value1
    status_code: 200

---
test_name: import_time_series_with_predictions
strict: false
marks:
- usefixtures:
  - django_live_url
stages:
- id: signup
  type: ref
- name: stage
  request:
    data:
      label_config: <View></View>
      title: Check consistency of imported data columns
    method: POST
    url: '{django_live_url}/api/projects'
  response:
    save:
      json:
        project_pk: id
    status_code: 201
- name: stage
  request:
    files:
      csv_0: tests/test_suites/samples/random-with-predictions.json
    headers:
      content-type: multipart/form-data
    method: POST
    url: '{django_live_url}/api/projects/{project_pk}/tasks/bulk'
  response:
    json:
      annotation_count: 0
      prediction_count: 1
      task_count: 1
    status_code: 201

---
test_name: import_from_url
strict: false
marks:
- usefixtures:
  - import_from_url
  - django_live_url
stages:
- id: signup
  type: ref
- name: stage
  request:
    data:
      label_config: <View></View>
      title: Check consistency of imported data columns
    method: POST
    url: '{django_live_url}/api/projects'
  response:
    save:
      json:
        project_pk: id
    status_code: 201
- name: stage
  request:
    data:
      url: https://data.heartex.net/q/fffeaf81f4.json
    headers:
      content-type: application/x-www-form-urlencoded
    method: POST
    url: '{django_live_url}/api/projects/{project_pk}/tasks/bulk'
  response:
    status_code: 201
- name: stage
  request:
    data:
      url: http://data.heartextest.net/test_1.csv
    headers:
      content-type: application/x-www-form-urlencoded
    method: POST
    url: '{django_live_url}/api/projects/{project_pk}/import?commit_to_project=false'
  response:
    json:
      could_be_tasks_list: True
    status_code: 201

---
test_name: full_step_project_creation
strict: false
marks:
- usefixtures:
  - django_live_url
stages:
- id: signup
  type: ref
- name: stage
  request:
    data:
      is_draft: true
    method: POST
    url: '{django_live_url}/api/projects'
  response:
    save:
      json:
        project_pk: id
    status_code: 201
- name: stage
  request:
    files:
      image_0: tests/test_suites/samples/first.jpg
      image_1: tests/test_suites/samples/second.jpg
    headers:
      content-type: multipart/form-data
    method: POST
    url: '{django_live_url}/api/projects/{project_pk}/tasks/bulk'
  response:
    json:
      annotation_count: 0
      prediction_count: 0
      task_count: 2
    status_code: 201
- name: stage
  request:
    method: GET
    url: '{django_live_url}/api/projects/{project_pk}/file-uploads'
  response:
    status_code: 200
- name: check_list_of_file_uploads
  request:
    method: GET
    url: '{django_live_url}/api/projects/{project_pk}/file-uploads'
    params:
      all: true
  response:
    status_code: 200
    json:
      - id: !anyint
      - id: !anyint

- name: stage
  request:
    method: GET
    url: '{django_live_url}/api/projects/{project_pk}/summary'
  response:
    json:
      all_data_columns:
        $undefined$: 2
      common_data_columns:
      - $undefined$
    status_code: 200
- name: stage
  request:
    headers:
      content-type: application/json
    json:
      is_draft: false
    method: PATCH
    url: '{django_live_url}/api/projects/{project_pk}'
  response:
    json:
      is_draft: false
    status_code: 200
- name: stage
  request:
    method: GET
    url: '{django_live_url}/api/projects'
  response:
    status_code: 200
- name: stage
  request:
    method: GET
    url: '{django_live_url}/api/projects/{project_pk}'
  response:
    json:
      created_by:
        avatar: null
        email: <EMAIL>
        first_name: ''
        last_name: ''
      task_number: 2
      title: ''
      total_predictions_number: 0
    status_code: 200
- name: stage
  request:
    method: DELETE
    url: '{django_live_url}/api/projects/{project_pk}'
  response:
    status_code: 204

---
test_name: interpert_as_time_series
strict: false
marks:
- usefixtures:
  - django_live_url
stages:
- id: signup
  type: ref
- name: stage
  request:
    data:
      label_config: <View></View>
      title: Interpert as time series
    method: POST
    url: '{django_live_url}/api/projects'
  response:
    save:
      json:
        project_pk: id
    status_code: 201
- name: stage
  request:
    files:
      csv_1: tests/test_suites/samples/test_3.csv
      csv_2: tests/test_suites/samples/test_4.csv
    headers:
      content-type: multipart/form-data
    method: POST
    url: '{django_live_url}/api/projects/{project_pk}/import?commit_to_project=false'
  response:
    json:
      annotation_count: null
      could_be_tasks_list: true
      prediction_count: null
      task_count: 5
    save:
      json:
        file_upload_ids: file_upload_ids
    status_code: 201
- name: stage
  request:
    headers:
      content-type: application/json
    json:
      file_upload_ids: !force_format_include '{file_upload_ids}'
      files_as_tasks_list: false
    method: POST
    url: '{django_live_url}/api/projects/{project_pk}/reimport'
  response:
    json:
      task_count: 2
    status_code: 201
- name: stage
  request:
    method: GET
    url: '{django_live_url}/api/projects/{project_pk}/tasks'
  response:
    status_code: 200
- name: stage
  request:
    headers:
      content-type: application/json
    json:
      file_upload_ids: !force_format_include '{file_upload_ids}'
    method: DELETE
    url: '{django_live_url}/api/projects/{project_pk}/file-uploads'
  response:
    json:
      deleted: 2
    status_code: 200

---
test_name: import_previous_export
strict: false
marks:
- usefixtures:
  - django_live_url
stages:
- id: signup
  type: ref
- name: stage
  request:
    data:
      label_config: <View><Image name="image" value="$image"/><Choices name="label" toName="image"><Choice
        value="Cat"/><Choice value="Dog"/></Choices></View>
      title: Image Classification Project
    method: POST
    url: '{django_live_url}/api/projects'
  response:
    save:
      json:
        project_pk: id
    status_code: 201
- name: stage
  request:
    files:
      json: tests/test_suites/samples/previous_export.json
    headers:
      content-type: multipart/form-data
    method: POST
    url: '{django_live_url}/api/projects/{project_pk}/import'
  response:
    status_code: 400
    json:
      validation_errors:
        non_field_errors:
          - "Unknown annotator'<NAME_EMAIL>"
# Now let's create missed user and try again...
- id: logout
  type: ref
- id: signup
  name: Sign up
  request:
    url: "{django_live_url}/user/signup"
    data:
      email: <EMAIL>
      password: 12345678
    method: POST
  response:
    status_code: 302
- id: login
  type: ref
- name: stage
  request:
    files:
      json: tests/test_suites/samples/previous_export.json
    headers:
      content-type: multipart/form-data
    method: POST
    url: '{django_live_url}/api/projects/{project_pk}/import'
  response:
    status_code: 201
    json:
      task_count: 1
      annotation_count: 1
      prediction_count: 0
      could_be_tasks_list: false
      found_formats: {'.json': 1}
      data_columns: ['image']

- name: stage
  request:
    files:
      json: tests/test_suites/samples/previous_export_unknown_completed_by.json
    headers:
      content-type: multipart/form-data
    method: POST
    url: '{django_live_url}/api/projects/{project_pk}/import'
  response:
    status_code: 400
    json:
      validation_errors:
        non_field_errors:
          - "Import data contains completed_by=99999 which is not a valid annotator's email or ID"


---
test_name: import_minified_preannotations_classification
strict: false
marks:
- usefixtures:
  - django_live_url
stages:
- id: signup
  type: ref
- name: Create project
  request:
    data:
      label_config: <View><Image name="image" value="$image"/><Choices name="label" toName="image"><Choice
        value="Cat"/><Choice value="Dog"/></Choices></View>
      title: Image Classification Project
    method: POST
    url: '{django_live_url}/api/projects'
  response:
    save:
      json:
        project_pk: id
    status_code: 201
- name: Import images with preannotations
  request:
    files:
      csv: tests/test_suites/samples/image_urls_with_classes.csv
    headers:
      content-type: multipart/form-data
    method: POST
    url: '{django_live_url}/api/projects/{project_pk}/import?return_task_ids=true&preannotated_from_fields=label'
  response:
    status_code: 201
    save:
      json:
        first_task: "task_ids[0]"
        second_task: "task_ids[1]"
- name: Check 1st task with prediction
  request:
    method: GET
    url: '{django_live_url}/api/tasks/{first_task}'
  response:
    status_code: 200
    json:
      id: !int '{first_task}'
      annotations: []
      drafts: []
      predictions:
        - result:
            - from_name: label
              to_name: image
              type: choices
              value:
                choices:
                  - Cat
- name: Check 2nd task with prediction
  request:
    method: GET
    url: '{django_live_url}/api/tasks/{second_task}'
  response:
    status_code: 200
    json:
      id: !int '{second_task}'
      annotations: []
      drafts: []
      predictions:
        - result:
            - from_name: label
              to_name: image
              type: choices
              value:
                choices:
                  - Dog

---
test_name: import_minified_preannotations_object_detection
strict: false
marks:
- usefixtures:
  - django_live_url
stages:
- id: signup
  type: ref
- name: Create project
  request:
    data:
      label_config: <View><Image name="image" value="$image"/><RectangleLabels name="objects" toName="image" choices="multiple"><Label
        value="Cat"/><Label value="Dog"/></RectangleLabels></View>
      title: Image Classification Project
    method: POST
    url: '{django_live_url}/api/projects'
  response:
    save:
      json:
        project_pk: id
    status_code: 201
- name: Import images with preannotations
  request:
    files:
      csv: tests/test_suites/samples/image_urls_with_bboxes.json
    headers:
      content-type: multipart/form-data
    method: POST
    url: '{django_live_url}/api/projects/{project_pk}/import?return_task_ids=true&preannotated_from_fields=bbox'
  response:
    status_code: 201
    save:
      json:
        first_task: "task_ids[0]"
        second_task: "task_ids[1]"
- name: Check 1st task with prediction
  request:
    method: GET
    url: '{django_live_url}/api/tasks/{first_task}'
  response:
    status_code: 200
    json:
      id: !int '{first_task}'
      annotations: []
      drafts: []
      predictions:
        - result:
            - from_name: objects
              to_name: image
              type: rectanglelabels
              value:
                x: 10
                y: 20
                width: 30
                height: 40
                rectanglelabels:
                  - Cat
                  - Dog


---
test_name: import_bulk_predictions
strict: false
marks:
- usefixtures:
  - django_live_url
stages:
- id: signup
  type: ref
- name: Create project
  request:
    data:
      label_config: <View><Image name="image" value="$image"/><Choices name="label" toName="image"><Choice
        value="AAA"/><Choice value="BBB"/></Choices></View>
      title: Image Classification Project
    method: POST
    url: '{django_live_url}/api/projects'
  response:
    save:
      json:
        project_pk: id
    status_code: 201
- name: Import images
  request:
    files:
      csv: tests/test_suites/samples/image_urls.csv
    headers:
      content-type: multipart/form-data
    method: POST
    url: '{django_live_url}/api/projects/{project_pk}/import?return_task_ids=true'
  response:
    status_code: 201
    save:
      json:
        first_task: "task_ids[0]"
        second_task: "task_ids[1]"
        third_task: "task_ids[2]"
- name: Import bulk predictions
  request:
    method: POST
    url: '{django_live_url}/api/projects/{project_pk}/import/predictions'
    json:
      - task: !int '{first_task}'
        result: AAA
        score: 0.123
      - task: !int '{third_task}'
        result: BBB
        model_version: abcdefgh
  response:
    status_code: 201
    json:
      created: 2
- name: Check 1st task with prediction
  request:
    method: GET
    url: '{django_live_url}/api/tasks/{first_task}'
  response:
    status_code: 200
    json:
      id: !int '{first_task}'
      annotations: []
      drafts: []
      predictions:
        - result:
            - from_name: label
              to_name: image
              type: choices
              value:
                choices:
                  - AAA
          score: 0.123
          model_version: undefined
- name: Check 2nd task with prediction
  request:
    method: GET
    url: '{django_live_url}/api/tasks/{second_task}'
  response:
    status_code: 200
    json:
      id: !int '{second_task}'
      annotations: []
      drafts: []
      predictions: []
- name: Check 3rd task with prediction
  request:
    method: GET
    url: '{django_live_url}/api/tasks/{third_task}'
  response:
    status_code: 200
    json:
      id: !int '{third_task}'
      annotations: []
      drafts: []
      predictions:
        - result:
            - from_name: label
              to_name: image
              type: choices
              value:
                choices:
                  - BBB
          score: null
          model_version: abcdefgh

---
test_name: import_groud_truth_preannotations
strict: false
marks:
- usefixtures:
  - django_live_url
stages:
- id: signup
  type: ref
- name: Create project
  request:
    data:
      label_config: <View><Image name="image" value="$image"/><RectangleLabels name="objects" toName="image" choices="multiple"><Label
        value="Cat"/><Label value="Dog"/></RectangleLabels></View>
      title: Image Classification Project
    method: POST
    url: '{django_live_url}/api/projects'
  response:
    save:
      json:
        project_pk: id
    status_code: 201
- name: Import images with preannotations
  request:
    files:
      csv: tests/test_suites/samples/image_urls_with_bboxes_gt.json
    headers:
      content-type: multipart/form-data
    method: POST
    url: '{django_live_url}/api/projects/{project_pk}/import?return_task_ids=true'
  response:
    status_code: 201
    save:
      json:
        first_task: "task_ids[0]"
        second_task: "task_ids[1]"
- name: Check 1st task with prediction
  request:
    method: GET
    url: '{django_live_url}/api/tasks/{first_task}'
  response:
    status_code: 200
    json:
      id: !int '{first_task}'
      is_labeled: true
---
test_name: extension_check
strict: false
marks:
- usefixtures:
  - django_live_url
stages:
- id: signup
  type: ref
- name: stage
  request:
    data:
      label_config: <View></View>
      title: Check consistency of imported data columns
    method: POST
    url: '{django_live_url}/api/projects'
  response:
    save:
      json:
        project_pk: id
    status_code: 201
- name: stage
  request:
    files:
      file: tests/test_suites/samples/test.wrong_ext
    headers:
      content-type: multipart/form-data
    method: POST
    url: '{django_live_url}/api/projects/{project_pk}/import?commit_to_project=false'
  response:
    json:
      validation_errors:
        non_field_errors:
          - '.wrong_ext extension is not supported'

    status_code: 400
---
test_name: extension_check_empty
strict: false
marks:
- usefixtures:
  - django_live_url
stages:
- id: signup
  type: ref
- name: stage
  request:
    data:
      label_config: <View></View>
      title: Check consistency of imported data columns
    method: POST
    url: '{django_live_url}/api/projects'
  response:
    save:
      json:
        project_pk: id
    status_code: 201
- name: stage
  request:
    files:
      file: tests/test_suites/samples/test_file_without_extension
    headers:
      content-type: multipart/form-data
    method: POST
    url: '{django_live_url}/api/projects/{project_pk}/import?commit_to_project=false'
  response:
    json:
      validation_errors:
        non_field_errors:
          - ' extension is not supported'

    status_code: 400

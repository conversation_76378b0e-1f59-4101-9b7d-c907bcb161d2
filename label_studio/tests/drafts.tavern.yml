---
test_name: no_annotation_no_drafts
strict: false
marks:
- usefixtures:
  - django_live_url
stages:
- id: signup
  type: ref
- name: stage
  request:
    data:
      label_config: <View><Text name="text" value="$text"/><Choices name="label" toName="text"><Choice
        value="pos"/><Choice value="neg"/></Choices></View>
      title: Test Draft 1
    method: POST
    url: '{django_live_url}/api/projects'
  response:
    save:
      json:
        project_pk: id
    status_code: 201
- name: stage
  request:
    headers:
      content-type: application/json
    json:
      data:
        text: Test example phrase
    method: POST
    url: '{django_live_url}/api/projects/{project_pk}/tasks'
  response:
    save:
      json:
        task_pk: id
    status_code: 201
- name: stage
  request:
    headers:
      content-type: application/json
    json:
      lead_time: 12.34
      result:
      - from_name: text_class
        to_name: text
        type: choices
        value:
          choices:
          - class_A
    method: POST
    url: '{django_live_url}/api/tasks/{task_pk}/drafts'
  response:
    save:
      json:
        draft_id: id
    status_code: 201

- name: stage
  request:
    headers:
      content-type: application/json
    method: GET
    url: '{django_live_url}/api/tasks/{task_pk}/drafts'
  response:
    status_code: 200  

- name: stage
  request:
    method: GET
    url: '{django_live_url}/api/drafts/{draft_id}/'
  response:
    json:
      annotation: null
      created_ago: !re_match 0.+minutes
      lead_time: 12.34
      result:
      - from_name: text_class
        to_name: text
        type: choices
        value:
          choices:
          - class_A
      user: <EMAIL>
    save:
      json:
        task_pk: task
    status_code: 200
- name: stage
  request:
    headers:
      content-type: application/json
    json:
      draft_id: '{draft_id}'
      lead_time: 91.01
      result:
      - from_name: text_class
        to_name: text
        type: choices
        value:
          choices:
          - class_A
    method: POST
    url: '{django_live_url}/api/tasks/{task_pk}/annotations'
  response:
    status_code: 201
- name: stage
  request:
    method: GET
    url: '{django_live_url}/api/drafts/{draft_id}/'
  response:
    status_code: 404

---
test_name: No annotation, draft exists
strict: false
marks:
- usefixtures:
  - django_live_url
stages:
- id: signup
  type: ref
- name: stage
  request:
    data:
      label_config: <View><Text name="text" value="$text"/><Choices name="label" toName="text"><Choice
        value="pos"/><Choice value="neg"/></Choices></View>
      title: Test Draft 2
    method: POST
    url: '{django_live_url}/api/projects'
  response:
    save:
      json:
        project_pk: id
    status_code: 201
- name: stage
  request:
    headers:
      content-type: application/json
    json:
      data:
        text: Test example phrase
    method: POST
    url: '{django_live_url}/api/projects/{project_pk}/tasks'
  response:
    save:
      json:
        task_pk: id
    status_code: 201
- name: stage
  request:
    headers:
      content-type: application/json
    json:
      lead_time: 12.34
      result:
      - from_name: text_class
        to_name: text
        type: choices
        value:
          choices:
          - class_A
    method: POST
    url: '{django_live_url}/api/tasks/{task_pk}/drafts'
  response:
    save:
      json:
        draft_id: id
    status_code: 201
- name: stage
  request:
    headers:
      content-type: application/json
    json:
      lead_time: 12.34
      result:
      - from_name: text_class
        to_name: text
        type: choices
        value:
          choices:
          - class_B
    method: PATCH
    url: '{django_live_url}/api/drafts/{draft_id}'
  response:
    status_code: 200
- name: stage
  request:
    method: GET
    url: '{django_live_url}/api/drafts/{draft_id}/'
  response:
    json:
      annotation: null
      created_ago: !re_match 0.+minutes
      lead_time: 12.34
      result:
      - from_name: text_class
        to_name: text
        type: choices
        value:
          choices:
          - class_B
      user: <EMAIL>
    save:
      json:
        task_pk: task
    status_code: 200

---
test_name: Completion exists, no drafts
strict: false
marks:
- usefixtures:
  - django_live_url
stages:
- id: signup
  type: ref
- name: stage
  request:
    data:
      label_config: <View><Text name="text" value="$text"/><Choices name="label" toName="text"><Choice
        value="pos"/><Choice value="neg"/></Choices></View>
      title: Test Draft 3
    method: POST
    url: '{django_live_url}/api/projects'
  response:
    save:
      json:
        project_pk: id
    status_code: 201
- name: stage
  request:
    headers:
      content-type: application/json
    json:
      data:
        text: Test example phrase
    method: POST
    url: '{django_live_url}/api/projects/{project_pk}/tasks'
  response:
    save:
      json:
        task_pk: id
    status_code: 201
- name: stage
  request:
    headers:
      content-type: application/json
    json:
      lead_time: 34.56
      result:
      - from_name: text_class
        to_name: text
        type: choices
        value:
          choices:
          - class_B
    method: POST
    url: '{django_live_url}/api/tasks/{task_pk}/annotations'
  response:
    save:
      json:
        annotation_pk: id
    status_code: 201
- name: stage
  request:
    headers:
      content-type: application/json
    json:
      lead_time: 56.78
      result:
      - from_name: text_class
        to_name: text
        type: choices
        value:
          choices:
          - class_A
    method: POST
    url: '{django_live_url}/api/tasks/{task_pk}/annotations/{annotation_pk}/drafts'
  response:
    save:
      json:
        draft_id: id
    status_code: 201
- name: stage
  request:
    method: GET
    url: '{django_live_url}/api/drafts/{draft_id}'
  response:
    json:
      created_ago: !re_match 0.+minutes
      lead_time: 56.78
      result:
      - from_name: text_class
        to_name: text
        type: choices
        value:
          choices:
          - class_A
      user: <EMAIL>
    save:
      json:
        annotation_pk: annotation
        task_pk: task
    status_code: 200

---
test_name: Completion exists, draft exists
strict: false
marks:
- usefixtures:
  - django_live_url
stages:
- id: signup
  type: ref
- name: stage
  request:
    data:
      label_config: <View><Text name="text" value="$text"/><Choices name="label" toName="text"><Choice
        value="pos"/><Choice value="neg"/></Choices></View>
      title: Test Draft 4
    method: POST
    url: '{django_live_url}/api/projects'
  response:
    save:
      json:
        project_pk: id
    status_code: 201
- name: stage
  request:
    headers:
      content-type: application/json
    json:
      data:
        text: Test example phrase
    method: POST
    url: '{django_live_url}/api/projects/{project_pk}/tasks'
  response:
    save:
      json:
        task_pk: id
    status_code: 201
- name: stage
  request:
    headers:
      content-type: application/json
    json:
      lead_time: 34.56
      result:
      - from_name: text_class
        to_name: text
        type: choices
        value:
          choices:
          - class_B
    method: POST
    url: '{django_live_url}/api/tasks/{task_pk}/annotations'
  response:
    save:
      json:
        annotation_pk: id
    status_code: 201
- name: stage
  request:
    headers:
      content-type: application/json
    json:
      lead_time: 56.78
      result:
      - from_name: text_class
        to_name: text
        type: choices
        value:
          choices:
          - class_A
    method: POST
    url: '{django_live_url}/api/tasks/{task_pk}/annotations/{annotation_pk}/drafts'
  response:
    save:
      json:
        draft_id: id
    status_code: 201
- name: stage
  request:
    headers:
      content-type: application/json
    json:
      lead_time: 99.99
      result:
      - from_name: text_class
        to_name: text
        type: choices
        value:
          choices:
          - class_B
    method: PATCH
    url: '{django_live_url}/api/drafts/{draft_id}'
  response:
    status_code: 200
- name: stage
  request:
    method: GET
    url: '{django_live_url}/api/drafts/{draft_id}'
  response:
    json:
      created_ago: !re_match 0.+minutes
      lead_time: 99.99
      result:
      - from_name: text_class
        to_name: text
        type: choices
        value:
          choices:
          - class_B
      user: <EMAIL>
    save:
      json:
        annotation_pk: annotation
        task_pk: task
    status_code: 200

---
test_name: remove_other_users_draft_on_annotation_create
strict: false
marks:
- usefixtures:
  - django_live_url
stages:
- id: signup
  type: ref
- id: create_project
  type: ref
- id: import_task
  type: ref
- id: get_user_token
  type: ref
- id: create_annotation
  type: ref

# Test this functionality with legacy tokens, need to enable them for the org
- name: enable_legacy_api_tokens
  request:
    url: "{django_live_url}/api/jwt/settings"
    method: POST
    headers:
      content-type: application/json
    json:
      legacy_api_tokens_enabled: true
  response:
    status_code: 200

- id: get_invite_url
  type: ref
- id: logout
  type: ref

# create second user
- name: signup_other_user
  request:
    url: "{django_live_url}{invite_url}"
    data:
      email: <EMAIL>
      password: 12345678
    method: POST
  response:
    status_code: 302
- name: create_draft
  request:
    headers:
      content-type: application/json
    json:
      lead_time: 12.34
      result:
      - from_name: text_class
        to_name: text
        type: choices
        value:
          choices:
          - class_A
    method: POST
    url: '{django_live_url}/api/tasks/{task_pk}/annotations/{annotation_pk}/drafts'
    # url: '{django_live_url}/api/tasks/{task_pk}/drafts'
  response:
    save:
      json:
        draft_pk: id
    status_code: 201
- name: get_drafts
  request:
    method: GET
    url: '{django_live_url}/api/tasks/{task_pk}/drafts'
  response:
    status_code: 200
    json:
      - id: !int '{draft_pk}'
- name: stage
  request:
    headers:
      content-type: application/json
    json:
      result:
      - from_name: label
        to_name: text
        type: choices
        value:
          choices:
          - 1
    method: PATCH
    url: '{django_live_url}/api/annotations/{annotation_pk}'
  response:
    status_code: 200

- name: get_drafts
  request:
    headers:
      authorization: "Token {user_token}"
    method: GET
    url: '{django_live_url}/api/tasks/{task_pk}/drafts'
  response:
    verify_response_with:
      function: tests.utils:empty_list
    status_code: 200

---
test_name: draft_permission
strict: false
marks:
- usefixtures:
  - django_live_url
stages:
- id: signup
  type: ref
- name: stage
  request:
    data:
      label_config: <View><Text name="text" value="$text"/><Choices name="label" toName="text"><Choice
        value="pos"/><Choice value="neg"/></Choices></View>
      title: Test Draft 1
    method: POST
    url: '{django_live_url}/api/projects'
  response:
    save:
      json:
        project_pk: id
    status_code: 201
- name: stage
  request:
    headers:
      content-type: application/json
    json:
      data:
        text: Test example phrase1
    method: POST
    url: '{django_live_url}/api/projects/{project_pk}/tasks'
  response:
    save:
      json:
        task_pk: id
    status_code: 201
- name: stage
  request:
    headers:
      content-type: application/json
    json:
      data:
        text: Test example phrase2
    method: POST
    url: '{django_live_url}/api/projects/{project_pk}/tasks'
  response:
    save:
      json:
        task2_pk: id
    status_code: 201
- name: stage
  request:
    headers:
      content-type: application/json
    json:
      lead_time: 12.34
      result:
      - from_name: text_class
        to_name: text
        type: choices
        value:
          choices:
          - class_A
    method: POST
    url: '{django_live_url}/api/tasks/{task2_pk}/drafts'
  response:
    save:
      json:
        draft_id: id
    status_code: 201

- name: stage
  request:
    headers:
      content-type: application/json
    method: GET
    url: '{django_live_url}/api/tasks/{task2_pk}/drafts'
  response:
    status_code: 200  

- name: stage
  request:
    method: GET
    url: '{django_live_url}/api/drafts/{draft_id}/'
  response:
    json:
      annotation: null
      created_ago: !re_match 0.+minutes
      lead_time: 12.34
      result:
      - from_name: text_class
        to_name: text
        type: choices
        value:
          choices:
          - class_A
      user: <EMAIL>
    status_code: 200
- name: stage
  request:
    headers:
      content-type: application/json
    json:
      draft_id: '{draft_id}'
      lead_time: 91.01
      result:
      - from_name: text_class
        to_name: text
        type: choices
        value:
          choices:
          - class_A
    method: POST
    url: '{django_live_url}/api/tasks/{task_pk}/annotations'
  response:
    status_code: 403
- name: stage
  request:
    method: GET
    url: '{django_live_url}/api/drafts/{draft_id}/'
  response:
    status_code: 200

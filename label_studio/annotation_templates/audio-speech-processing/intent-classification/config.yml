title: Intent Classification
type: community
group: Audio/Speech Processing
image: /static/templates/intent-classification.png
details: <h1>Do voice activity segmentation and choose spoken intent</h1>
config: |
  <View>
    <Labels name="labels" toName="audio">
      <Label value="Segment" />
    </Labels>

    <Audio name="audio" value="$audio"/>

    <Choices name="intent" toName="audio" perRegion="true" required="true">
      <Choice value="Question" />
      <Choice value="Request" />
      <Choice value="Satisfied" />
      <Choice value="Interested" />
      <Choice value="Unsatisfied" />
    </Choices>
  </View>

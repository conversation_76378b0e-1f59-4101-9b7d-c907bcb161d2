title: Text-to-Image Generation
type: community
group: Ranking & Scoring
image: /static/templates/text-to-image.png
details: <h1>Choose the most related image generated by textual description</h1>
config: |
  <View>
    <View className="ch-title">
      <Text name="prompt" value="$prompt"/>
    </View>
    <View className="highlight">
      <Choices name="images" toName="prompt" value="$images" choice="multiple" layout="inline"/>
    </View>
    <Style>
    .ch-title {
      font-size: 2em;
      box-shadow: rgba(99, 99, 99, 0.2) 0px 2px 8px 0px;
      text-align: center;
    }
    .ch-title .lsf-richtext__line:before {
      display: none;
    }
    .highlight .ant-checkbox {
      position: absolute;
      opacity: 0;
    }
    .ant-checkbox+span {
      border: 4px solid transparent;
      border-radius: 8px;
      padding: 8px;
    }
    .ant-checkbox-checked+span {
      border-color: red;
    }
    .ant-checkbox+span sup {
      position: absolute;
      right: 8px;
      top: 8px;
      z-index: 2;
      display: block;
      background: white;
      width: 24px;
      line-height: 16px;
      text-align: center;
    }
    </Style>
  </View>
  <!-- {"data": {
    "prompt": "An astronaut riding a horse in a photorealistic style",
    "images": [{
      "value": "id123#0",
  	"style": "margin: 5px",
      "html": "<img width='400' src='https://cdn.openai.com/dall-e-2/demos/text2im/astronaut/horse/photo/0.jpg'/>"
    },
  {
      "value": "id123#1",
  	"style": "margin: 5px",
      "html": "<img width='400' src='https://cdn.openai.com/dall-e-2/demos/text2im/astronaut/horse/photo/1.jpg'/>"
    }, {
      "value": "id123#2",
  	"style": "margin: 5px",
      "html": "<img width='400' src='https://cdn.openai.com/dall-e-2/demos/text2im/astronaut/horse/photo/2.jpg'/>"
    }
    ]
  }} -->

title: Document Retrieval
type: community
group: Ranking & Scoring
image: /static/templates/document-retrieval.png
details: <h1>Select document related to the query</h1>
config: |
  <View>
    <Text name="query" value="$query" />
    <Header value="Select document related to the query:" />
    <View style="display: flex">
      <View style="width:50%">
        <Text name="text1" value="$text1" />
        <Text name="text2" value="$text2" />
        <Text name="text3" value="$text3" />
      </View>
      <View>
        <Choices name="selection" toName="query" required="true" choice="multiple">
          <Choice value="One" />
          <Choice value="Two" />
          <Choice value="Two" />
        </Choices>
      </View>
    </View>
  </View>

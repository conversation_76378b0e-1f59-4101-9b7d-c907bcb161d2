<View>

    <!-- Object tag for time series data source -->
    <TimeSeries name="ts" valueType="url" value="$csv"
                sep=","
                timeColumn="time"
                timeFormat="%Y-%m-%d %H:%M:%S.%f"
                timeDisplayFormat="%Y-%m-%d"
                overviewChannels="velocity">

        <Channel column="velocity"
                 units="miles/h"
                 displayFormat=",.1f"
                 strokeColor="#1f77b4"
                 legend="Velocity"/>
    </TimeSeries>
    <!-- Control tag for region labels -->
    <TimeSeriesLabels name="label" toName="ts">
        <Label value="Region" background="red" />
    </TimeSeriesLabels>
  <Choices name="region_type" toName="ts"
        perRegion="true" required="true">
      <Choice value="Outlier"/>
      <Choice value="Anomaly"/>
  </Choices>
</View>

<!-- { "data": { "timeseriesUrl": "/samples/time-series.csv?time=time&values=velocity%2Cacceleration&sep=%2C&tf=%25Y-%25m-%25d+%25H%3A%25M%3A%25S.%25f" },
 "annotations": [{
 "result": [
    {
        "value": {
            "start": "2020-01-14 00:00:00.000000",
            "end": "2020-01-14 00:00:00.000000",
            "instant": true,
            "timeserieslabels": [
                "Region"
            ]
        },
        "id": "wxAAv5XVR2",
        "from_name": "label",
        "to_name": "ts",
        "type": "timeserieslabels"
    },
    {
        "value": {
            "start": "2020-01-14 00:00:00.000000",
            "end": "2020-01-14 00:00:00.000000",
            "instant": true,
            "choices": [
                "Outlier"
            ]
        },
        "id": "wxAAv5XVR2",
        "from_name": "region_type",
        "to_name": "ts",
        "type": "choices"
    },
    {
        "value": {
            "start": "2020-02-01 00:00:00.000000",
            "end": "2020-02-01 00:00:00.000000",
            "instant": true,
            "timeserieslabels": [
                "Region"
            ]
        },
        "id": "IdRc3nTc5Y",
        "from_name": "label",
        "to_name": "ts",
        "type": "timeserieslabels"
    },
    {
        "value": {
            "start": "2020-02-01 00:00:00.000000",
            "end": "2020-02-01 00:00:00.000000",
            "instant": true,
            "choices": [
                "Outlier"
            ]
        },
        "id": "IdRc3nTc5Y",
        "from_name": "region_type",
        "to_name": "ts",
        "type": "choices"
    },
    {
        "value": {
            "start": "2020-03-11 00:00:00.000000",
            "end": "2020-03-22 00:00:00.000000",
            "instant": false,
            "timeserieslabels": [
                "Region"
            ]
        },
        "id": "kZk0r1j-wk",
        "from_name": "label",
        "to_name": "ts",
        "type": "timeserieslabels"
    },
    {
        "value": {
            "start": "2020-03-11 00:00:00.000000",
            "end": "2020-03-22 00:00:00.000000",
            "instant": false,
            "choices": [
                "Anomaly"
            ]
        },
        "id": "kZk0r1j-wk",
        "from_name": "region_type",
        "to_name": "ts",
        "type": "choices"
    }
]
 }]
 } -->

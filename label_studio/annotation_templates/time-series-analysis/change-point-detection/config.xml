<View>
    <!-- Control tag for region labels -->
    <TimeSeriesLabels name="label" toName="ts">
        <Label value="Change" background="red" />
    </TimeSeriesLabels>

    <!-- Object tag for time series data source -->
    <TimeSeries name="ts" valueType="url" value="$csv"
                sep=","
                timeColumn="time"
                timeFormat="%Y-%m-%d %H:%M:%S.%f"
                timeDisplayFormat="%Y-%m-%d"
                overviewChannels="velocity">

        <Channel column="velocity"
                 units="miles/h"
                 displayFormat=",.1f"
                 strokeColor="#1f77b4"
                 legend="Velocity"/>       
    </TimeSeries>
</View>

<!-- { "data": { "timeseriesUrl": "/samples/time-series.csv?time=time&values=velocity%2Cacceleration&sep=%2C&tf=%25Y-%25m-%25d+%25H%3A%25M%3A%25S.%25f" },
 "annotations": [{"result":
 [
    {
        "value": {
            "start": "2020-01-12 00:00:00.000000",
            "end": "2020-01-12 00:00:00.000000",
            "instant": true,
            "timeserieslabels": [
                "Change"
            ]
        },
        "id": "vspOsiWrt-",
        "from_name": "label",
        "to_name": "ts",
        "type": "timeserieslabels"
    },
    {
        "value": {
            "start": "2020-01-05 00:00:00.000000",
            "end": "2020-01-05 00:00:00.000000",
            "instant": true,
            "timeserieslabels": [
                "Change"
            ]
        },
        "id": "aGtIVfbfmo",
        "from_name": "label",
        "to_name": "ts",
        "type": "timeserieslabels"
    },
    {
        "value": {
            "start": "2020-01-20 00:00:00.000000",
            "end": "2020-01-20 00:00:00.000000",
            "instant": true,
            "timeserieslabels": [
                "Change"
            ]
        },
        "id": "Q7wqxJwmSD",
        "from_name": "label",
        "to_name": "ts",
        "type": "timeserieslabels"
    }
]
 }]

 } -->

title: Relation Extraction
type: community
group: Natural Language Processing
order: 5
image: /static/templates/relation-extraction.png
details: <h1>Predict attributes and relations for entities in a sentence</h1>
config: |
  <View>
     <Relations>
      <Relation value="org:founded_by"/>
      <Relation value="org:founded"/>
    </Relations>
    <Labels name="label" toName="text">
      <Label value="Organization" background="orange"/>
      <Label value="Person" background="green"/>
      <Label value="Datetime" background="blue"/>
    </Labels>

    <Text name="text" value="$text"/>
  </View>

  <!-- {"data": {
    "text": "Microsoft was founded by <PERSON> and <PERSON> on April 4, 1975, to develop and sell BASIC interpreters for the Altair 8800."
  }} -->


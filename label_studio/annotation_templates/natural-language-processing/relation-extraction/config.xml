<View>
   <Relations>
    <Relation value="org:founded_by"/>
    <Relation value="org:founded"/>
  </Relations>
  <Labels name="label" toName="text">
    <Label value="Organization" background="orange"/>
    <Label value="Person" background="green"/>
    <Label value="Datetime" background="blue"/>
  </Labels>

  <Text name="text" value="$text"/>
</View>

<!-- {"data": {
"text": "Microsoft was founded by <PERSON> and <PERSON> on April 4, 1975, to develop and sell BASIC interpreters for the Altair 8800."
},
"annotations": [{"result":
[
    {
        "value": {
            "start": 0,
            "end": 9,
            "text": "Microsoft",
            "labels": [
                "Organization"
            ]
        },
        "id": "a-ubmF-dP2",
        "from_name": "label",
        "to_name": "text",
        "type": "labels"
    },
    {
        "value": {
            "start": 25,
            "end": 35,
            "text": "<PERSON> Gates",
            "labels": [
                "Person"
            ]
        },
        "id": "dQ_pmsrWsm",
        "from_name": "label",
        "to_name": "text",
        "type": "labels"
    },
    {
        "value": {
            "start": 40,
            "end": 50,
            "text": "<PERSON>",
            "labels": [
                "Person"
            ]
        },
        "id": "IpmpOMj0vw",
        "from_name": "label",
        "to_name": "text",
        "type": "labels"
    },
    {
        "value": {
            "start": 54,
            "end": 67,
            "text": "April 4, 1975",
            "labels": [
                "Datetime"
            ]
        },
        "id": "Q-zXcdacte",
        "from_name": "label",
        "to_name": "text",
        "type": "labels"
    },
    {
        "from_id": "a-ubmF-dP2",
        "to_id": "dQ_pmsrWsm",
        "type": "relation",
        "direction": "right",
        "labels": [
            "org:founded_by"
        ]
    },
    {
        "from_id": "a-ubmF-dP2",
        "to_id": "IpmpOMj0vw",
        "type": "relation",
        "direction": "right",
        "labels": []
    },
    {
        "from_id": "a-ubmF-dP2",
        "to_id": "Q-zXcdacte",
        "type": "relation",
        "direction": "right",
        "labels": [
            "org:founded"
        ]
    }
]
}]

}
-->

title: Video Object Tracking
type: community
group: Videos
image: /static/templates/video-object-tracking.png
details: <h1>Object tracking in video with bounding boxes</h1>
config: |
  <View>
     <Labels name="videoLabels" toName="video" allowEmpty="true">
       <Label value="Man" background="blue"/>
       <Label value="Woman" background="red"/>
       <Label value="Other" background="green"/>
     </Labels>
     
     <!-- Please specify FPS carefully, it will be used for all project videos -->
     <Video name="video" value="$video" framerate="25.0"/>
     <VideoRectangle name="box" toName="video" />
  </View>

  <!--{
   "video": "/static/samples/opossum_snow.mp4"
  }-->

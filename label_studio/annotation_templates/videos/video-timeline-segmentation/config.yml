title: Video Timeline Segmentation
type: community
group: Videos
image: /static/templates/video-timeline-segmentation.png
details: <h1>Select and classify video segments</h1>
config: |
  <View>
    <Header value="Video timeline segmentation via Audio sync trick"/>
    <Video name="video" value="$video_url" sync="audio"/>
    <Labels name="tricks" toName="audio" choice="multiple">
      <Label value="Kickflip" background="#1BB500"/>
      <Label value="360 Flip" background="#FFA91D"/>
      <Label value="Trick" background="#358EF3"/>
    </Labels>
    <Audio name="audio" value="$video_url" sync="video" speed="false"/>
  </View>

  <!--
    Audio tag uses the same $video file to be in sync, video is muted
  -->

  <!--{
   "video_url": "/static/samples/opossum_snow.mp4"
  }-->


title: PDF Classification
type: community
group: Structured Data Parsing
image: /static/templates/pdf-classification.png
details: <h1>Classify PDF documents</h1>
config: |
  <View>
    <Header value="Rate this article"/>
    <Rating name="rating" toName="pdf" maxRating="10" icon="star" size="medium" />

    <Choices name="choices" choice="single-radio" toName="pdf" showInline="true">
      <Choice value="Important article"/>
      <Choice value="Yellow press"/>
    </Choices>
    <Pdf name="pdf" value="$pdf"/>
  </View>


  <!-- {
    "data": {
      "pdf": "/static/samples/sample.pdf"
    }
  } -->


# Generated by Django 4.2.16 on 2024-10-30 17:57

from django.db import migrations, models
from django.db import connection
from django.conf import settings

from core.models import AsyncMigrationStatus
from core.redis import start_job_async_or_sync

import logging
logger = logging.getLogger(__name__)
migration_name = '0052_auto_20241030_1757'

if connection.vendor == 'sqlite':
    sql_update_created_at = """
    UPDATE tasks_tasklock
    SET created_at = datetime(expire_at, %s);
    """
    sql_params = (f'-{settings.TASK_LOCK_TTL} seconds',)
else:
    sql_update_created_at = """
    UPDATE tasks_tasklock
    SET created_at = expire_at - INTERVAL %s;
    """
    sql_params = ('%s seconds' % settings.TASK_LOCK_TTL,)

def forward_migration(migration_name):
    migration = AsyncMigrationStatus.objects.create(
        name=migration_name,
        status=AsyncMigrationStatus.STATUS_STARTED,
    )
    logger.info(f'Start async migration {migration_name}')

    with connection.cursor() as cursor:
        cursor.execute(sql_update_created_at, sql_params)

    migration.status = AsyncMigrationStatus.STATUS_FINISHED
    migration.save()
    logger.info(f'Async migration {migration_name} complete')

def forwards(apps, schema_editor):
    # Dispatch migrations to rqworkers
    start_job_async_or_sync(forward_migration, migration_name=migration_name)

def backwards(apps, schema_editor):
    pass

class Migration(migrations.Migration):
    atomic = False

    dependencies = [
        ('tasks', '0051_tasklock_created_at'),
    ]

    operations = [
        migrations.AlterField(
            model_name='tasklock',
            name='created_at',
            field=models.DateTimeField(auto_now_add=True, help_text='Creation time', null=True, verbose_name='created at'),
        ),
        migrations.RunPython(forwards, backwards),
    ]


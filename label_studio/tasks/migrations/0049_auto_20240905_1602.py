# Generated by Django 3.2.25 on 2024-09-05 16:02

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('tasks', '0048_failedprediction'),
    ]

    operations = [
        migrations.CreateModel(
            name='PredictionMeta',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('inference_time', models.FloatField(blank=True, help_text='Time taken for inference in seconds', null=True, verbose_name='inference time')),
                ('prompt_tokens_count', models.IntegerField(blank=True, help_text='Number of tokens in the prompt', null=True, verbose_name='prompt tokens count')),
                ('completion_tokens_count', models.IntegerField(blank=True, help_text='Number of tokens in the completion', null=True, verbose_name='completion tokens count')),
                ('total_tokens_count', models.IntegerField(blank=True, help_text='Total number of tokens', null=True, verbose_name='total tokens count')),
                ('prompt_cost', models.FloatField(blank=True, help_text='Cost of the prompt', null=True, verbose_name='prompt cost')),
                ('completion_cost', models.FloatField(blank=True, help_text='Cost of the completion', null=True, verbose_name='completion cost')),
                ('total_cost', models.FloatField(blank=True, help_text='Total cost', null=True, verbose_name='total cost')),
                ('extra', models.JSONField(blank=True, help_text='Additional metadata in JSON format', null=True, verbose_name='extra')),
                ('failed_prediction', models.OneToOneField(blank=True, help_text='Reference to the associated failed prediction', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='meta', to='tasks.failedprediction')),
                ('prediction', models.OneToOneField(blank=True, help_text='Reference to the associated prediction', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='meta', to='tasks.prediction')),
            ],
            options={
                'verbose_name': 'Prediction Meta',
                'verbose_name_plural': 'Prediction Metas',
                'db_table': 'prediction_meta',
            },
        ),
        migrations.AddConstraint(
            model_name='predictionmeta',
            constraint=models.CheckConstraint(check=models.Q(models.Q(('prediction__isnull', False), ('failed_prediction__isnull', True)), models.Q(('prediction__isnull', True), ('failed_prediction__isnull', False)), _connector='OR'), name='prediction_or_failed_prediction_not_null'),
        ),
    ]

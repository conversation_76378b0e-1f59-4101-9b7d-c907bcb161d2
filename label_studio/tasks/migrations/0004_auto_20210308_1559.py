# Generated by Django 3.1.4 on 2021-03-08 15:59

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('projects', '0006_auto_20210308_1559'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('tasks', '0003_merge_20210308_1141'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='task',
            options={'ordering': ['-updated_at']},
        ),
        migrations.RemoveField(
            model_name='annotation',
            name='prediction_equal_score',
        ),
        migrations.RemoveField(
            model_name='prediction',
            name='ground_truth_match',
        ),
        migrations.RemoveField(
            model_name='task',
            name='accuracy',
        ),
        migrations.RemoveField(
            model_name='task',
            name='taken_at',
        ),
        migrations.AlterField(
            model_name='annotation',
            name='completed_by',
            field=models.ForeignKey(help_text='User ID of the person who created this annotation', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='annotations', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='annotation',
            name='honeypot',
            field=models.BooleanField(default=False, help_text='This annotation is a Ground Truth (honeypot)', verbose_name='honeypot'),
        ),
        migrations.AlterField(
            model_name='annotation',
            name='lead_time',
            field=models.FloatField(default=None, help_text='How much time it took to annotate the task', null=True, verbose_name='lead time'),
        ),
        migrations.AlterField(
            model_name='annotation',
            name='updated_at',
            field=models.DateTimeField(auto_now=True, help_text='Last updated time', verbose_name='updated at'),
        ),
        migrations.AlterField(
            model_name='annotationdraft',
            name='lead_time',
            field=models.FloatField(help_text='How much time it took to annotate the task', verbose_name='lead time'),
        ),
        migrations.AlterField(
            model_name='annotationdraft',
            name='user',
            field=models.ForeignKey(help_text='User who created this draft', on_delete=django.db.models.deletion.CASCADE, related_name='drafts', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='prediction',
            name='neighbors',
            field=models.JSONField(blank=True, help_text='Array of task IDs of the closest neighbors', null=True, verbose_name='neighbors'),
        ),
        migrations.AlterField(
            model_name='task',
            name='created_at',
            field=models.DateTimeField(auto_now_add=True, help_text='Time a task was created', verbose_name='created at'),
        ),
        migrations.AlterField(
            model_name='task',
            name='data',
            field=models.JSONField(help_text='User imported (uploaded) data for the task. Data is formatted according to the project label config. You can find examples of data for your project on the Import page in the Label Studio Data Manager UI.', verbose_name='data'),
        ),
        migrations.AlterField(
            model_name='task',
            name='is_labeled',
            field=models.BooleanField(default=False, help_text='True if the annotation number for this task is greater than or equal to the number of maximum_completions for the project', verbose_name='is_labeled'),
        ),
        migrations.AlterField(
            model_name='task',
            name='meta',
            field=models.JSONField(default=dict, help_text='Meta is user imported (uploaded) data and can be useful as input for an ML Backend, for embeddings, advanced vectors, and other info. It is passed to ML during training/predicting steps.', null=True, verbose_name='meta'),
        ),
        migrations.AlterField(
            model_name='task',
            name='overlap',
            field=models.IntegerField(db_index=True, default=1, help_text='Number of distinct annotators that processed the current task', verbose_name='overlap'),
        ),
        migrations.AlterField(
            model_name='task',
            name='project',
            field=models.ForeignKey(help_text='Project ID for this task', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='tasks', to='projects.project'),
        ),
        migrations.AlterField(
            model_name='task',
            name='updated_at',
            field=models.DateTimeField(auto_now=True, help_text='Last time a task was updated', verbose_name='updated at'),
        ),
        migrations.DeleteModel(
            name='Review',
        ),
    ]

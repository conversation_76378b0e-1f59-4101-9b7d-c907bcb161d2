# Generated by Django 3.2.24 on 2024-03-14 19:57

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('ml', '0007_auto_20240314_1957'),
        ('tasks', '0045_auto_20231124_1238'),
    ]

    operations = [
        migrations.AddField(
            model_name='prediction',
            name='model',
            field=models.ForeignKey(help_text='An ML Backend instance that created the prediction.', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='predictions', to='ml.mlbackend'),
        ),
        migrations.AlterField(
            model_name='prediction',
            name='model_version',
            field=models.TextField(blank=True, default='', help_text='A string value that for model version that produced the prediction. Used in both live models and when uploading offline predictions.', null=True, verbose_name='model version'),
        ),
    ]

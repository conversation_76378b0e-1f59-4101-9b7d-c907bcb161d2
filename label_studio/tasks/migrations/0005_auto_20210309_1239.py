# Generated by Django 3.1.4 on 2021-03-09 12:39

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('tasks', '0004_auto_20210308_1559'),
    ]

    operations = [
        migrations.RemoveIndex(
            model_name='annotation',
            name='task_comple_task_id_07c6ca_idx',
        ),
        migrations.RenameField(
            model_name='annotation',
            old_name='honeypot',
            new_name='ground_truth',
        ),
        migrations.AlterField(
            model_name='annotation',
            name='ground_truth',
            field=models.BooleanField(default=False, help_text='This annotation is a Ground Truth (ground_truth)', verbose_name='ground_truth'),
        ),
        migrations.AlterField(
            model_name='task',
            name='data',
            field=models.JSONField(help_text='User imported or uploaded data for a task. Data is formatted according to the project label config. You can find examples of data for your project on the Import page in the Label Studio Data Manager UI.', verbose_name='data'),
        ),
        migrations.Alter<PERSON>ield(
            model_name='task',
            name='is_labeled',
            field=models.BooleanField(default=False, help_text='True if the number of annotations for this task is greater than or equal to the number of maximum_completions for the project', verbose_name='is_labeled'),
        ),
        migrations.AlterField(
            model_name='task',
            name='meta',
            field=models.JSONField(default=dict, help_text='Meta is user imported (uploaded) data and can be useful as input for an ML Backend for embeddings, advanced vectors, and other info. It is passed to ML during training/predicting steps.', null=True, verbose_name='meta'),
        ),
        migrations.AlterField(
            model_name='tasklock',
            name='user',
            field=models.ForeignKey(help_text='User who locked this task', on_delete=django.db.models.deletion.CASCADE, related_name='task_locks', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddIndex(
            model_name='annotation',
            index=models.Index(fields=['task', 'ground_truth'], name='task_comple_task_id_e82920_idx'),
        ),
    ]

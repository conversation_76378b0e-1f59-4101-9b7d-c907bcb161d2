# Generated by Django 3.2.25 on 2024-08-14 20:14

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('ml', '0007_auto_20240314_1957'),
        ('ml_models', '0009_alter_thirdpartymodelversion_provider'),
        ('projects', '0026_auto_20231103_0020'),
        ('tasks', '0047_merge_20240318_2210'),
    ]

    operations = [
        migrations.CreateModel(
            name='FailedPrediction',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('message', models.TextField(blank=True, default=None, help_text='The message explaining why generating this prediction failed', null=True, verbose_name='message')),
                ('error_type', models.CharField(default=None, help_text='The type of error that caused prediction to fail', max_length=512, null=True, verbose_name='error_type')),
                ('model_version', models.TextField(blank=True, default=None, help_text='A string value that for model version that produced the failed prediction. Used in both live models and when uploading offline predictions.', null=True, verbose_name='model version')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='created at')),
                ('ml_backend_model', models.ForeignKey(help_text='An ML Backend instance that created the failed prediction.', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='failed_predictions', to='ml.mlbackend')),
                ('model_run', models.ForeignKey(help_text='A run of a ModelVersion that created the failed prediction.', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='failed_predictions', to='ml_models.modelrun')),
                ('project', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='failed_predictions', to='projects.project')),
                ('task', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='failed_predictions', to='tasks.task')),
            ],
        ),
    ]

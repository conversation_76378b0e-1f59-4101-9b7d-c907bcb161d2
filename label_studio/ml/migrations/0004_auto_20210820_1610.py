# Generated by Django 3.1.12 on 2021-08-20 16:10

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('ml', '0003_auto_20210309_1239'),
    ]

    operations = [
        migrations.AddField(
            model_name='mlbackend',
            name='is_interactive',
            field=models.BooleanField(default=False, help_text="It's used for interactive annotating. If true, model has to return one-length list with results", verbose_name='is_interactive'),
        ),
        migrations.AddField(
            model_name='mlbackend',
            name='timeout',
            field=models.FloatField(blank=True, default=100.0, help_text='Responce model timeout', verbose_name='timeout'),
        ),
        migrations.AlterField(
            model_name='mlbackend',
            name='error_message',
            field=models.TextField(blank=True, help_text='Error message in error state', null=True, verbose_name='error_message'),
        ),
    ]

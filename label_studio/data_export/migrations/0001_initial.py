# Generated by Django 3.1.12 on 2021-09-08 18:13

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('projects', '0012_auto_20210906_1323'),
    ]

    operations = [
        migrations.CreateModel(
            name='Export',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, help_text='Creation time', verbose_name='created at')),
                ('file', models.FileField(null=True, upload_to='export')),
                ('md5', models.CharField(default='', max_length=128, verbose_name='md5 of file')),
                ('finished_at', models.DateTimeField(default=None, help_text='Complete or fail time', null=True, verbose_name='finished at')),
                ('status', models.CharField(choices=[('created', 'Created'), ('in_progress', 'In progress'), ('failed', 'Failed'), ('completed', 'Completed')], default='created', max_length=64, verbose_name='Exporting status')),
                ('counters', models.JSONField(default=dict, verbose_name='Exporting meta data')),
                ('only_finished', models.BooleanField(default=False, help_text='If true - it exports only finished tasks', verbose_name='Only finished')),
                ('task_ids', models.JSONField(default=list, help_text='If list is empty - download all tasks', verbose_name='Task ids list')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_exports', to=settings.AUTH_USER_MODEL, verbose_name='created by')),
                ('project', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='exports', to='projects.project')),
            ],
        ),
    ]

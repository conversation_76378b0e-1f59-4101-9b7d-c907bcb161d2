{"editor_preview": {"TextRaw": "Sample: Your text will go here.", "TextUrl": "https://htx-pub.s3.amazonaws.com/example.txt", "HyperText": "<div style=\"max-width: 750px\"><div style=\"clear: both\"><div style=\"float: right; display: inline-block; border: 1px solid #F2F3F4; background-color: #F8F9F9; border-radius: 5px; padding: 7px; margin: 10px 0;\"><p><b>Jules</b>: No no, Mr. <PERSON>, it's not like that. Your help is definitely appreciated.</p></div></div><div style=\"clear: both\"><div style=\"float: right; display: inline-block; border: 1px solid #F2F3F4; background-color: #F8F9F9; border-radius: 5px; padding: 7px; margin: 10px 0;\"><p><b>Vincent</b>: Look, Mr. <PERSON>, I respect you. I just don't like people barking orders at me, that's all.</p></div></div><div style=\"clear: both\"><div style=\"display: inline-block; border: 1px solid #D5F5E3; background-color: #EAFAF1; border-radius: 5px; padding: 7px; margin: 10px 0;\"><p><b>The Wolf</b>: If I'm curt with you, it's because time is a factor. I think fast, I talk fast, and I need you two guys to act fast if you want to get out of this. So pretty please, with sugar on top, clean the car.</p></div></div></div>", "HyperTextUrl": "<HOSTNAME>/static/samples/hypertext.html", "Image": "<HOSTNAME>/static/samples/sample.jpg", "Audio": "<HOSTNAME>/static/samples/game.wav", "AudioPlus": "<HOSTNAME>/static/samples/game.wav", "Header": "Sample: Header text", "Paragraphs": [{"author": "<PERSON>", "text": "Sample: Text #1"}, {"author": "<PERSON>", "text": "Sample: Text #2"}, {"author": "<PERSON>", "text": "Sample: Text #3"}, {"author": "<PERSON>", "text": "Sample: Text #4"}, {"author": "<PERSON>", "text": "Sample: Text #5"}], "ParagraphsUrl": "<HOSTNAME>/samples/paragraphs.json?", "Table": {"Card number": 18799210, "First name": "<PERSON><PERSON>", "Last name": "Text"}, "$videoHack": "<video src='<HOSTNAME>/static/samples/opossum_snow.mp4' width=100% controls>", "Video": "<HOSTNAME>/static/samples/opossum_snow.mp4", "Labels": [{"value": "DynamicLabel1", "background": "#ff0000"}, {"value": "DynamicLabel2", "background": "#0000ff"}], "Choices": [{"value": "DynamicChoice1"}, {"value": "DynamicChoice2"}, {"value": "DynamicChoice3"}], "NestedChoices": [{"value": "DynamicChoiceHeader1", "children": [{"value": "DynamicChoice1.1"}, {"value": "DynamicChoice1.2"}]}, {"value": "DynamicChoiceHeader2", "children": [{"value": "DynamicChoice2.1"}, {"value": "DynamicChoice2.2"}, {"value": "DynamicChoice2.3"}]}], "List": [{"id": 1, "title": "Sample: The Amazing World of Opossums", "body": "Opossums are fascinating marsupials native to North America. They have prehensile tails, which help them to climb trees and navigate their surroundings with ease. Additionally, they are known for their unique defense mechanism, called 'playing possum,' where they mimic the appearance and smell of a dead animal to deter predators."}, {"id": 2, "title": "Sample: Opossums: Nature's Pest Control", "body": "Opossums play a crucial role in controlling insect and rodent populations, as they consume a variety of pests like cockroaches, beetles, and mice. This makes them valuable allies for gardeners and homeowners, as they help to maintain a balanced ecosystem and reduce the need for chemical pest control methods."}, {"id": 3, "title": "Sample: Fun Fact: Opossums Are Immune to Snake Venom", "body": "One surprising characteristic of opossums is their natural immunity to snake venom. They have a unique protein in their blood called 'Lethal Toxin-Neutralizing Factor' (LTNF), which neutralizes venom from a variety of snake species, including rattlesnakes and cottonmouths. This allows opossums to prey on snakes without fear of harm, further highlighting their important role in the ecosystem."}], "$longText": "Sample: This is a sample text for long text task. It can be used for text classification, named entity recognition, etc.", "$corefText": "Sample: This is a sample text for coreference resolution and entity linking task.", "$pdf": "<embed src='<HOSTNAME>/static/samples/sample.pdf' width='100%' height='600px'/>", "$website": "<iframe src='http://heartex.ai' width='100%' height='600px'/>", "$headlessCsv": "<HOSTNAME>/static/samples/sample-task-sin-headless.csv", "$humanMachineDialogue": [{"author": "Human", "text": "Sample: Hi, <PERSON>!"}, {"author": "Robot", "text": "<PERSON><PERSON>: Nice to meet you, human! Tell me what you want."}, {"author": "Human", "text": "Sam<PERSON>: Order me a pizza from Golden Boy at Green Street "}, {"author": "Robot", "text": "Sample: Done. When do you want to get the order?"}, {"author": "Human", "text": "Sample: At 3am in the morning, please"}], "$respone": "Sample: Response #1", "$resptwo": "Sample: Response #2", "$respthree": "Sample: Response #3", "$ocr": "https://htx-pub.s3.amazonaws.com/demo/ocr/example.jpg", "$ner": "Sample: This is a sample text for named entity recognition task.", "$captioning": "<HOSTNAME>/static/samples/trees_in_snow.jpg", "$pairText1": "Sample: Text #1", "$pairText2": "Sample: Text #2"}, "upload": {"TextRaw": "Sample: Your text will go here.", "TextUrl": "https://htx-pub.s3.amazonaws.com/example.txt", "HyperText": "<a href='https://labelstud.io'>Label Studio</a>", "Image": "<HOSTNAME>/static/samples/sample.jpg", "Audio": "<HOSTNAME>/static/samples/game.wav", "AudioPlus": "<HOSTNAME>/static/samples/game.wav", "Header": "Sample: Header text", "Paragraphs": [{"author": "<PERSON>", "text": "Sample: <PERSON>, <PERSON>."}, {"author": "<PERSON>", "text": "Sample: Hello, <PERSON>!"}, {"author": "<PERSON>", "text": "<PERSON><PERSON>: What's up?"}, {"author": "<PERSON>", "text": "Sample: <PERSON>. <PERSON><PERSON><PERSON>!"}, {"author": "<PERSON>", "text": "Sample: <PERSON>, <PERSON>."}], "Table": {"Card number": 18799210, "First name": "<PERSON><PERSON>", "Last name": "Text"}, "$videoHack": "<video src='static/samples/opossum_snow.mp4' width=100% controls>", "Video": "<HOSTNAME>/static/samples/opossum_snow.mp4", "$longText": "Sample: This is a sample text for long text task. It can be used for text classification, named entity recognition, etc.", "$pdf": "<embed src='<HOSTNAME>/static/samples/sample.pdf' width='100%' height='600px'/>", "$website": "<iframe src='https://labelstud.io' width='100%' height='600px'/>", "$headlessCsv": "<HOSTNAME>/static/samples/sample-task-sin-headless.csv"}}
<!-- {"title": "Video timeline segmentation", "category": "other", "complexity": "advanced"} -->
<View>
  <Header value="Video timeline segmentation via Audio sync trick"/>
  <HyperText name="video" value="$video"/>
  <Labels name="tricks" toName="audio" choice="multiple">
    <Label value="Kickflip" background="#1BB500" />
    <Label value="360 Flip" background="#FFA91D" />
    <Label value="Trick" background="#358EF3" />
  </Labels>
  <Audio name="audio" value="$videoSource" speed="false"/>
</View>

<!--
  It's very important to prepare task data correctly,
  it includes HyperText $video and
  it must be like this example below:
-->

<!-- {
 "video": "<video src='/static/samples/opossum_snow.mp4' width=100% muted /><img src onerror=\"$=n=>document.getElementsByTagName(n)[0];a=$('audio');v=$('video');a.onseeked=()=>{v.currentTime=a.currentTime};a.onplay=()=>v.play();a.onpause=()=>v.pause()\" />",
 "videoSource": "/static/samples/opossum_snow.mp4"
} -->

<!-- {"title": "Two level classification", "category": "nested", "complexity": "advanced"} -->
<View>

  <Text name="text" value="$text" />
  <Choices name="sentiment" toName="text" showInLine="true">
    <Choice value="Positive" />
    <Choice value="Negative" />
    <Choice value="Neutral" />
  </Choices>

  <Choices name="other-props" toName="text"
  	   choice="single" showInLine="true"
           visibleWhen="choice-selected"
           whenTagName="sentiment">
    <View style="width:100%">
      <Header value="Other properties of the text" />
    </View>
    <Choice value="Descriptive" />
    <Choice value="Emotional" />
  </Choices>

</View>
<!-- {"title": "Text spans labeling", "category": "per-region", "complexity": "advanced"} -->
<View style="display: flex;">
  <View style="width: 150px; padding-left: 2em; margin-right: 2em; background: #f1f1f1; border-radius: 3px">
    <Labels name="ner" toName="text">
      <Label value="Person" />
      <Label value="Organization" />
    </Labels>
  </View>

  <View>
    <View style="height: 200px; overflow-y: auto">
      <Text name="text" value="$text" />
    </View>

    <View>
      <Choices name="relevance" toName="text" perRegion="true">
      	<Choice value="Relevant" />
        <Choice value="Non Relevant" />
      </Choices>

      <View visibleWhen="region-selected">
      	<Header value="Your confidence" />
      </View>
      <Rating name="confidence" toName="text" perRegion="true" />
    </View>

    <View style="width: 100%; display: block">
      <Header value="Select span after creation to go next"/>
    </View>
  </View>

</View>

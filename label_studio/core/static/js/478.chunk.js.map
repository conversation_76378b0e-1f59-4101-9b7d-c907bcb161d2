{"version": 3, "file": "static/js/478.chunk.js", "mappings": "CAAA,SAA2CA,EAAMC,GAC1B,iBAAZC,SAA0C,iBAAXC,OACxCA,OAAOD,QAAUD,IACQ,mBAAXG,QAAyBA,OAAOC,IAC9CD,OAAO,GAAIH,GACe,iBAAZC,QACdA,QAAqB,YAAID,IAEzBD,EAAkB,YAAIC,GACvB,CATD,CASoB,oBAATK,KAAuBA,KAAOC,MAAO,WAChD,O,yeCDO,MAAMC,EAqDXC,YAAYC,GAAaC,EAAA,sBACvBJ,KAAKK,OAASF,CAChB,CAEAG,cAAcC,GAA4B,IAADC,EAAAC,EACvC,MAAMC,QAAeV,KAAKW,YAAYX,KAAKK,OAAQ,CACjDE,OACAK,KAAM,YACL,GAEH,OAAOF,SAAY,QAANF,EAANE,EAAQH,YAAI,IAAAC,GAAQ,QAARC,EAAZD,EAAcE,cAAM,IAAAD,OAAd,EAANA,EAAsBF,IAC/B,CAEAD,iBAAiBC,SACTP,KAAKW,YAAYX,KAAKK,OAAQ,CAClCE,OACAK,KAAM,cAEV,CAEAN,YAAYC,SACJP,KAAKW,YAAYX,KAAKK,OAAQ,CAClCE,OACAK,KAAM,SAEV,CAEAN,mBAAoB,IAADO,EACjB,MAAMC,QAAiBd,KAAKW,YAAYX,KAAKK,OAAQ,CACnDO,KAAM,eACL,GAEH,OAAOE,SAAc,QAAND,EAARC,EAAUP,YAAI,IAAAM,OAAN,EAARA,EAAgBH,MACzB,CAEAK,UACEf,KAAKK,OAAOW,WACd,CAEQL,YAAYN,EAAgBE,EAA2BU,GAAe,GAC5E,OAAO,IAAIC,SAAmCC,IAC5C,MAAMC,EAAUC,KAAKC,SAASC,WAE9B,GAAIN,EAAc,CAChB,MAAMO,EAAYC,IACZL,IAAYK,EAAElB,KAAKa,UACrBf,EAAOqB,oBAAoB,UAAWF,GACtCL,EAAQM,GACV,EAGFpB,EAAOsB,iBAAiB,UAAWH,EACrC,CAEAnB,EAAOuB,YAAY,IAAKrB,EAAMa,YAEzBH,GAAcE,OAAQU,EAAU,GAEzC,ECtHK,SAASC,GAAc,MAC5BC,EAAK,aACLC,IAKA,MAAMC,EAA2B,GAGjC,IAAK,IAAIC,EAAI,EAAGA,EAAIF,EAAcE,IAChCD,EAASC,GAAK,IAAIC,aAAaJ,EAAMK,OAASJ,GAIhD,IAAK,IAAIK,EAAS,EAAGA,EAASN,EAAMK,OAAQC,IAAU,CAIpD,MAAMC,EAAUD,EAASL,EAOnBO,EAAelB,KAAKmB,MAAMH,EAASL,GAEzCC,EAASK,GAASC,GAAgBR,EAAMM,EAC1C,CAEA,OAAOJ,CACT,C,ODuFC7B,EAhHYH,EAAa,YAGL,CACjBwC,SACEC,QAASC,EACTC,WAAYC,IAEZ,MAAMC,EAA+B,CAAC,EA8BtC/C,KAAK4B,iBAAiB,WAAYF,IAChC,IAAKA,EAAElB,KAAM,OAEb,MAAM,KAAEA,EAAI,KAAEK,EAAI,QAAEQ,GAAYK,EAAElB,KAElC,OAAQK,GACN,IAAK,UA9BO8B,EAACnC,EAAWa,KAQ1BuB,EAAgBpC,EAAMuC,GAPLpC,IACfX,KAAK6B,YAAY,CACflB,SACAU,WACA,GAGmC,EAsBrBsB,CAAQnC,EAAMa,GAAU,MACxC,IAAK,aApBWb,KAClBsC,SAAAA,EAAqBtC,EAAMuC,GAAUpC,IACnCqC,OAAOC,OAAOF,EAASpC,EAAO,GAC9B,EAiBmBkC,CAAWrC,GAAO,MACrC,IAAK,QApCUkB,KACjBsB,OAAOC,OAAOF,EAASrB,EAAElB,KAAKA,KAAK,EAmCnB0C,CAAUxB,GAAI,MAC5B,IAAK,aAhBWL,KAClBrB,KAAK6B,YAAY,CACflB,OAAQoC,EACR1B,WACA,EAYmB8B,CAAW9B,GAChC,GAEJ,ICvBJnB,EAAckD,UAAUV,QAAQ,CAC9BC,QAASA,CAACnC,EAAM6C,EAAUC,KACxBA,EAAQ,CACN9C,KAAMuB,EAAcvB,IACpB,EAGJqC,WAAYA,CAACrC,EAAM6C,EAAUC,KAC3BA,EAAQ,CACN9C,KAAMuB,EAAcvB,IACpB,I", "sources": ["webpack://LabelStudio/webpack/universalModuleDefinition", "webpack://LabelStudio/./src/lib/AudioUltra/Common/Worker/index.ts", "webpack://LabelStudio/./src/lib/AudioUltra/Media/SplitChannelWorker.ts"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"LabelStudio\"] = factory();\n\telse\n\t\troot[\"LabelStudio\"] = factory();\n})((typeof self !== 'undefined' ? self : this), function() {\nreturn ", "type MessengerResponder = (result: Record<string, any>) => void;\n\ntype MessengerCallback = (data: any, storage: Record<string, any>, respond: MessengerResponder) => void;\n\ntype MessengerInput = {\n  compute: MessengerCallback,\n  precompute?: MessengerCallback,\n}\n\nexport class ComputeWorker {\n  private worker: Worker;\n\n  static Messenger = {\n    receive({\n      compute: computeCallback,\n      precompute: precomputeCallback,\n    }: MessengerInput) {\n      const storage: Record<string, any> = {};\n\n      const storeData = (e: MessageEvent) => {\n        Object.assign(storage, e.data.data);\n      };\n\n      const compute = (data: any, eventId: string) => {\n        const respond = (result: Record<string, any>) => {\n          self.postMessage({\n            result,\n            eventId,\n          });\n        };\n\n        computeCallback(data, storage, respond);\n      };\n\n      const precompute = (data: any) => {\n        precomputeCallback?.(data, storage, (result) => {\n          Object.assign(storage, result);\n        });\n      };\n\n      const getStorage = (eventId: string) => {\n        self.postMessage({\n          result: storage,\n          eventId,\n        });\n      };\n\n      self.addEventListener('message', (e) => {\n        if (!e.data) return;\n\n        const { data, type, eventId } = e.data;\n\n        switch (type) {\n          case 'compute': compute(data, eventId); break;\n          case 'precompute': precompute(data); break;\n          case 'store': storeData(e); break;\n          case 'getStorage': getStorage(eventId); break;\n        }\n      });\n    },\n  };\n\n  constructor(url: Worker) {\n    this.worker = url;\n  }\n\n  async compute(data: Record<string, any>) {\n    const result = await this.sendMessage(this.worker, {\n      data,\n      type: 'compute',\n    }, true);\n\n    return result?.data?.result?.data;\n  }\n\n  async precompute(data: Record<string, any>) {\n    await this.sendMessage(this.worker, {\n      data,\n      type: 'precompute',\n    });\n  }\n\n  async store(data: Record<string, any>) {\n    await this.sendMessage(this.worker, {\n      data,\n      type: 'store',\n    });\n  }\n\n  async getStorage() {\n    const response = await this.sendMessage(this.worker, {\n      type: 'getStorage',\n    }, true);\n\n    return response?.data?.result;\n  }\n\n  destroy() {\n    this.worker.terminate();\n  }\n\n  private sendMessage(worker: Worker, data: Record<string, any>, waitResponse = false) {\n    return new Promise<MessageEvent | undefined>((resolve) => {\n      const eventId = Math.random().toString();\n\n      if (waitResponse) {\n        const resolver = (e: MessageEvent) => {\n          if (eventId === e.data.eventId) {\n            worker.removeEventListener('message', resolver);\n            resolve(e);\n          }\n        };\n\n        worker.addEventListener('message', resolver);\n      }\n\n      worker.postMessage({ ...data, eventId });\n\n      if (!waitResponse) resolve(undefined);\n    });\n  }\n}\n", "import { ComputeWorker } from '../Common/Worker';\n\nexport function splitChannels({\n  value,\n  channelCount,\n}: {\n  value: Float32Array,\n  channelCount: number,\n}) : Float32Array[] {\n  const channels: Float32Array[] = [];\n\n  // Create new Float32Array for each channel\n  for (let c = 0; c < channelCount; c++) {\n    channels[c] = new Float32Array(value.length / channelCount);\n  }\n\n  // Split the channels into separate Float32Array samples\n  for (let sample = 0; sample < value.length; sample++) {\n    // interleaved channels\n    // ie. 2 channels\n    // [channel1, channel2, channel1, channel2, ...]\n    const channel = sample % channelCount;\n    // index of the channel sample\n    // ie. 2 channels\n    // sample = 8, channel = 0, channelIndex = 4\n    // sample = 9, channel = 1, channelIndex = 4\n    // sample = 10, channel = 0, channelIndex = 5\n    // sample = 11, channel = 1, channelIndex = 5\n    const channelIndex = Math.floor(sample / channelCount);\n\n    channels[channel][channelIndex] = value[sample];\n  }\n\n  return channels;\n}\n\nComputeWorker.Messenger.receive({\n  compute: (data, _storage, respond) => {\n    respond({\n      data: splitChannels(data),\n    });\n  },\n\n  precompute: (data, _storage, respond) => {\n    respond({\n      data: splitChannels(data),\n    });\n  },\n});\n"], "names": ["root", "factory", "exports", "module", "define", "amd", "self", "this", "ComputeWorker", "constructor", "url", "_defineProperty", "worker", "async", "data", "_result$data", "_result$data$result", "result", "sendMessage", "type", "_response$data", "response", "destroy", "terminate", "waitResponse", "Promise", "resolve", "eventId", "Math", "random", "toString", "resolver", "e", "removeEventListener", "addEventListener", "postMessage", "undefined", "splitChannels", "value", "channelCount", "channels", "c", "Float32Array", "length", "sample", "channel", "channelIndex", "floor", "receive", "compute", "computeCallback", "precompute", "precomputeCallback", "storage", "Object", "assign", "storeData", "getStorage", "<PERSON>", "_storage", "respond"], "sourceRoot": ""}
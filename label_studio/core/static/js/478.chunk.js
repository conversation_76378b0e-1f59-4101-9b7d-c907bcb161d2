!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define([],t):"object"==typeof exports?exports.LabelStudio=t():e.LabelStudio=t()}("undefined"!=typeof self?self:this,(function(){return function(){"use strict";var e={};function t(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var s=r.call(e,t||"default");if("object"!=typeof s)return s;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}class r{constructor(e){t(this,"worker",void 0),this.worker=e}async compute(e){var t,r;const s=await this.sendMessage(this.worker,{data:e,type:"compute"},!0);return null==s||null===(t=s.data)||void 0===t||null===(r=t.result)||void 0===r?void 0:r.data}async precompute(e){await this.sendMessage(this.worker,{data:e,type:"precompute"})}async store(e){await this.sendMessage(this.worker,{data:e,type:"store"})}async getStorage(){var e;const t=await this.sendMessage(this.worker,{type:"getStorage"},!0);return null==t||null===(e=t.data)||void 0===e?void 0:e.result}destroy(){this.worker.terminate()}sendMessage(e,t,r=!1){return new Promise((s=>{const n=Math.random().toString();if(r){const t=r=>{n===r.data.eventId&&(e.removeEventListener("message",t),s(r))};e.addEventListener("message",t)}e.postMessage({...t,eventId:n}),r||s(void 0)}))}}function s({value:e,channelCount:t}){const r=[];for(let s=0;s<t;s++)r[s]=new Float32Array(e.length/t);for(let s=0;s<e.length;s++){const n=s%t,a=Math.floor(s/t);r[n][a]=e[s]}return r}return t(r,"Messenger",{receive({compute:e,precompute:t}){const r={};self.addEventListener("message",(s=>{if(!s.data)return;const{data:n,type:a,eventId:o}=s.data;switch(a){case"compute":((t,s)=>{e(t,r,(e=>{self.postMessage({result:e,eventId:s})}))})(n,o);break;case"precompute":(e=>{null==t||t(e,r,(e=>{Object.assign(r,e)}))})(n);break;case"store":(e=>{Object.assign(r,e.data.data)})(s);break;case"getStorage":(e=>{self.postMessage({result:r,eventId:e})})(o)}}))}}),r.Messenger.receive({compute:(e,t,r)=>{r({data:s(e)})},precompute:(e,t,r)=>{r({data:s(e)})}}),e=e.default}()}));
//# sourceMappingURL=478.chunk.js.map
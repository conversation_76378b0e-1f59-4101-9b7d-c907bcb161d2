html {
    overflow-x: auto;
}

:root {
  --font-sans: 'Figtree', sans-serif !important;
  --font-mono: Monaco, monospace;

  /* Deprecated: [DO NOT USE] */
  --sand_900: #12110D;
  --sand_800: #262522;
  --sand_700: #45433E;
  --sand_600: #6B6860;
  --sand_500: #A49F95;
  --sand_400: #CAC5B8;
  --sand_300: #E1DED5;
  --sand_200: #F0EFEB;
  --sand_100: #F9F8F6;
  --sand_0: #FDFDFC;

  --grape_0: #F0F3FE;
  --grape_100: #D4DBFB; 
  --grape_500: #566FCF;
  --grape_600: #576CC1;
  --grape_700: #4C5FA9;
  --grape_800: #37447A;

  --kale_0: #F4F9F9;
  --kale_400: #57B7AB;

  --plum_500: #CC6FBE;
  --plum_400: #E37BD3;
  --plum_100: #F7D6F2;
  --plum_0: #FBF2FC;

  --persimmon_300: #FF9F89;
  --persimmon_400: #FF7557;

  --canteloupe_0: #FFF6EF;
  --canteloupe_100: #FFE4D0;
  --canteloupe_400: #FFA663;
  --canteloupe_500: #E69559;
  --canteloupe_600: #CC854F;
  --canteloupe_700: #B37445;

  --red_1: #FFF1F0;
  --red_3: #FFCCC7;
  --red_7: #CF1322;
  --red_10: #5C0011;

  --primary_link: #566FCF;

  overflow-x: unset !important;

  /* Primitive Tokens [DO NOT USE] */
  /* Primitive Tokens: Colors */
  --color-blueberry-000: rgb(240 247 254);
  --color-blueberry-100: rgb(212 231 251);
  --color-blueberry-200: rgb(182 214 248);
  --color-blueberry-300: rgb(153 197 245);
  --color-blueberry-400: rgb(83 158 238);
  --color-blueberry-500: rgb(50 135 226);
  --color-blueberry-600: rgb(43 120 202);
  --color-blueberry-700: rgb(43 105 171);
  --color-blueberry-800: rgb(37 80 126);
  --color-blueberry-900: rgb(28 60 95);
  --color-blueberry-950: rgb(11 24 38);
  --color-canteloupe-000: rgb(255 246 239);
  --color-canteloupe-100: rgb(255 228 208);
  --color-canteloupe-200: rgb(255 211 177);
  --color-canteloupe-300: rgb(255 184 130);
  --color-canteloupe-400: rgb(255 166 99);
  --color-canteloupe-500: rgb(230 149 89);
  --color-canteloupe-600: rgb(204 133 79);
  --color-canteloupe-700: rgb(179 116 69);
  --color-canteloupe-800: rgb(153 100 58);
  --color-canteloupe-900: rgb(102 66 40);
  --color-canteloupe-950: rgb(51 28 20);
  --color-fig-000: rgb(248 242 252);
  --color-fig-100: rgb(233 214 247);
  --color-fig-200: rgb(218 189 241);
  --color-fig-300: rgb(197 149 233);
  --color-fig-400: rgb(172 121 210);
  --color-fig-500: rgb(159 108 198);
  --color-fig-600: rgb(146 98 182);
  --color-fig-700: rgb(127 86 159);
  --color-fig-800: rgb(109 74 136);
  --color-fig-900: rgb(91 62 114);
  --color-fig-950: rgb(31 21 38);
  --color-grape-000: rgb(240 243 254);
  --color-grape-100: rgb(212 219 251);
  --color-grape-200: rgb(182 195 248);
  --color-grape-300: rgb(153 171 245);
  --color-grape-400: rgb(109 135 241);
  --color-grape-500: rgb(97 122 218);
  --color-grape-600: rgb(87 108 193);
  --color-grape-700: rgb(76 95 169);
  --color-grape-800: rgb(55 68 122);
  --color-grape-900: rgb(43 54 96);
  --color-grape-950: rgb(17 22 38);
  --color-kale-000: rgb(244 249 249);
  --color-kale-100: rgb(212 241 235);
  --color-kale-200: rgb(171 228 218);
  --color-kale-300: rgb(122 206 193);
  --color-kale-400: rgb(87 183 171);
  --color-kale-500: rgb(52 152 141);
  --color-kale-600: rgb(40 122 114);
  --color-kale-700: rgb(34 98 93);
  --color-kale-800: rgb(32 79 77);
  --color-kale-900: rgb(31 66 64);
  --color-kale-950: rgb(18 38 37);
  --color-kiwi-000: rgb(246 249 244);
  --color-kiwi-100: rgb(222 241 212);
  --color-kiwi-200: rgb(191 228 171);
  --color-kiwi-300: rgb(151 206 122);
  --color-kiwi-400: rgb(120 183 87);
  --color-kiwi-500: rgb(87 152 52);
  --color-kiwi-600: rgb(69 122 40);
  --color-kiwi-700: rgb(56 98 34);
  --color-kiwi-800: rgb(48 79 32);
  --color-kiwi-900: rgb(43 66 31);
  --color-kiwi-950: rgb(25 38 18);
  --color-mango-000: rgb(255 249 239);
  --color-mango-100: rgb(255 238 208);
  --color-mango-200: rgb(255 226 177);
  --color-mango-300: rgb(255 209 130);
  --color-mango-400: rgb(250 186 76);
  --color-mango-500: rgb(244 170 42);
  --color-mango-600: rgb(235 156 20);
  --color-mango-700: rgb(204 142 36);
  --color-mango-800: rgb(160 114 34);
  --color-mango-900: rgb(98 71 24);
  --color-mango-950: rgb(38 28 10);
  --color-persimmon-000: rgb(255 241 238);
  --color-persimmon-100: rgb(255 214 205);
  --color-persimmon-200: rgb(255 186 170);
  --color-persimmon-300: rgb(255 159 137);
  --color-persimmon-400: rgb(255 117 87);
  --color-persimmon-500: rgb(230 105 78);
  --color-persimmon-600: rgb(204 94 70);
  --color-persimmon-700: rgb(179 82 61);
  --color-persimmon-800: rgb(153 70 52);
  --color-persimmon-900: rgb(128 59 44);
  --color-persimmon-950: rgb(38 18 13);
  --color-plum-000: rgb(251 242 252);
  --color-plum-100: rgb(247 214 242);
  --color-plum-200: rgb(241 189 233);
  --color-plum-300: rgb(233 149 220);
  --color-plum-400: rgb(227 123 211);
  --color-plum-500: rgb(204 111 190);
  --color-plum-600: rgb(182 98 169);
  --color-plum-700: rgb(159 86 148);
  --color-plum-800: rgb(136 74 128);
  --color-plum-900: rgb(114 62 106);
  --color-plum-950: rgb(38 21 36);
  --color-sand-000: rgb(253 253 252);
  --color-sand-100: rgb(249 248 246);
  --color-sand-200: rgb(240 239 235);
  --color-sand-300: rgb(225 222 213);
  --color-sand-400: rgb(202 197 184);
  --color-sand-500: rgb(164 159 149);
  --color-sand-600: rgb(107 104 96);
  --color-sand-700: rgb(69 67 62);
  --color-sand-800: rgb(38 37 34);
  --color-sand-850: rgb(30 29 26);
  --color-sand-900: rgb(18 17 13);
  --color-sand-950: rgb(13 12 9);

  /* Semantic Tokens */
  /* Semantic Tokens: Colors */
  
  /* Light Color Scheme (Default) */
  --color-accent-blueberry-bold: var(--color-blueberry-600);
  --color-accent-blueberry-dark: var(--color-blueberry-900);
  --color-accent-blueberry-subtle: var(--color-blueberry-100);
  --color-accent-blueberry-subtlest: var(--color-blueberry-000);
  --color-accent-canteloupe-bold: var(--color-canteloupe-600);
  --color-accent-canteloupe-dark: var(--color-canteloupe-900);
  --color-accent-canteloupe-subtle: var(--color-canteloupe-100);
  --color-accent-canteloupe-subtlest: var(--color-canteloupe-000);
  --color-accent-fig-bold: var(--color-fig-600);
  --color-accent-fig-dark: var(--color-fig-900);
  --color-accent-fig-subtle: var(--color-fig-100);
  --color-accent-fig-subtlest: var(--color-fig-000);
  --color-accent-grape-bold: var(--color-grape-600);
  --color-accent-grape-dark: var(--color-grape-900);
  --color-accent-grape-subtle: var(--color-grape-100);
  --color-accent-grape-subtlest: var(--color-grape-000);
  --color-accent-kale-bold: var(--color-kale-600);
  --color-accent-kale-dark: var(--color-kale-900);
  --color-accent-kale-subtle: var(--color-kale-100);
  --color-accent-kale-subtlest: var(--color-kale-000);
  --color-accent-kiwi-bold: var(--color-kiwi-600);
  --color-accent-kiwi-dark: var(--color-kiwi-900);
  --color-accent-kiwi-subtle: var(--color-kiwi-100);
  --color-accent-kiwi-subtlest: var(--color-kiwi-000);
  --color-accent-mango-bold: var(--color-mango-600);
  --color-accent-mango-dark: var(--color-mango-900);
  --color-accent-mango-subtle: var(--color-mango-100);
  --color-accent-mango-subtlest: var(--color-mango-000);
  --color-accent-persimmon-bold: var(--color-persimmon-600);
  --color-accent-persimmon-dark: var(--color-persimmon-900);
  --color-accent-persimmon-subtle: var(--color-persimmon-100);
  --color-accent-persimmon-subtlest: var(--color-persimmon-000);
  --color-accent-plum-bold: var(--color-plum-600);
  --color-accent-plum-dark: var(--color-plum-900);
  --color-accent-plum-subtle: var(--color-plum-100);
  --color-accent-plum-subtlest: var(--color-plum-000);
  --color-accent-sand-bold: var(--color-sand-600);
  --color-accent-sand-dark: var(--color-sand-900);
  --color-accent-sand-subtle: var(--color-sand-100);
  --color-accent-sand-subtlest: var(--color-sand-100);
  --color-negative-background: var(--color-persimmon-000);
  --color-negative-emphasis: var(--color-persimmon-100);
  --color-negative-emphasis-subtle: var(--color-persimmon-000);
  --color-negative-border: var(--color-persimmon-700);
  --color-negative-border-bold: var(--color-persimmon-800);
  --color-negative-border-subtle: var(--color-persimmon-500);
  --color-negative-border-subtler: var(--color-persimmon-300);
  --color-negative-border-subtlest: var(--color-persimmon-200);
  --color-negative-content: var(--color-persimmon-700);
  --color-negative-content-hover: var(--color-persimmon-500);
  --color-negative-content-subtle: var(--color-persimmon-500);
  --color-negative-focus-outline: var(--color-persimmon-200);
  --color-negative-icon: var(--color-persimmon-500);
  --color-negative-surface: var(--color-persimmon-600);
  --color-negative-surface-content: var(--color-persimmon-000);
  --color-negative-surface-content-subtle: var(--color-persimmon-100);
  --color-negative-surface-active: var(--color-persimmon-800);
  --color-negative-surface-icon: var(--color-persimmon-100);
  --color-negative-surface-hover: var(--color-persimmon-500);
  --color-neutral-background: var(--color-sand-000);
  --color-neutral-background-bold: var(--color-sand-000);
  --color-neutral-emphasis: var(--color-sand-200);
  --color-neutral-emphasis-subtle: var(--color-sand-100);
  --color-neutral-border: var(--color-sand-300);
  --color-neutral-border-bold: var(--color-sand-400);
  --color-neutral-border-bolder: var(--color-sand-500);
  --color-neutral-border-boldest: var(--color-sand-600);
  --color-neutral-border-subtle: var(--color-sand-300);
  --color-neutral-border-subtler: var(--color-sand-200);
  --color-neutral-content: var(--color-sand-800);
  --color-neutral-content-subtle: var(--color-sand-700);
  --color-neutral-content-subtler: var(--color-sand-600);
  --color-neutral-content-subtlest: var(--color-sand-500);
  --color-neutral-icon: var(--color-sand-700);
  --color-neutral-shadow: var(--color-sand-900);
  --color-neutral-surface: var(--color-sand-100);
  --color-neutral-surface-active: var(--color-sand-200);
  --color-neutral-surface-hover: var(--color-sand-000);
  --color-neutral-surface-inset: var(--color-sand-200);
  --color-neutral-inverted-background: var(--color-sand-900);
  --color-neutral-inverted-border: var(--color-sand-800);
  --color-neutral-inverted-content: var(--color-sand-100);
  --color-neutral-inverted-content-subtle: var(--color-sand-400);
  --color-neutral-inverted-content-subtler: var(--color-sand-300);
  --color-neutral-inverted-content-subtlest: var(--color-sand-500);
  --color-neutral-inverted-surface: var(--color-sand-900);
  --color-neutral-inverted-surface-active: var(--color-sand-700);
  --color-neutral-inverted-surface-hover: var(--color-sand-800);
  --color-positive-background: var(--color-kale-000);
  --color-positive-emphasis: var(--color-kale-100);
  --color-positive-emphasis-subtle: var(--color-kale-000);
  --color-positive-border: var(--color-kale-700);
  --color-positive-border-bold: var(--color-kale-800);
  --color-positive-border-subtle: var(--color-kale-500);
  --color-positive-border-subtler: var(--color-kale-300);
  --color-positive-border-subtlest: var(--color-kale-200);
  --color-positive-content: var(--color-kale-700);
  --color-positive-content-hover: var(--color-kale-500);
  --color-positive-content-subtle: var(--color-kale-500);
  --color-positive-focus-outline: var(--color-kale-200);
  --color-positive-icon: var(--color-kale-500);
  --color-positive-surface: var(--color-kale-600);
  --color-positive-surface-content: var(--color-kale-000);
  --color-positive-surface-content-subtle: var(--color-kale-100);
  --color-positive-surface-active: var(--color-kale-800);
  --color-positive-surface-icon: var(--color-kale-100);
  --color-positive-surface-hover: var(--color-kale-500);
  --color-primary-background: var(--color-grape-000);
  --color-primary-emphasis: var(--color-grape-100);
  --color-primary-emphasis-subtle: var(--color-grape-000);
  --color-primary-border: var(--color-grape-700);
  --color-primary-border-bold: var(--color-grape-800);
  --color-primary-border-subtle: var(--color-grape-500);
  --color-primary-border-subtler: var(--color-grape-300);
  --color-primary-border-subtlest: var(--color-grape-200);
  --color-primary-content: var(--color-grape-700);
  --color-primary-content-hover: var(--color-grape-500);
  --color-primary-content-subtle: var(--color-grape-500);
  --color-primary-focus-outline: var(--color-grape-200);
  --color-primary-icon: var(--color-grape-500);
  --color-primary-shadow: var(--color-grape-900);
  --color-primary-surface: var(--color-grape-700);
  --color-primary-surface-content: var(--color-grape-000);
  --color-primary-surface-content-subtle: var(--color-grape-100);
  --color-primary-surface-active: var(--color-grape-800);
  --color-primary-surface-icon: var(--color-grape-100);
  --color-primary-surface-hover: var(--color-grape-600);
  --color-warning-background: var(--color-canteloupe-000);
  --color-warning-emphasis: var(--color-canteloupe-200);
  --color-warning-emphasis-subtle: var(--color-canteloupe-100);
  --color-warning-border: var(--color-canteloupe-700);
  --color-warning-border-bold: var(--color-canteloupe-800);
  --color-warning-border-subtle: var(--color-canteloupe-500);
  --color-warning-border-subtler: var(--color-canteloupe-300);
  --color-warning-border-subtlest: var(--color-canteloupe-200);
  --color-warning-content: var(--color-canteloupe-700);
  --color-warning-content-hover: var(--color-canteloupe-500);
  --color-warning-content-subtle: var(--color-canteloupe-500);
  --color-warning-focus-outline: var(--color-canteloupe-200);
  --color-warning-icon: var(--color-canteloupe-500);
  --color-warning-surface: var(--color-canteloupe-600);
  --color-warning-surface-content: var(--color-canteloupe-000);
  --color-warning-surface-content-subtle: var(--color-canteloupe-100);
  --color-warning-surface-active: var(--color-canteloupe-800);
  --color-warning-surface-icon: var(--color-canteloupe-100);
  --color-warning-surface-hover: var(--color-canteloupe-500);
}

h1, h2, h3, h4, h5, h6 {
    font-weight: normal;
    color: var(--color-neutral-content);
}
a {
    text-decoration: none;
}

table th {
    background-color: #fcfcfc;
}

table td {
    font-size: 13px;
}
table tr.positive {
    color: #222222 !important;
    background-color: #fafffb;
}

table tr:nth-child(2n).positive {
    background-color: #f5fff7;
}

table th {
    font-weight: normal;
    font-size: 1em;
}

.team-header {
    background: #f7fcff !important;
}

.small-text {
    font-size: 0.3em;
    color: grey;
    line-height: 0;
}

.message a.no-go {
    text-decoration: none;
    border-bottom: 1px dashed #5994cb;
}

.segment .support {
    text-align: center;
    color: #666;
    font-size: 1.2em;
    max-width: 600px;
    margin: auto;
}

pre {
    white-space: pre-wrap; /* css-3 */
    white-space: -moz-pre-wrap;
    white-space: -pre-wrap;
    white-space: -o-pre-wrap;
    word-wrap: break-word;
}

.tab {
    padding: 2em !important;
}

.ui.divider-wide {
    margin: 2rem 0 !important;
}

.sidebar {
    background: #302b3c;
    visibility: visible !important;
    width: 230px !important;
}
.sidebar .item {
    font-size: 16px;
}
.breadcrumb .active, .breadcrumbs-menu .active {
    font-weight: lighter !important;
}

.brown {
    background-color: #3A6EA5 !important;
}

.pusher {
    display: flex;
    min-height: 100vh;
    min-width: 840px;
    flex-direction: column;
    background: #FCFCFC !important;
}

.pusher > .main {
    flex: 1;
    width: 100%;
}

.pushable >.pusher {
    overflow: unset
}

main.main {
    background: var(--color-neutral-background);
    padding: 2em 4rem 2rem 2rem;
}

.ui.label {
    margin-bottom: 3px;
}

.zero-margin {
    margin: 0 !important;
}

.zero-padding {
    padding: 0 !important;
}

.logo-image {
    width: 200px;
}

.ui.vertical.menu .active.item {

    /* background: #444 !important; */
    background: rgba(26, 108, 158, 0.48) !important;
}

.ui.menu .menu {
    margin-top: 4rem !important;
}

.ui.menu .menu .item {
    padding: 0.8em 1.14285714em !important;
    border: 0px;
}

.message .header {
    margin-bottom: 0.5em !important;
}

.cm-s-default {
    border: 1px solid #00000026;
    border-radius: 3px;
}

.logo-sym {
    width: 36px;
    display: block;
    position: absolute;
    top: 2.75em;
    left: 1.2em;
}

.logo {
    display: block;
    height: 20px;
    margin: 3.35em 1em 3.3em 1.25em;
    background: url(data:image/svg+xml;base64,PHN2ZyBpZD0i0KHQu9C+0LlfMSIgZGF0YS1uYW1lPSLQodC70L7QuSAxIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCA0MzguMzIgNzcuMyI+PGRlZnM+PHN0eWxlPi5jbHMtMXtmaWxsOiNmZmY7fTwvc3R5bGU+PC9kZWZzPjx0aXRsZT5sb2dvX2hfd2hpdGU8L3RpdGxlPjxwYXRoIGNsYXNzPSJjbHMtMSIgZD0iTTY0LjUzLDBWNzUuMzloLTIxVjQ2LjQ1SDIxVjc1LjM5SDBWMEgyMVYyNy4ySDQzLjU4VjBaIi8+PHBhdGggY2xhc3M9ImNscy0xIiBkPSJNMTM3LjE0LDQ1Ljc2YTM2Ljg2LDM2Ljg2LDAsMCwxLS40MSw1LjNIOTMuNTRjMS4zMSw2LjY3LDUuNTUsMTAuMTIsMTEuODcsMTAuMTIsNC42OCwwLDguNTktMiwxMC41Mi01LjQ1aDE5LjkzQzEzMS43Nyw2OSwxMjAsNzcuMywxMDUuNDEsNzcuM2MtMTguMTUsMC0zMS41OS0xMy4yNi0zMS41OS0zMXMxMy4zNC0zMC45NCwzMS41OS0zMC45NGMxOC43NSwwLDMxLjczLDEzLjM4LDMxLjczLDMwLjQzWk05My44OSw0MGgyMy41MWMtMS43Ni01LjgxLTUuOTUtOC45NS0xMi04Ljk1Qzk5LjU3LDMxLDk1LjUxLDM0LjIsOTMuODksNDBaIi8+PHBhdGggY2xhc3M9ImNscy0xIiBkPSJNMjA4LjE2LDE3LjE3Vjc1LjMzSDE5Mi42N0wxOTEsNzEuMjJhMjcuNTQsMjcuNTQsMCwwLDEtMTcuNTUsNi4wOGMtMTcuNzgsMC0zMC41LTEzLTMwLjUtMzEuMDVzMTIuNzEtMzAuODMsMzAuNS0zMC44M2EyNy41OSwyNy41OSwwLDAsMSwxNy43Nyw2LjI1bDItNC41Wk0xODkuMTEsNDYuM2MwLTcuNTUtNS41Mi0xMy4yMi0xMy0xMy4yMnMtMTMsNS42Ny0xMywxMy4yMiw1LjUyLDEzLjI3LDEzLDEzLjI3UzE4OS4xMSw1My44OSwxODkuMTEsNDYuM1oiLz48cGF0aCBjbGFzcz0iY2xzLTEiIGQ9Ik0yNTguNjYsMTdWMzUuMjloLTcuNzhjLTcuMDksMC0xMC4wNiwzLjExLTEwLjA2LDEwLjUzVjc1LjM5aC0yMFYxNy4yM2gxMy40MmwzLjEzLDYuNDVjNC4xLTQuNzQsOS4xLTYuNzMsMTYtNi43M1oiLz48cGF0aCBjbGFzcz0iY2xzLTEiIGQ9Ik0zMDYuNDMsNTcuODVWNzUuMzlIMjkyLjY1Yy0xMi44NiwwLTIwLjY4LTcuODYtMjAuNjgtMjAuNzlWMzIuNjZIMjYxLjMyVjI4TDI4Ny4zOC4yM2g0LjEydjE3aDE0LjYxVjMyLjY2SDI5MlY1MS4xN2MwLDQuMTcsMi41MSw2LjY4LDYuNzQsNi42OFoiLz48cGF0aCBjbGFzcz0iY2xzLTEiIGQ9Ik0zNzUuMTgsNDUuNzZhMzYuODYsMzYuODYsMCwwLDEtLjQxLDUuM0gzMzEuNTdjMS4zMSw2LjY3LDUuNTUsMTAuMTIsMTEuODcsMTAuMTIsNC42OCwwLDguNTktMiwxMC41Mi01LjQ1SDM3My45QzM2OS44MSw2OSwzNTgsNzcuMywzNDMuNDUsNzcuM2MtMTguMTUsMC0zMS41OS0xMy4yNi0zMS41OS0zMXMxMy4zNC0zMC45NCwzMS41OS0zMC45NGMxOC43NSwwLDMxLjczLDEzLjM4LDMxLjczLDMwLjQzWk0zMzEuOTMsNDBoMjMuNTFjLTEuNzYtNS44MS01Ljk1LTguOTUtMTItOC45NUMzMzcuNjEsMzEsMzMzLjU1LDM0LjIsMzMxLjkzLDQwWiIvPjxwYXRoIGNsYXNzPSJjbHMtMSIgZD0iTTQxNS45NCw3NS4zOWwtOS41Ni0xNy4yMy05Ljc1LDE3LjIzSDM3NC41bDE5LjI5LTMwLjE4LTE3Ljg4LTI4SDM5OGw4LjQsMTQuNjUsOC43OS0xNC42NUg0MzdMNDE5LDQ0LjdsMTkuMzMsMzAuN1oiLz48L3N2Zz4=) no-repeat center;
    opacity: 0.85;
}

.logo-small {

}

.top-margin-none {
    margin-top: 0;
}

.upload-row-error {
    margin-top: 0.5%;
}

.upload-row-error .code {
    display: block;
    font-size: 85%;
}

.upload-row-error * {
    display: inline-block;
}

.help-button, .settings-button {
    padding: 0.80571429em 0.7em 0.80571429em !important;
}

/* --- fomantic rating bug fix in editor --- */
.ui .rating i:before {
    content: "\f005";
    color: #111;
    opacity: 0.2;
}

.ui .rating i.active:before {
    opacity: 1.0;
}

.ui .rating i.selected:before {
    opacity: 1.0;
}

/* editor width */
div[class^='App_editor'], div[class*=' App_editor'] {
    width: 100%;
}

/* create and edit_config pages */
.editor-iframe {
    display: none;
    width: 100%;
    overflow: hidden !important;
    border: none;
}

/* --- Task explorer --- */
#source-data-pre {
    /* word breaks to correct presentation */
    white-space: pre-wrap;
    white-space: -moz-pre-wrap;
    white-space: -pre-wrap;
    white-space: -o-pre-wrap;
    word-wrap: break-word;
}

/* --- Cards --- */
.upper-right {
    position: absolute;
    top: 10px;
    right: 5px;
}

.no-border {
    padding: 0;
    border: none;
    background: none;
}

/* --- Mobile Menu --- */
.top-sidebar {
    display: none;
}

.right-part {
    background: #e8e8e8;
    margin-left: 230px;
}

/* --- Plots --- */
.plot-instance {
    width: 100%;
}

.settings-slider {
    padding-top: 0 !important;
    width: 20% !important;
    min-width: 500px !important;
}

@media only screen and (max-width: 768px) {
    .main {
        margin-right: 0;
    }

    .dashboard-title {
        display: none;
    }

    .right-part {
        background: #e8e8e8;
        margin-left: 0;
    }

    button.undo, button.redo, button.reset {
        display: none !important;
        visibility: hidden;
    }

    .pusher {
        left: 0 !important;
        -webkit-transform: none !important;
        transform: none !important;
        min-width: unset !important;
    }

    body.pushable {
        background: white !important;
    }

    main.main {
        width: 100%;
        padding-left: 1rem !important;
        padding-right: 1rem !important;
    }

    .top-sidebar {
        display: block;
        background-color: #302b3c !important;
    }

    .hide-mobile {
        display: none !important;
    }

    .rmenu {
        padding-top: 0px !important;
        /* margin-top: 0px !important; */
        margin-right: 1rem !important;
        margin-top: -25px !important;
        padding-top: -20px !important;
        display: inline-flex !important;
        top: -10px !important;
        height: 0px !important;
    }

    /* --- Plots --- */
    .plot-instance {
        max-width: 100%;
    }

    .settings-slider {
        padding-top: 0 !important;
        width: 20% !important;
        min-width: 100% !important;
    }
}

/* --- Mobile Menu End --- */

/* --- Stripe --- */
.StripeElement {
    box-sizing: border-box;

    height: 40px;

    padding: 10px 12px;
    margin: 1em 0;

    border: 1px solid transparent;
    border-radius: 4px;
    background-color: #f7f7f7;

    box-shadow: 1px 1px 3px 1px #e6ebf1;
    -webkit-transition: box-shadow 150ms ease;
    transition: box-shadow 150ms ease;
}

.StripeElement--focus {
    box-shadow: 0 1px 3px 0 #cfd7df;
}

.StripeElement--invalid {
    border-color: #fa755a;
}

.StripeElement--webkit-autofill {
    background-color: #fefde5 !important;
}

/*--- Stripe End --- */

/*--- Others ---*/
.private-icon {
    font-size: 90% !important;
    padding-top: 5px;
    color: #555;
    margin-right: 0;
}

.ui.visible.left.sidebar ~ .pusher {
    -webkit-transform: translate3d(230px, 0, 0) !important;
    transform: translate3d(230px, 0, 0) !important;
}

/* fix cards headers with long titles */
.ui.card .project-card-header {
    word-break: break-all;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.ui.card .project-card-title {
    color: #444;
    align-self: flex-start;
}

.ui.card .project-card-buttons {
    margin-left: auto;
    display: inline-flex;
}

.ui.card .project-button {
    color: #444 !important;
}

.ui.card .project-button:hover {
    color: #4183c4 !important;
}
.card-header {
    background: #fafafa !important;
}

hotkey {
    display: inline-block;
    border: 1px solid #ccc;
    border-radius: 4px;
    padding: 0.1em 0.5em;
    margin: 0 0.2em;
    box-shadow: 0 1px 0 rgba(0, 0, 0, 0.2), 0 0 0 2px #fff inset;
    background-color: #f7f7f7;
}
hotkey i {
    color: #777;
}
.hotkey-helper {
    margin-bottom: 1.0em;
}
.hotkey-helper .keys {
    min-width: 15em;
    display: inline-block;
}
.hotkey-helper .description {
    margin-left: 1.5em;
    display: inline-block;
}

.whats-new-date {
    display: block;
    font-size: 0.75em;
    color: #505050;
    margin-left: 2.2em;
    margin-top: 0.2em;
}

/* semantic ui pretty */

.ui .button {
    font-weight: 400 !important;
}
.ui .button.primary {
    background: rgb(24, 144, 255);
    border-color: rgb(24, 144, 255);
    color: #fff;
}
.ui .button.primary:hover {
    background: rgb(24, 156, 255);
    border-color: rgb(24, 156, 255);
    color: #fff;
}
.ui .button.primary:focus {
    background: rgb(13, 154, 255);
    border-color: rgb(24, 156, 255);
    color: #fff;
}
.ui .button.positive {
    background: #25c440;
    border-color: #25c440;
}
.ui .button {
    transition: 0.3s;
}
.ui .button:hover {
    transition: 0.3s;
}
.ui .message, .ui.message {
    background: none !important;
    box-shadow: 0 0 0 1px rgba(34, 36, 38, .15) !important;
    color: #555;
}
.ui .message .content, .ui.message .content {
    margin-top: 0.7em;
}
.ui .message .category .icon {
    color: #777;
}
.ui .placeholder {
    font-weight: lighter !important;
}
.ui.placeholder.segment {
    background: #fafafa !important;
    -webkit-box-shadow: none;
    box-shadow: none;
}
.ui .icon {
    font-weight: lighter;
}
.ui .statistic .label {
    font-weight: normal !important;
}
.ui .statistic .value {
    font-weight: lighter !important;
    color: #444 !important;
}
.ui .divider.section {
    margin-top: 2em;
    margin-bottom: 1.1em;
    box-sizing: content-box !important;
    text-transform: none !important;
    font-size: 1.25;
    font-weight: normal;
}
.ui form p:first-of-type, .ui .form p:first-of-type {
    margin-top: 0 !important;
}
.ui form label, .ui .form label {
    font-size: 1em !important;
    font-weight: normal !important;
    color: #555 !important;
}
.ui form .field label, .ui .form .field label {
    font-size: 0.95em !important;
    font-weight: normal !important;
    color: #555 !important;
}
.ui form p, .ui .form p {
    color: rgb(85, 85, 85);
}

/* file upload button */
.ui form .field input[type=file], .ui .form .field input[type=file] {
    border: none;
    padding-left: 0;
}
.ui form .field input[type=file]::-webkit-file-upload-button,
.ui .form .field input[type=file]::-webkit-file-upload-button{
  visibility: hidden;
}
.ui form .field input[type=file]::before,
.ui .form .field input[type=file]::before{
  content: 'Choose file';
  display: inline-block;
  background: none;
  border: 1px solid #999;
  border-radius: 3px;
  padding: 5px 8px;
  outline: none;
  white-space: nowrap;
  -webkit-user-select: none;
  cursor: pointer;
  text-shadow: 1px 1px #fff;
  font-weight: 400;
  font-size: 1em;
}
.ui form .field input[type=file]:hover::before,
.ui .form .field input[type=file]:hover::before{
  border-color: #333;
}
.ui form .field input[type=file]:active::before,
.ui .form .field input[type=file]:active::before{
  background: #e3e3e3;
}

.ui .label {
    font-weight: lighter;
    opacity: 1.0;
}
.ui .label .detail {
    font-weight: normal;
    opacity: 0.9;
}
.ui .basic.labels  {
    opacity: 0.8 !important;
}
.ui.dropdown .menu>.header {
    font-weight: 400;
}

.ui.placeholder.segment {
  font-weight: lighter !important;
    color:#777;
}
.ui.placeholder .icon.header {
    color:#444;
}

.ui.segment .header {
    font-weight: lighter !important;
    font-size: 1.5em;
}

.ui .dividing.header {
    margin-bottom: 1em !important;
    color: red;
}

.ui.modal>.header {
    font-weight: normal !important;
}

.ui.accordion .title {
    font-size: 1.1em !important;
}

.ui.card, .ui.cards>.card {
    box-shadow: 0 0 2px 0 #d1d1d1, 0 0 0 1px #e1e1e1;
}

.ui.toggle.checkbox input:checked~.box:before, .ui.toggle.checkbox input:checked~label:before {
    background-color: rgb(24, 144, 255)!important;
}

.small-breadcrumb {
    padding-top: 2px;
    font-size: 0.5em;
    color: #AAA;
    line-height: 1.2em;
}

.settings-padded {
    padding: 1em 1em 1em 1em;
}

.darkgrey {
    color: #555 !important;
}

.ui.labeled.icon.button > .icon, .ui.labeled.icon.buttons > .button > .icon{
    background-color: unset;
}
.ui.labeled.icon.button, .ui.labeled.icon.buttons .button {
    padding-left: 3em !important;
}
.ui.labeled.icon.button > i, .ui.labeled.icon.buttons .button > i {
    padding-left: 0.85em !important;
}
.ui[class*="right labeled"].icon.button {
    padding-right: 3em !important;
    padding-left: 1.5em !important;
}
.ui[class*="right labeled"].icon.button i {
    padding-right: 2.25em !important;
}

/* to separate buttons in label-stream */
.breadcrumbs-menu .ui.icon.button, .breadcrumbs-menu .ui.icon.buttons .button {
    margin-bottom: 0.5em;
}

/* pagination */
.page_select {
    cursor: pointer;
    border: none;
    background: none;
    min-width: 20px;
    text-align: center
}

.page_size {
    cursor: pointer;
    background: none;
    box-shadow: none;
    outline: none;
    border: none;
}

.pagination {
    height: 25px;
    box-shadow: none;
}

.people_list_roles {
    list-style: none;
    padding: 0 1em;
    color: #222;
}
.people_list_roles li {
    margin: 0.5em 0;
}
.people_list_roles li .role_span {
    font-size: 1.1em;
    font-weight: bold;
}
.people_list_roles li .desc_span {
    padding: 1em 1em;
}

.ui.statistic > .value, .ui.statistics .statistic > .value {
    font-size: 3rem !important;
}

.button.light-blue {
    background: rgb(77, 172, 255) !important;
    border-color: rgb(24, 144, 255) !important;
    color: #fff !important;
}
.button.light-blue:hover {
    background: rgb(50, 149, 230) !important;
}

.ant-modal-content {
    background-color: var(--color-neutral-background);
}
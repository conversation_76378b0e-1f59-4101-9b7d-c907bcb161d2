html, body {
  height: 100%;
}

.login_page_new_ui {
  display: flex;
  font-weight: 400;
  height: 100%;
  font-family: 'Hellix', sans-serif;
  background-color: var(--color-neutral-background-bold);
  color: var(--color-neutral-content);
}

.login_page_new_ui .left {
  flex: 1;
  background: linear-gradient(109.47deg, rgba(255, 166, 99, 0.15) 0%, rgba(255, 117, 87, 0.15) 51.56%, rgba(227, 123, 211, 0.15) 100%);
  height: 100%;
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.login_page_new_ui .left:after {
  content: '';
  position: absolute;
  bottom: 0;
  right: 0;
  width: 179px;
  height: 239px;
  background: url('../images/login-bg.svg') no-repeat center center;
  background-size: contain;
}

.login_page_new_ui .ls-logo {
  width: 414px;
  height: 64px;
  color: var(--color-neutral-content);
}

.login_page_new_ui .hs-logo {
  width: 152px;
  height: 22px;
  color: var(--color-neutral-content);
}

.login_page_new_ui .left h2 {
  font-size: 24px;
  font-weight: 500;
  line-height: 32px;
  letter-spacing: 0em;
  text-align: center;
  margin: 0;
}

.login_page_new_ui .left h3 {
  font-size: 16px;
  font-weight: 400;
  line-height: 24px;
  text-align: center;
  margin: 8px 0 0 0;
}

.login_page_new_ui .left .by {
  position: absolute;
  bottom: 60px;
  left: 60px;
  display: flex;
  flex-direction: column;
  font-size: .75rem;
  font-style: normal;
  font-weight: 400;
  line-height: 28px;
}

.login_page_new_ui .left img {
  color: var(--color-neutral-content);
}

.login_page_new_ui .tips {
  max-width: 520px;
  border-radius: 8px;
  border: 1px solid var(--color-neutral-border);
  background: var(--color-neutral-surface);
  box-shadow: 0 2px 6px 0 rgb(var(--color-neutral-shadow) / 20%);
  padding: 16px;
  margin:18vh 60px 0 60px;
  position: relative;
  z-index:3;
}

.login_page_new_ui .tips:after {
  content: '';
  width: 115px;
  height: 83px;
  position: absolute;
  bottom: -72px;
  left: 16px;
  fill: var(--color-neutral-border);
  background: url('../images/heidi-tip.svg') no-repeat center center;
}

.login_page_new_ui .tips .title {
  font-size: 16px;
  font-style: normal;
  font-weight: bold;
  line-height: 24px; 
  letter-spacing: 0.15px;
  margin-bottom: 8px;
}

.login_page_new_ui .tips .description {
  font-size: .875rem;
  font-style: normal;
  font-weight: 400;
  line-height: 20px;
  letter-spacing: 0.25px;
}

.login_page_new_ui .tips .description a {
  color: var(--color-primary-content);
  font-size: .875rem;
  font-style: normal;
  font-weight: 500;
  line-height: 20px;
  text-decoration: underline;
}

.login_page_new_ui .tips .description a:hover {
  text-decoration: none;
}

.login_page_new_ui .right {
  display: flex;
  flex-direction: column;
  flex: 1;
  flex-wrap: wrap;
  justify-content: center;
  align-items: center;
  height: 100%;
}

.login_page_new_ui .toggle {
  padding: 0;
  list-style: none;
  display: flex;
  justify-content: stretch;
}

.login_page_new_ui .toggle > * {
  flex-grow: 1;
  text-align: center;
  padding: .75rem 0;
  font-size: 1rem;
  color: var(--color-neutral-border);
  border-top: 2px solid transparent;
  transition: all 150ms ease-out;
}
.login_page_new_ui .toggle > *.active {
  border-color: var(--color-primary-surface); 
  color: var(--color-primary-content);
}
.login_page_new_ui .toggle > *:hover {
  color: var(--color-primary-surface-hover);
  border-color: var(--color-primary-surface-hover);
}
.form-wrapper{
  position: relative;
  background: var(--color-neutral-surface);
  margin-top: 1.5rem;
  border: 1px solid var(--color-neutral-border);
  padding: 2rem;
  border-radius: 1rem;
  box-shadow: 0px 0px 12px rgba(23, 20, 13, 0.16);
  width: 22rem;
}

.form-wrapper h2{
  font-size: 2rem;
  font-weight: 500;
  margin: 0 0 1rem 0;
  width: 100;
  text-align: center;
}

.login_page_new_ui form {
  display: grid;
  flex-direction: column;
  grid-auto-flow: rows;
  grid-gap: 1rem;
}
.login_page_new_ui form > * {
  position: relative;
}
.login_page_new_ui form p {
  margin: 0;
}
.login_page_new_ui form .error {
  color: var(--color-negative-content);
  font-size: .875rem;
}
.login_page_new_ui form input,
.login_page_new_ui form button,
.login_page_new_ui form select {
  width: 100%;
  font-family: 'Hellix', sans-serif;
}

.login_page_new_ui form .lsf-button-ls.lsf-button-ls_look_primary {
  --button-color: var(--color-neutral-inverted-content);
  border-radius: 4rem;
  background: var(--color-neutral-inverted-surface);
}

.login_page_new_ui .field_errors {
  margin: 0;
  padding: 0;
  list-style: none;
  text-align: left;
  font-size: .875rem;
  line-height: 16px;
  color: var(--color-negative-content);
}
.login_page_new_ui .field_errors li {
  margin-top: .25rem;
}

.auto-generated form {
  width: 100%;
}
.auto-generated form label {
  display: block;
}
.auto-generated form input {
  height: 40px;
  min-height: 40px;
  background: var(--color-neutral-surface);
  font-size: 1rem;
  line-height: 22px;
  border: 1px solid var(--color-neutral-border);
  box-sizing: border-box;
  border-radius: 5px;
  padding: 0 16px;
  transition: box-shadow 80ms ease;
}

.auto-generated p:empty {
  display: none;
}
.auto-generated form ul li:not(:last-child) {
  margin-bottom: 0.5em;
}
.auto-generated form ul {
  margin-bottom: 0;
}

.input-wrapper label{
  font-size: .825rem !important;
  margin-bottom: .2rem;
  font-family: 'Hellix', sans-serif !important;
  font-weight: 500;
}

label{
  font-family: 'Hellix', sans-serif !important;
  font-weight: 500;
  font-size: .825rem !important;
  margin-bottom: .2rem;
  display: block;
}

.form-group {
  display: block;
  margin-bottom: .5rem;
}

.form-group input {
  padding: 0;
  height: initial;
  width: initial;
  margin-bottom: 0;
  display: none;
  cursor: pointer;
}

.form-group label {
  position: relative;
  cursor: pointer;
  font-size: .875rem;
}

.form-group label a{
  font-family: 'Hellix', sans-serif !important;
  font-weight: 500;
}

.form-group label:before {
  content:'';
  -webkit-appearance: none;
  background-color: transparent;
  border: 1px solid #CAC5B8;
  padding: 10px;
  border-radius: 4px;
  display: inline-block;
  position: relative;
  vertical-align: middle;
  cursor: pointer;
  margin-right: 5px;
  top: -1px;
}

.form-group input:checked + label:before {
  background: var(--primary_link);
  border: 1px solid var(--primary_link);
}

.form-group input:checked + label:after {
  content: '';
  display: block;
  position: absolute;
  top: 2px;
  left: 8px;
  width: 5px;
  height: 10px;
  border: solid var(--color-primary-surface-content);
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

.text-wrapper{
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: .875rem;
}

.text-wrapper a{
  color: var(--primary_link);
  text-decoration: underline;
  font-weight: 500;
}

.text-wrapper a:hover{
  text-decoration: none;
}
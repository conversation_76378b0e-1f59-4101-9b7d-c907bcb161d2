# Label Studio Chinese Translation
# Copyright (C) 2025 Label Studio
# This file is distributed under the same license as the Label Studio package.
# Chinese Translation Team, 2025.
#
#: webhooks/models.py:116 webhooks/models.py:125 webhooks/models.py:134
#: webhooks/models.py:143 webhooks/models.py:152 webhooks/models.py:161
#: webhooks/models.py:177 webhooks/models.py:193 webhooks/models.py:209
#: webhooks/models.py:218 webhooks/models.py:227 webhooks/models.py:243
msgid ""
msgstr ""
"Project-Id-Version: Label Studio 1.20.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-05-28 17:25+0800\n"
"PO-Revision-Date: 2025-05-28 17:30+0800\n"
"Last-Translator: Chinese Translation Team\n"
"Language-Team: Chinese (Simplified) <<EMAIL>>\n"
"Language: zh_<PERSON>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=1; plural=0;\n"
#: data_export/models.py:31 data_export/models.py:189 projects/models.py:1429
#: projects/models.py:1462
msgid "Created"
msgstr "已创建"

#: data_export/models.py:32 data_export/models.py:190
#: io_storages/base_models.py:49 projects/models.py:1430
#: projects/models.py:1463
msgid "In progress"
msgstr "进行中"

#: data_export/models.py:33 data_export/models.py:191
#: io_storages/base_models.py:50 projects/models.py:1431
#: projects/models.py:1464
msgid "Failed"
msgstr "失败"

#: data_export/models.py:34 data_export/models.py:192
#: io_storages/base_models.py:51 projects/models.py:1432
#: projects/models.py:1465 users/product_tours/models.py:10
msgid "Completed"
msgstr "已完成"

#: data_export/models.py:37 io_storages/base_models.py:216 ml/models.py:62
#: ml_models/models.py:32 ml_models/models.py:64 projects/models.py:125
#: projects/models.py:1123
msgid "title"
msgstr "标题"

#: data_export/models.py:43 data_export/models.py:224
#: io_storages/base_models.py:218 io_storages/base_models.py:705
#: io_storages/base_models.py:733 jwt_auth/models.py:35 ml/models.py:123
#: ml/models.py:440 ml/models.py:454 ml_model_providers/models.py:75
#: ml_models/models.py:40 ml_models/models.py:110 ml_models/models.py:179
#: organizations/models.py:28 organizations/models.py:93 projects/models.py:247
#: projects/models.py:1127 projects/models.py:1142 projects/models.py:1171
#: projects/models.py:1178 projects/models.py:1442 tasks/models.py:82
#: tasks/models.py:632 tasks/models.py:814 tasks/models.py:861
#: tasks/models.py:930 tasks/models.py:1115 webhooks/models.py:65
msgid "created at"
msgstr "创建时间"

#: data_export/models.py:52
msgid "md5 of file"
msgstr "文件MD5"

#: data_export/models.py:57 data_export/models.py:236 projects/models.py:1444
msgid "finished at"
msgstr "完成时间"

#: data_export/models.py:64
msgid "Export status"
msgstr "导出状态"

#: data_export/models.py:70
msgid "Exporting meta data"
msgstr "导出元数据"

#: data_export/models.py:83 data_export/models.py:246 projects/models.py:202
msgid "created by"
msgstr "创建者"

msgid "Log in"
msgstr "登录"

msgid "Email Address"
msgstr "邮箱地址"

msgid "Password"
msgstr "密码"

msgid "Keep me logged in this browser"
msgstr "在此浏览器中保持登录状态"

msgid "Don't have an account?"
msgstr "还没有账户？"

msgid "Sign up"
msgstr "注册"

msgid "A full-fledged open source solution for data labeling"
msgstr "功能齐全的开源数据标注解决方案"

msgid "New Feature!"
msgstr "新功能！"

msgid "You can now label multi-page documents like PDFs."
msgstr "您现在可以标注多页文档，如PDF。"

msgid "Learn more"
msgstr "了解更多"

msgid "Brought to you by"
msgstr "由以下公司提供"

#: data_export/models.py:230 io_storages/base_models.py:734
#: jwt_auth/models.py:36 ml/models.py:124 ml/models.py:441 ml/models.py:455
#: ml_model_providers/models.py:77 ml_models/models.py:42
#: ml_models/models.py:112 organizations/models.py:29
#: organizations/models.py:94 projects/models.py:248 projects/models.py:1128
#: projects/models.py:1143 projects/models.py:1172 projects/models.py:1443
#: tasks/models.py:83 tasks/models.py:633 tasks/models.py:862
#: tasks/models.py:931 webhooks/models.py:66
msgid "updated at"
msgstr ""

#: data_manager/models.py:10
msgid "data"
msgstr ""

#: data_manager/models.py:11
msgid "ordering"
msgstr ""

#: data_manager/models.py:13
msgid "order"
msgstr ""

#: data_manager/models.py:18
msgid "selected items"
msgstr ""

#: data_manager/models.py:82
msgid "conjunction"
msgstr ""

#: data_manager/models.py:89
msgid "index"
msgstr ""

#: data_manager/models.py:90
msgid "column"
msgstr ""

#: data_manager/models.py:91
msgid "type"
msgstr ""

#: data_manager/models.py:92
msgid "operator"
msgstr ""

#: data_manager/models.py:93
msgid "value"
msgstr ""

#: io_storages/azure_blob/models.py:38
msgid "container"
msgstr ""

#: io_storages/azure_blob/models.py:39 io_storages/gcs/models.py:34
#: io_storages/s3/models.py:45
msgid "prefix"
msgstr ""

#: io_storages/azure_blob/models.py:41 io_storages/gcs/models.py:36
#: io_storages/localfiles/models.py:32 io_storages/s3/models.py:47
msgid "regex_filter"
msgstr ""

#: io_storages/azure_blob/models.py:44 io_storages/gcs/models.py:42
#: io_storages/localfiles/models.py:38 io_storages/redis/models.py:37
#: io_storages/s3/models.py:53
msgid "use_blob_urls"
msgstr ""

#: io_storages/azure_blob/models.py:46
msgid "account_name"
msgstr ""

#: io_storages/azure_blob/models.py:47
msgid "account_key"
msgstr ""

#: io_storages/azure_blob/models.py:191 io_storages/gcs/models.py:169
#: io_storages/s3/models.py:185
msgid "presign"
msgstr ""

#: io_storages/azure_blob/models.py:193 io_storages/gcs/models.py:171
#: io_storages/s3/models.py:187
msgid "presign_ttl"
msgstr ""

#: io_storages/base_models.py:47
msgid "Initialized"
msgstr ""

#: io_storages/base_models.py:48
msgid "Queued"
msgstr ""

#: io_storages/base_models.py:56
msgid "last sync"
msgstr ""

#: io_storages/base_models.py:58
msgid "last sync count"
msgstr ""

#: io_storages/base_models.py:61
msgid "last_sync_job"
msgstr ""

#: io_storages/base_models.py:217 ml/models.py:92 ml_models/models.py:34
#: projects/models.py:137 projects/models.py:1124
msgid "description"
msgstr ""

#: io_storages/base_models.py:220
msgid "synchronizable"
msgstr ""

#: io_storages/base_models.py:596
msgid "can_delete_objects"
msgstr ""

#: io_storages/base_models.py:698
msgid "key"
msgstr ""

#: io_storages/base_models.py:702 io_storages/base_models.py:731
msgid "object exists"
msgstr ""

#: io_storages/gcs/models.py:33 io_storages/s3/models.py:44
msgid "bucket"
msgstr ""

#: io_storages/gcs/models.py:47
msgid "google_application_credentials"
msgstr ""

#: io_storages/gcs/models.py:52
msgid "Google Project ID"
msgstr ""

#: io_storages/localfiles/models.py:30 io_storages/redis/models.py:26
msgid "path"
msgstr ""

#: io_storages/redis/models.py:27
msgid "host"
msgstr ""

#: io_storages/redis/models.py:28 io_storages/redis/models.py:31
msgid "port"
msgstr ""

#: io_storages/redis/models.py:29
msgid "password"
msgstr ""

#: io_storages/redis/models.py:82 io_storages/redis/models.py:138
msgid "db"
msgstr ""

#: io_storages/s3/models.py:57
msgid "aws_access_key_id"
msgstr ""

#: io_storages/s3/models.py:59
msgid "aws_secret_access_key"
msgstr ""

#: io_storages/s3/models.py:64
msgid "aws_session_token"
msgstr ""

#: io_storages/s3/models.py:66
msgid "aws_sse_kms_key_id"
msgstr ""

#: io_storages/s3/models.py:68
msgid "region_name"
msgstr ""

#: io_storages/s3/models.py:69
msgid "s3_endpoint"
msgstr ""

#: io_storages/s3/models.py:190
msgid "recursive scan"
msgstr ""

#: io_storages/s3/models.py:192
msgid "Perform recursive scan over the bucket content"
msgstr ""

#: jwt_auth/models.py:20
msgid "JWT API tokens enabled"
msgstr ""

#: jwt_auth/models.py:25
msgid "JWT API token time to live (days)"
msgstr ""

#: jwt_auth/models.py:30
msgid "legacy API tokens enabled"
msgstr ""

#: labels_manager/models.py:7
msgid "Created at"
msgstr ""

#: labels_manager/models.py:8
msgid "Updated at"
msgstr ""

#: labels_manager/models.py:13
msgid "Title"
msgstr ""

#: labels_manager/models.py:14
msgid "Description"
msgstr ""

#: labels_manager/models.py:36
msgid "Tag name"
msgstr ""

#: ml/models.py:26
msgid "Connected"
msgstr ""

#: ml/models.py:27
msgid "Disconnected"
msgstr ""

#: ml/models.py:28
msgid "Error"
msgstr ""

#: ml/models.py:29
msgid "Training"
msgstr ""

#: ml/models.py:30
msgid "Predicting"
msgstr ""

#: ml/models.py:34
msgid "None"
msgstr ""

#: ml/models.py:35
msgid "Basic Auth"
msgstr ""

#: ml/models.py:47
msgid "is_interactive"
msgstr ""

#: ml/models.py:52
msgid "url"
msgstr ""

#: ml/models.py:56
msgid "error_message"
msgstr ""

#: ml/models.py:76
msgid "basic auth user"
msgstr ""

#: ml/models.py:84
msgid "basic auth password"
msgstr ""

#: ml/models.py:100
msgid "extra params"
msgstr ""

#: ml/models.py:106 ml/models.py:434 ml/models.py:449 projects/models.py:236
#: tasks/models.py:893 tasks/models.py:1098
msgid "model version"
msgstr ""

#: ml/models.py:113
msgid "timeout"
msgstr ""

#: ml/models.py:126
msgid "auto_update"
msgstr ""

#: ml/models.py:437
msgid "batch size"
msgstr ""

#: ml_model_providers/models.py:12
msgid "OpenAI"
msgstr ""

#: ml_model_providers/models.py:13
msgid "AzureOpenAI"
msgstr ""

#: ml_model_providers/models.py:14
msgid "AzureAIFoundry"
msgstr ""

#: ml_model_providers/models.py:15
msgid "VertexAI"
msgstr ""

#: ml_model_providers/models.py:16
msgid "Gemini"
msgstr ""

#: ml_model_providers/models.py:17
msgid "Anthropic"
msgstr ""

#: ml_model_providers/models.py:18
msgid "Custom"
msgstr ""

#: ml_model_providers/models.py:22
msgid "Organization"
msgstr ""

#: ml_model_providers/models.py:23
msgid "User"
msgstr ""

#: ml_model_providers/models.py:24
msgid "Model"
msgstr ""

#: ml_model_providers/models.py:31
msgid "api_key"
msgstr ""

#: ml_model_providers/models.py:33
msgid "auth_token"
msgstr ""

#: ml_model_providers/models.py:40
msgid "google application credentials"
msgstr ""

#: ml_model_providers/models.py:47
msgid "google project id"
msgstr ""

#: ml_model_providers/models.py:51
msgid "google location"
msgstr ""

#: ml_model_providers/models.py:80
msgid "is_internal"
msgstr ""

#: ml_model_providers/models.py:88
msgid "budget_limit"
msgstr ""

#: ml_model_providers/models.py:96
msgid "budget_last_reset_date"
msgstr ""

#: ml_model_providers/models.py:104
msgid "budget_reset_period"
msgstr ""

#: ml_model_providers/models.py:117
msgid "budget_total_spent"
msgstr ""

#: ml_model_providers/models.py:125
msgid "budget_alert_threshold"
msgstr ""

#: ml_models/models.py:18
msgid "TextClassification"
msgstr ""

#: ml_models/models.py:19
msgid "NamedEntityRecognition"
msgstr ""

#: ml_models/models.py:68
msgid "prompt"
msgstr ""

#: ml_models/models.py:131
msgid "All"
msgstr ""

#: ml_models/models.py:132
msgid "HasGT"
msgstr ""

#: ml_models/models.py:133
msgid "Sample"
msgstr ""

#: ml_models/models.py:136
msgid "Input"
msgstr ""

#: ml_models/models.py:137
msgid "Output"
msgstr ""

#: ml_models/models.py:140
msgid "Pending"
msgstr ""

#: ml_models/models.py:141
msgid "InProgress"
msgstr ""

#: ml_models/models.py:173
msgid "total predictions"
msgstr ""

#: ml_models/models.py:175
msgid "total correct predictions"
msgstr ""

#: ml_models/models.py:177
msgid "total tasks"
msgstr ""

#: ml_models/models.py:181
msgid "triggered at"
msgstr ""

#: ml_models/models.py:183
msgid "predictions updated at"
msgstr ""

#: ml_models/models.py:185
msgid "completed at"
msgstr ""

#: organizations/models.py:32
msgid "deleted at"
msgstr ""

#: organizations/models.py:79
msgid "organization title"
msgstr ""

#: organizations/models.py:81 projects/models.py:191
msgid "token"
msgstr ""

#: organizations/models.py:90
msgid "created_by"
msgstr ""

#: organizations/models.py:96
msgid "contact info"
msgstr ""

#: projects/models.py:144
msgid "label config"
msgstr ""

#: projects/models.py:151
msgid "parsed label config"
msgstr ""

#: projects/models.py:159
msgid "expert instruction"
msgstr ""

#: projects/models.py:162
msgid "show instruction"
msgstr ""

#: projects/models.py:166
msgid "show skip button"
msgstr ""

#: projects/models.py:171
msgid "enable empty annotation"
msgstr ""

#: projects/models.py:175
msgid "reveal_preannotations_interactively"
msgstr ""

#: projects/models.py:178
msgid "show annotation history"
msgstr ""

#: projects/models.py:181
msgid "show predictions to annotator"
msgstr ""

#: projects/models.py:187
msgid "evaluate predictions automatically"
msgstr ""

#: projects/models.py:193 tasks/models.py:649
msgid "result count"
msgstr ""

#: projects/models.py:195
msgid "color"
msgstr ""

#: projects/models.py:205
msgid "maximum annotation number"
msgstr ""

#: projects/models.py:212
msgid "min_annotations_to_start_training"
msgstr ""

#: projects/models.py:218
msgid "control weights"
msgstr ""

#: projects/models.py:239
msgid "data_types"
msgstr ""

#: projects/models.py:242
msgid "is draft"
msgstr ""

#: projects/models.py:245
msgid "published"
msgstr ""

#: projects/models.py:264
msgid "show ground truth first"
msgstr ""

#: projects/models.py:265
msgid "show overlap first"
msgstr ""

#: projects/models.py:266
msgid "overlap_cohort_percentage"
msgstr ""

#: projects/models.py:269
msgid "task_data_login"
msgstr ""

#: projects/models.py:272
msgid "task_data_password"
msgstr ""

#: projects/models.py:275
msgid "pinned at"
msgstr ""

#: projects/models.py:278
msgid "custom_task_lock_ttl"
msgstr ""

#: projects/models.py:1097
#, python-format
msgid "Business number %d"
msgstr ""

#: projects/models.py:1182
msgid "all data columns"
msgstr ""

#: projects/models.py:1186
msgid "common data columns"
msgstr ""

#: projects/models.py:1190
msgid "created annotations"
msgstr ""

#: projects/models.py:1196
msgid "created labels"
msgstr ""

#: projects/models.py:1198
msgid "created labels in drafts"
msgstr ""

#: tasks/choices.py:6
msgid "Created from prediction"
msgstr ""

#: tasks/choices.py:7
msgid "Created from another annotation"
msgstr ""

#: tasks/choices.py:8
msgid "Imported"
msgstr ""

#: tasks/choices.py:9
msgid "Submitted"
msgstr ""

#: tasks/choices.py:10
msgid "Updated"
msgstr ""

#: tasks/choices.py:11 users/product_tours/models.py:11
msgid "Skipped"
msgstr ""

#: tasks/choices.py:12
msgid "Accepted"
msgstr ""

#: tasks/choices.py:13
msgid "Rejected"
msgstr ""

#: tasks/choices.py:14
msgid "Fixed and accepted"
msgstr ""

#: tasks/choices.py:15
msgid "Deleted review"
msgstr ""

#: tasks/models.py:89 tasks/models.py:623
msgid "updated by"
msgstr ""

#: tasks/models.py:93
msgid "is_labeled"
msgstr ""

#: tasks/models.py:99
msgid "overlap"
msgstr ""

#: tasks/models.py:113
msgid "inner id"
msgstr ""

#: tasks/models.py:120
msgid "total_annotations"
msgstr ""

#: tasks/models.py:126
msgid "cancelled_annotations"
msgstr ""

#: tasks/models.py:132
msgid "total_predictions"
msgstr ""

#: tasks/models.py:139
msgid "comment count"
msgstr ""

#: tasks/models.py:145
msgid "unresolved comment count"
msgstr ""

#: tasks/models.py:158
msgid "last comment updated at"
msgstr ""

#: tasks/models.py:626
msgid "was cancelled"
msgstr ""

#: tasks/models.py:628
msgid "ground_truth"
msgstr ""

#: tasks/models.py:635
msgid "draft created at"
msgstr ""

#: tasks/models.py:638 tasks/models.py:820
msgid "lead time"
msgstr ""

#: tasks/models.py:644
msgid "prediction"
msgstr ""

#: tasks/models.py:674
msgid "last action"
msgstr ""

#: tasks/models.py:684
msgid "last created by"
msgstr ""

#: tasks/models.py:690
msgid "bulk created"
msgstr ""

#: tasks/models.py:806
msgid "expire_at"
msgstr ""

#: tasks/models.py:818
msgid "result"
msgstr ""

#: tasks/models.py:848
msgid "was postponed"
msgstr ""

#: tasks/models.py:891
msgid "score"
msgstr ""

#: tasks/models.py:915
msgid "cluster"
msgstr ""

#: tasks/models.py:926
msgid "mislabeling"
msgstr ""

#: tasks/models.py:1077
msgid "message"
msgstr ""

#: tasks/models.py:1084
msgid "error_type"
msgstr ""

#: tasks/models.py:1125
msgid "Reference to the associated prediction"
msgstr ""

#: tasks/models.py:1133
msgid "Reference to the associated failed prediction"
msgstr ""

#: tasks/models.py:1136
msgid "inference time"
msgstr ""

#: tasks/models.py:1136
msgid "Time taken for inference in seconds"
msgstr ""

#: tasks/models.py:1139
msgid "prompt tokens count"
msgstr ""

#: tasks/models.py:1139
msgid "Number of tokens in the prompt"
msgstr ""

#: tasks/models.py:1142
msgid "completion tokens count"
msgstr ""

#: tasks/models.py:1142
msgid "Number of tokens in the completion"
msgstr ""

#: tasks/models.py:1145
msgid "total tokens count"
msgstr ""

#: tasks/models.py:1145
msgid "Total number of tokens"
msgstr ""

#: tasks/models.py:1147
msgid "prompt cost"
msgstr ""

#: tasks/models.py:1147
msgid "Cost of the prompt"
msgstr ""

#: tasks/models.py:1149
msgid "completion cost"
msgstr ""

#: tasks/models.py:1149
msgid "Cost of the completion"
msgstr ""

#: tasks/models.py:1151
msgid "total cost"
msgstr ""

#: tasks/models.py:1151
msgid "Total cost"
msgstr ""

#: tasks/models.py:1152
msgid "extra"
msgstr ""

#: tasks/models.py:1152
msgid "Additional metadata in JSON format"
msgstr ""

#: tasks/models.py:1200
msgid "Prediction Meta"
msgstr ""

#: tasks/models.py:1201
msgid "Prediction Metas"
msgstr ""

#: templates/standard_form.html:11
msgid "Create"
msgstr ""

#: users/models.py:26
msgid "year"
msgstr ""

#: users/models.py:65
msgid "last activity"
msgstr ""

#: users/models.py:86
msgid "username"
msgstr ""

#: users/models.py:87
msgid "email address"
msgstr ""

#: users/models.py:89
msgid "first name"
msgstr ""

#: users/models.py:90
msgid "last name"
msgstr ""

#: users/models.py:91
msgid "phone"
msgstr ""

#: users/models.py:95
msgid "staff status"
msgstr ""

#: users/models.py:95
msgid "Designates whether the user can log into this admin site."
msgstr ""

#: users/models.py:99
msgid "active"
msgstr ""

#: users/models.py:101
msgid ""
"Designates whether to treat this user as active. Unselect this instead of "
"deleting accounts."
msgstr ""

#: users/models.py:104
msgid "date joined"
msgstr ""

#: users/models.py:106
msgid "last annotation activity"
msgstr ""

#: users/models.py:113
msgid "allow newsletters"
msgstr ""

#: users/models.py:113
msgid "Allow sending newsletters to user"
msgstr ""

#: users/models.py:124
msgid "user"
msgstr ""

#: users/models.py:125
msgid "users"
msgstr ""

#: users/product_tours/models.py:9
msgid "Ready"
msgstr ""

#: users/product_tours/models.py:34
msgid "Name"
msgstr ""

#: users/product_tours/models.py:38
msgid "State"
msgstr ""

#: users/product_tours/models.py:46
msgid "Interaction Data"
msgstr ""

#: webhooks/models.py:38
msgid "URL of webhook"
msgstr ""

#: webhooks/models.py:41
msgid "does webhook send the payload"
msgstr ""

#: webhooks/models.py:47
msgid "Use webhook for all actions"
msgstr ""

#: webhooks/models.py:53
msgid "request extra headers of webhook"
msgstr ""

#: webhooks/models.py:60
msgid "is webhook active"
msgstr ""

#: webhooks/models.py:65
msgid "Creation time"
msgstr ""

#: webhooks/models.py:66
msgid "Last update time"
msgstr ""

#: webhooks/models.py:115
msgid "Project created"
msgstr ""

#: webhooks/models.py:124
msgid "Project updated"
msgstr ""

#: webhooks/models.py:133
msgid "Project deleted"
msgstr ""

#: webhooks/models.py:142
msgid "Task created"
msgstr ""

#: webhooks/models.py:151
msgid "Task deleted"
msgstr ""

#: webhooks/models.py:160
msgid "Annotation created"
msgstr ""

#: webhooks/models.py:176
msgid "Annotations created"
msgstr ""

#: webhooks/models.py:192
msgid "Annotation updated"
msgstr ""

#: webhooks/models.py:208
msgid "Annotation deleted"
msgstr ""

#: webhooks/models.py:217
msgid "Label link created"
msgstr ""

#: webhooks/models.py:226
msgid "Label link updated"
msgstr ""

#: webhooks/models.py:242
msgid "Label link deleted"
msgstr ""

#: webhooks/models.py:255
msgid "action of webhook"
msgstr ""

#: webhooks/models.py:259
msgid "Action value"
msgstr ""

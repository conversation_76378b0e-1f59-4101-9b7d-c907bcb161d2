# Generated by Django 4.2.15 on 2024-08-14 16:44

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("ml_models", "0009_alter_thirdpartymodelversion_provider"),
    ]

    operations = [
        migrations.AddField(
            model_name="modelinterface",
            name="skill_name",
            field=models.CharField(
                choices=[
                    ("TextClassification", "TextClassification"),
                    ("NamedEntityRecognition", "NamedEntityRecognition"),
                ],
                max_length=255,
                null=True,
            ),
        ),
        migrations.RunSQL(
            # set existing ModelInterface objects to text classification
            sql=[
                "UPDATE ml_models_modelinterface SET skill_name = 'TextClassification' WHERE skill_name IS NULL;",
            ],
            # statement above is not reversible, but doesn't matter because the column will be dropped, so do nothing
            reverse_sql=[]
        ),
    ]

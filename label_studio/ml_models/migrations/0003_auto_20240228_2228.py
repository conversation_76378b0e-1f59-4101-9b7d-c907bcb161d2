# Generated by Django 3.2.23 on 2024-02-28 22:28

from django.db import migrations, models
import ml_models.models


class Migration(migrations.Migration):

    dependencies = [
        ('projects', '0026_auto_20231103_0020'),
        ('ml_models', '0002_modelrun'),
    ]

    operations = [
        migrations.AddField(
            model_name='modelinterface',
            name='associated_projects',
            field=models.ManyToManyField(blank=True, to='projects.Project'),
        ),
        migrations.AddField(
            model_name='modelinterface',
            name='input_fields',
            field=models.JSONField(default=list, validators=[ml_models.models.validate_string_list]),
        ),
        migrations.AddField(
            model_name='modelinterface',
            name='output_classes',
            field=models.JSONField(default=list, validators=[ml_models.models.validate_string_list]),
        ),
    ]

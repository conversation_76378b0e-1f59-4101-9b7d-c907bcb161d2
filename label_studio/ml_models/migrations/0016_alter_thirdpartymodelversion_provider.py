# Generated by Django 5.1.5 on 2025-02-07 16:47

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("ml_models", "0015_alter_thirdpartymodelversion_provider"),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name="thirdpartymodelversion",
            name="provider",
            field=models.CharField(
                choices=[
                    ("OpenAI", "OpenAI"),
                    ("AzureOpenAI", "AzureOpenAI"),
                    ("AzureAIFoundry", "AzureAIFoundry"),
                    ("VertexAI", "VertexAI"),
                    ("Gemini", "Gemini"),
                    ("Anthropic", "Anthropic"),
                    ("Custom", "Custom"),
                ],
                default="OpenAI",
                help_text="The model provider to use e.g. OpenAI",
                max_length=255,
            ),
        ),
    ]

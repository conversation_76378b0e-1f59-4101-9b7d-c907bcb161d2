# Generated by Django 5.1.4 on 2025-01-10 18:38

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("ml_models", "0013_alter_thirdpartymodelversion_provider"),
    ]

    operations = [
        migrations.AlterField(
            model_name="thirdpartymodelversion",
            name="provider",
            field=models.CharField(
                choices=[
                    ("OpenAI", "OpenAI"),
                    ("AzureOpenAI", "AzureOpenAI"),
                    ("VertexAI", "VertexAI"),
                    ("Gemini", "Gemini"),
                    ("Custom", "Custom"),
                ],
                default="OpenAI",
                help_text="The model provider to use e.g. OpenAI",
                max_length=255,
            ),
        ),
    ]

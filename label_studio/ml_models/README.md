# ML Models

Currently supporting a feature in [Label Studio Enterprise](https://github.com/HumanSignal/label-studio-enterprise).

## ModelInterface

Defines a "type signature" for the inputs and outputs for an LLM call as related to a Label Studio project.

## ModelVersion

Defines an "implementation" of an LLM call, given any user input needed. Currently, the only one implemented is a third-party model (such as OpenAI) with a chat prompt.

## ModelRun

Defines a run of a ModelVersion on a chosen set of tasks on a project. Adds that <PERSON>V<PERSON><PERSON>'s outputs to the project as predictions.

# Diagram

```mermaid
---
Django ER Diagram
---
erDiagram
ModelInterface {
    AutoField id
    CharField title
    TextField description
    Foreign<PERSON><PERSON> created_by
    <PERSON>T<PERSON><PERSON>ield created_at
    DateTimeField updated_at
    ForeignKey organization
    JSONField input_fields
    J<PERSON><PERSON><PERSON> output_classes
    ManyToManyField associated_projects
}
ThirdPartyModelVersion {
    AutoField id
    CharField title
    ForeignKey parent_model
    TextField prompt
    CharField provider
    CharField provider_model_id
    <PERSON><PERSON><PERSON> created_by
    <PERSON><PERSON><PERSON><PERSON><PERSON> created_at
    DateTimeField updated_at
    ForeignKey organization
}
ModelRun {
    AutoField id
    ForeignKey organization
    ForeignKey project
    ForeignKey model_version
    <PERSON><PERSON><PERSON> created_by
    Char<PERSON>ield project_subset
    CharField status
    Char<PERSON>ield job_id
    <PERSON>T<PERSON><PERSON><PERSON> created_at
    DateTimeField triggered_at
    DateTimeField predictions_updated_at
    DateTimeField completed_at
}
ModelInterface }|--|| User : created_by
ModelInterface }|--|| Organization : organization
ModelInterface }|--|{ Project : associated_projects
ThirdPartyModelVersion }|--|| ModelInterface : parent_model
ThirdPartyModelVersion }|--|| User : created_by
ThirdPartyModelVersion }|--|| Organization : organization
ModelRun }|--|| Organization : organization
ModelRun }|--|| Project : project
ModelRun }|--|| ThirdPartyModelVersion : model_version
ModelRun }|--|| User : created_by
```


---
generated by:
```bash
pip install django-diagram
python -m django_diagram --settings=core.settings.label_studio --app=ml_models --output=ml_models/README.md
```

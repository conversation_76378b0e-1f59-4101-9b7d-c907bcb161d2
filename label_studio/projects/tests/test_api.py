from django.test import TestCase
from django.urls import reverse
from django.utils.http import urlencode
from rest_framework.test import APIClient
from tasks.models import Task

from .factories import ProjectFactory


class TestProjectCountsListAPI(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.project_1 = ProjectFactory()
        cls.project_2 = ProjectFactory(organization=cls.project_1.organization)
        Task.objects.create(project=cls.project_1, data={'text': 'Task 1'})
        Task.objects.create(project=cls.project_1, data={'text': 'Task 2'})
        Task.objects.create(project=cls.project_2, data={'text': 'Task 3'})

    def get_url(self, **params):
        return f'{reverse("projects:api:project-counts-list")}?{urlencode(params)}'

    def test_get_counts(self):
        client = APIClient()
        client.force_authenticate(user=self.project_1.created_by)
        response = client.get(self.get_url(include='id,task_number,finished_task_number,total_predictions_number'))
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json()['count'], 2)
        self.assertEqual(
            response.json()['results'],
            [
                {
                    'id': self.project_1.id,
                    'task_number': 2,
                    'finished_task_number': 0,
                    'total_predictions_number': 0,
                },
                {
                    'id': self.project_2.id,
                    'task_number': 1,
                    'finished_task_number': 0,
                    'total_predictions_number': 0,
                },
            ],
        )

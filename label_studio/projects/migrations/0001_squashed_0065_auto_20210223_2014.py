"""This file and its contents are licensed under the Apache License 2.0. Please see the included NOTICE for copyright information and LICENSE for a copy of the license.
"""
# Generated by Django 3.1.4 on 2021-03-03 07:37

import annoying.fields
import core.utils.common
from django.conf import settings
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    replaces = [('projects', '0001_initial'), ('projects', '0002_auto_20190318_2010'), ('projects', '0003_auto_20190318_2011'), ('projects', '0004_auto_20190318_2012'), ('projects', '0005_auto_20190318_2014'), ('projects', '0006_auto_20190321_1457'), ('projects', '0007_auto_20190326_2059'), ('projects', '0008_auto_20190328_2028'), ('projects', '0009_auto_20190403_1517'), ('projects', '0010_mlbackendscheduledjob'), ('projects', '0011_auto_20190418_2056'), ('projects', '0012_auto_20190423_1445'), ('projects', '0012_auto_20190422_1253'), ('projects', '0013_merge_20190424_0748'), ('projects', '0014_auto_20190424_0748'), ('projects', '0015_auto_20190506_1532'), ('projects', '0016_projecttemplate_ml_backend'), ('projects', '0017_auto_20190507_1126'), ('projects', '0018_auto_20190507_1633'), ('projects', '0019_auto_20190507_2010'), ('projects', '0020_auto_20190508_1850'), ('projects', '0021_auto_20190509_2057'), ('projects', '0022_auto_20190510_1324'), ('projects', '0023_project_show_skip_button'), ('projects', '0024_project_enable_empty_completion'), ('projects', '0025_project_cluster_annotation'), ('projects', '0026_auto_20190624_1254'), ('projects', '0029_auto_20190702_0959'), ('projects', '0030_mlbackendtrainjob'), ('projects', '0031_project_show_completion_history'), ('projects', '0032_auto_20190708_1648'), ('projects', '0033_auto_20190712_0849'), ('projects', '0034_auto_20190726_1928'), ('projects', '0035_auto_20190805_1955'), ('projects', '0036_auto_20190805_2058'), ('projects', '0037_remove_project_active_learning_enabled'), ('projects', '0038_auto_20190921_0830'), ('projects', '0039_auto_20190923_1323'), ('projects', '0040_auto_20190930_0909'), ('projects', '0041_project_use_kappa'), ('projects', '0042_auto_20191114_1430'), ('projects', '0043_auto_20191126_1145'), ('projects', '0044_auto_20191206_1641'), ('projects', '0045_auto_20200302_1221'), ('projects', '0046_project_agreement_method'), ('projects', '0047_auto_20200313_1925'), ('projects', '0048_auto_20200316_1400'), ('projects', '0049_project_control_weights'), ('projects', '0050_auto_20200417_1019'), ('projects', '0051_project_team'), ('projects', '0052_projecttemplate_organization'), ('projects', '0053_auto_20200504_1324'), ('projects', '0054_project_result_count'), ('projects', '0055_storage'), ('projects', '0056_set_project_skip_onboarding'), ('projects', '0057_auto_20201015_1553'), ('projects', '0058_project_organization'), ('projects', '0059_remove_project_team'), ('projects', '0058_remove_projecttemplate_business'), ('projects', '0059_auto_20210122_1542'), ('projects', '0060_merge_20210126_1328'), ('projects', '0061_delete_storage'), ('projects', '0062_auto_20210217_2135'), ('projects', '0063_auto_20210222_1246'), ('projects', '0064_auto_20210223_0726'), ('projects', '0065_auto_20210223_2014')]

    initial = True

    dependencies = [
        ('organizations', '0001_squashed_0008_auto_20201005_1552'),
        # ('tasks', '0038_delete_storagelink'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        # ('organizations', '0004_auto_20200501_1751'),
    ]

    operations = [
        migrations.CreateModel(
            name='MLBackend',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('url', models.TextField(blank=True, default='http://localhost:8999', null=True, verbose_name='url')),
                ('name', models.TextField(blank=True, default='default', null=True, verbose_name='name')),
                ('title', models.TextField(blank=True, default='Default ML backend', null=True, verbose_name='title')),
                ('type', models.CharField(choices=[('IN', 'Internal'), ('EX', 'External')], default='IN', max_length=100, null=True, verbose_name='type')),
                ('description', models.TextField(blank=True, default='', null=True, verbose_name='description')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='updated at')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='custom_models', to=settings.AUTH_USER_MODEL, verbose_name='custom models')),
            ],
        ),
        migrations.CreateModel(
            name='Project',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=1000, verbose_name='name')),
                ('label_config', models.TextField(blank=True, null=True, verbose_name='label config')),
                ('expert_instruction', models.TextField(blank=True, default='', null=True, verbose_name='expert instruction')),
                ('show_instruction', models.BooleanField(default=False, verbose_name='show instruction')),
                ('skip_onboarding', models.BooleanField(default=False)),
                ('active_learning_enabled', models.BooleanField(default=True, verbose_name='active learning enabled')),
                ('maximum_completions', models.IntegerField(default=1, verbose_name='maximum completion number')),
                ('model_version', models.TextField(blank=True, default='', null=True, verbose_name='model version')),
                ('data_types', models.JSONField(default=dict, null=True, verbose_name='data_types')),
                ('has_finished', models.BooleanField(default=False, verbose_name='has finished')),
                ('is_published', models.BooleanField(default=False, verbose_name='published')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='updated at')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_projects', to=settings.AUTH_USER_MODEL, verbose_name='created by')),
                ('ml_backend', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='projects', to='projects.mlbackend')),
            ],
            options={
                'db_table': 'project',
            },
        ),
        migrations.CreateModel(
            name='ProjectOnboardingSteps',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('code', models.CharField(choices=[('DU', 'Upload your data'), ('CF', 'Configure settings'), ('PB', 'Publish project'), ('IE', 'Invite annotators')], max_length=2, null=True)),
                ('title', models.CharField(max_length=1000, verbose_name='title')),
                ('description', models.TextField(verbose_name='descrition')),
                ('order', models.IntegerField(default=0)),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='updated at')),
            ],
            options={
                'ordering': ['order'],
            },
        ),
        migrations.CreateModel(
            name='ProjectStats',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('data', models.JSONField(default=dict)),
                ('model_version', models.TextField(blank=True, default='', null=True, verbose_name='model version')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='updated at')),
                ('project', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='stats', to='projects.project')),
            ],
        ),
        migrations.CreateModel(
            name='ProjectTemplate',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=1000, verbose_name='title')),
                ('description', models.TextField(verbose_name='descrition')),
                ('cover_image_url', models.CharField(max_length=1000, verbose_name='cover image')),
                ('input_example', models.TextField(verbose_name='input example')),
                ('input_example_json', models.JSONField(default=list, verbose_name='input example json')),
                ('output_example', models.TextField(verbose_name='output example')),
                ('output_example_json', models.JSONField(default=list, verbose_name='output example json')),
                ('label_config', models.TextField(verbose_name='label config')),
                ('expert_instruction', models.TextField(default='', verbose_name='expert instruction')),
                ('tags', models.JSONField(default=list, verbose_name='tags')),
                ('task_data', models.JSONField(default=list, verbose_name='task data')),
                ('is_published', models.BooleanField(default=True, verbose_name='published')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='updated at')),
            ],
        ),
        migrations.CreateModel(
            name='UploadedDataFile',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('file', models.FileField(upload_to='uploads/%Y/%m/%d')),
            ],
        ),
        migrations.CreateModel(
            name='ProjectOnboarding',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('finished', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='updated at')),
                ('project', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='projects.project')),
                ('step', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='po_through', to='projects.projectonboardingsteps')),
            ],
        ),
        migrations.AddField(
            model_name='project',
            name='onboarding_steps',
            field=models.ManyToManyField(related_name='projects', through='projects.ProjectOnboarding', to='projects.ProjectOnboardingSteps'),
        ),
        migrations.AddField(
            model_name='project',
            name='template_used',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='projects', to='projects.projecttemplate', verbose_name='projects'),
        ),
        migrations.AddField(
            model_name='projectstats',
            name='num_completions',
            field=models.IntegerField(default=0, verbose_name='num_completions'),
        ),
        migrations.AddField(
            model_name='projectstats',
            name='num_honeypots',
            field=models.IntegerField(default=0, verbose_name='num_honeypots'),
        ),
        migrations.AddField(
            model_name='projectstats',
            name='num_completions_fit_predictions',
            field=models.FloatField(default=0.0, verbose_name='num_completions_fit_predictions'),
        ),
        migrations.AddField(
            model_name='projectstats',
            name='num_honeypots_fit_predictions',
            field=models.FloatField(default=0.0, verbose_name='num_honeypots_fit_predictions'),
        ),
        migrations.RemoveField(
            model_name='project',
            name='ml_backend',
        ),
        migrations.CreateModel(
            name='MLBackendConnection',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('schema', models.JSONField(default=dict)),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='created at')),
                ('ml_backend', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='project_connections', to='projects.mlbackend')),
                ('project', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='ml_backend_connections', to='projects.project')),
            ],
        ),
        migrations.AddField(
            model_name='project',
            name='ml_backend_active_connection',
            field=models.OneToOneField(help_text='The connection ID that identifies current ML backend.', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='active_project', to='projects.mlbackendconnection'),
        ),
        migrations.AddField(
            model_name='project',
            name='batch_size',
            field=models.IntegerField(default=100, help_text='Batch size for machine learning', verbose_name='batch_size'),
        ),
        migrations.AddField(
            model_name='project',
            name='interval',
            field=models.IntegerField(default=100, help_text='Interval of calculations for MLBackend', verbose_name='interval'),
        ),
        migrations.CreateModel(
            name='MLBackendScheduledJob',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('job_id', models.CharField(max_length=128)),
                ('created_on', models.DateTimeField(auto_now_add=True)),
                ('project', models.OneToOneField(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='scheduled_job', to='projects.project')),
            ],
        ),
        migrations.AlterField(
            model_name='project',
            name='active_learning_enabled',
            field=models.BooleanField(default=False, verbose_name='active learning enabled'),
        ),
        migrations.AlterField(
            model_name='project',
            name='active_learning_enabled',
            field=models.BooleanField(default=False, verbose_name='active learning enabled'),
        ),
        migrations.AlterField(
            model_name='mlbackend',
            name='created_by',
            field=models.ForeignKey(help_text='User ID who creates this model', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='custom_models', to=settings.AUTH_USER_MODEL, verbose_name='custom models'),
        ),
        migrations.AlterField(
            model_name='mlbackend',
            name='description',
            field=models.TextField(blank=True, default='', help_text='Model description', null=True, verbose_name='description'),
        ),
        migrations.AlterField(
            model_name='mlbackend',
            name='name',
            field=models.TextField(blank=True, default='default', help_text='Model name', null=True, verbose_name='name'),
        ),
        migrations.AlterField(
            model_name='mlbackend',
            name='title',
            field=models.TextField(blank=True, default='Default ML backend', help_text='Model title', null=True, verbose_name='title'),
        ),
        migrations.AlterField(
            model_name='mlbackend',
            name='type',
            field=models.CharField(choices=[('IN', 'Internal'), ('EX', 'External')], default='IN', help_text='Backend type', max_length=100, null=True, verbose_name='type'),
        ),
        migrations.AlterField(
            model_name='mlbackend',
            name='url',
            field=models.TextField(blank=True, default='http://localhost:8999', help_text='Model server URL', null=True, verbose_name='url'),
        ),
        migrations.AlterField(
            model_name='project',
            name='active_learning_enabled',
            field=models.BooleanField(default=False, help_text='Enable machine learning backend and active learning strategy for the task selection', verbose_name='active learning enabled'),
        ),
        migrations.AlterField(
            model_name='project',
            name='expert_instruction',
            field=models.TextField(blank=True, default='', help_text='Expert instruction in HTML format', null=True, verbose_name='expert instruction'),
        ),
        migrations.AlterField(
            model_name='project',
            name='is_published',
            field=models.BooleanField(default=False, help_text='Project publishing flag for experts', verbose_name='published'),
        ),
        migrations.AlterField(
            model_name='project',
            name='label_config',
            field=models.TextField(blank=True, help_text='Label config in XML format. More about it in <a href="https://labelstud.io/guide/setup.html">documentation</a>', null=True, verbose_name='label config'),
        ),
        migrations.AlterField(
            model_name='project',
            name='maximum_completions',
            field=models.IntegerField(default=1, help_text='Maximum overlaps of expert completions for one task. If the completion number per task is equal or greater to this value, the task becomes finished (is_labeled=True)', verbose_name='maximum completion number'),
        ),
        migrations.AlterField(
            model_name='project',
            name='model_version',
            field=models.TextField(blank=True, default='', help_text='Machine learning model version', null=True, verbose_name='model version'),
        ),
        migrations.AlterField(
            model_name='project',
            name='show_instruction',
            field=models.BooleanField(default=False, help_text='Show the instruction to the expert before he starts', verbose_name='show instruction'),
        ),
        migrations.AlterField(
            model_name='project',
            name='skip_onboarding',
            field=models.BooleanField(default=False, help_text='Skip onboarding steps'),
        ),
        migrations.AlterField(
            model_name='project',
            name='title',
            field=models.CharField(help_text='Project name', max_length=1000, verbose_name='name'),
        ),
        migrations.AddField(
            model_name='projecttemplate',
            name='ml_backend',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='project_templates', to='projects.mlbackend'),
        ),
        migrations.AlterField(
            model_name='projectonboardingsteps',
            name='code',
            field=models.CharField(choices=[('DU', 'Upload your data'), ('CF', 'Configure settings'), ('PB', 'Publish project'), ('IE', 'Invite collaborators')], max_length=2, null=True),
        ),
        migrations.AlterField(
            model_name='project',
            name='title',
            field=models.CharField(help_text='Project name, length is from 3 to 50 chars', max_length=50, validators=[django.core.validators.MinLengthValidator(3), django.core.validators.MaxLengthValidator(50)], verbose_name='name'),
        ),
        migrations.AddField(
            model_name='project',
            name='show_skip_button',
            field=models.BooleanField(default=True, help_text='Show a skip button in interface and allow collaborators to skip the task', verbose_name='show skip button'),
        ),
        migrations.AddField(
            model_name='project',
            name='enable_empty_completion',
            field=models.BooleanField(default=True, help_text='Allow submit empty completions', verbose_name='enable empty completion'),
        ),
        migrations.AddField(
            model_name='project',
            name='cluster_annotation',
            field=models.BooleanField(default=False, help_text='If enabled, completions for all tasks in one cluster are propagated', verbose_name='cluster annotation'),
        ),
        migrations.AlterField(
            model_name='projectonboardingsteps',
            name='description',
            field=models.TextField(verbose_name='description'),
        ),
        migrations.AlterUniqueTogether(
            name='project',
            unique_together={('title', 'created_by')},
        ),
        migrations.AddField(
            model_name='project',
            name='min_completions_to_start_training',
            field=models.IntegerField(default=10, help_text='Minimum number of completed tasks after which training is started', verbose_name='min_completions_to_start_training'),
        ),
        migrations.AlterField(
            model_name='project',
            name='batch_size',
            field=models.IntegerField(default=1, help_text='How many predictions are sent to ML backend in one request', verbose_name='batch_size'),
        ),
        migrations.RemoveField(
            model_name='project',
            name='interval',
        ),
        migrations.AddField(
            model_name='project',
            name='show_completion_history',
            field=models.BooleanField(default=False, help_text='Show completion history to collaborator', verbose_name='show completion history'),
        ),
        migrations.CreateModel(
            name='MLBackendTrainJob',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('job_id', models.CharField(max_length=128)),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='updated at')),
                ('project', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='ml_backend_train_jobs', to='projects.project')),
                ('completed_tasks_count', models.IntegerField(default=0, help_text='Number of completed tasks at the moment when this job is created', verbose_name='completed_tasks_count')),
                ('is_finished', models.BooleanField(default=False, help_text='Whether this job is finished.', verbose_name='is_finished')),
                ('model_version', models.TextField(blank=True, default='', help_text='Model version returned when job is finished', null=True, verbose_name='model version')),
            ],
        ),
        migrations.AlterField(
            model_name='project',
            name='batch_size',
            field=models.IntegerField(default=100, help_text='How many predictions are sent to ML backend in one request', verbose_name='batch_size'),
        ),
        migrations.AddField(
            model_name='project',
            name='show_collab_predictions',
            field=models.BooleanField(default=True, help_text='If set collaborator gets model predictions', verbose_name='show predictions to collaborator'),
        ),
        migrations.AlterField(
            model_name='project',
            name='label_config',
            field=models.TextField(blank=True, help_text='Label config in XML format. See more about it in documentation', null=True, verbose_name='label config'),
        ),
        migrations.RemoveField(
            model_name='project',
            name='active_learning_enabled',
        ),
        migrations.AddField(
            model_name='project',
            name='show_ground_truths_first',
            field=models.BooleanField(default=True, verbose_name='show ground truths first'),
        ),
        migrations.AddField(
            model_name='project',
            name='sampling',
            field=models.CharField(choices=[('Sequential sampling', 'Tasks are ordered by their IDs'), ('Uniform sampling', 'Tasks are chosen randomly'), ('Uncertainty sampling', 'Tasks are chosen according to model uncertainty scores (active learning mode)')], default='Uniform sampling', max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='project',
            name='task_data_login',
            field=models.CharField(blank=True, help_text='Task data credentials: login', max_length=100, null=True, verbose_name='task_data_login'),
        ),
        migrations.AddField(
            model_name='project',
            name='task_data_password',
            field=models.CharField(blank=True, help_text='Task data credentials: password', max_length=100, null=True, verbose_name='task_data_password'),
        ),
        migrations.AddField(
            model_name='project',
            name='use_kappa',
            field=models.BooleanField(default=False, help_text="If categorical variables are used in labeling (e.g. choices), Cohen's Kappa statistic is computed to measure inter-rater reliability instead of basic agreement"),
        ),
        migrations.AddField(
            model_name='project',
            name='metric_name',
            field=models.TextField(blank=True, default='', help_text='Evaluation metric chosen for this project', null=True, verbose_name='metric_name'),
        ),
        migrations.AddField(
            model_name='project',
            name='metric_params',
            field=models.JSONField(blank=True, default=dict, null=True, verbose_name='metric params'),
        ),
        migrations.AlterField(
            model_name='project',
            name='ml_backend_active_connection',
            field=models.OneToOneField(blank=True, help_text='The connection ID that identifies current ML backend.', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='active_project', to='projects.mlbackendconnection'),
        ),
        migrations.AlterField(
            model_name='project',
            name='template_used',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='projects', to='projects.projecttemplate', verbose_name='Project templates'),
        ),
        migrations.AlterField(
            model_name='projectonboardingsteps',
            name='code',
            field=models.CharField(choices=[('DU', 'Import your data'), ('CF', 'Configure settings'), ('PB', 'Publish project'), ('IE', 'Invite collaborators')], max_length=2, null=True),
        ),
        migrations.AddField(
            model_name='project',
            name='metric_threshold',
            field=models.FloatField(default=0.5, help_text='Threshold imposed on dist between two completions: if dist>threshold, they are considered as equal', verbose_name='metric_threshold'),
        ),
        migrations.AddField(
            model_name='project',
            name='overlap_cohort_percentage',
            field=models.IntegerField(default=100, verbose_name='overlap_cohort_percentage'),
        ),
        migrations.AddField(
            model_name='project',
            name='show_overlap_first',
            field=models.BooleanField(default=True, verbose_name='show overlap first'),
        ),
        migrations.AddField(
            model_name='project',
            name='agreement_threshold',
            field=models.FloatField(default=0.0, help_text='Minimal agreement score to consider tasks labeled for sending to ML backend (inclusively)', verbose_name='agreement_threshold'),
        ),
        migrations.AddField(
            model_name='project',
            name='agreement_method',
            field=models.CharField(choices=[('Single linkage', 'Threshold based completions grouping using single linkage method'), ('Complete linkage', 'Threshold based completions grouping using complete linkage method'), ('No grouping', 'Compute agreement without grouping (just averaging the distances between labeling results)')], default='Single linkage', max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='projecttemplate',
            name='created_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='project_templates', to=settings.AUTH_USER_MODEL, verbose_name='created by'),
        ),
        migrations.AddField(
            model_name='projecttemplate',
            name='is_private',
            field=models.BooleanField(default=True, help_text='If template is private, it is accessible only from private team', verbose_name='private'),
        ),
        migrations.AddField(
            model_name='projecttemplate',
            name='project_settings',
            field=models.JSONField(default=dict, help_text='general dict serialized project settings', verbose_name='project settings'),
        ),
        migrations.AlterField(
            model_name='projecttemplate',
            name='cover_image_url',
            field=models.CharField(blank=True, default='', max_length=1000, null=True, verbose_name='cover image'),
        ),
        migrations.AlterField(
            model_name='projecttemplate',
            name='description',
            field=models.TextField(default='', null=True, verbose_name='description'),
        ),
        migrations.AlterField(
            model_name='projecttemplate',
            name='input_example',
            field=models.TextField(blank=True, verbose_name='input example'),
        ),
        migrations.AlterField(
            model_name='projecttemplate',
            name='output_example',
            field=models.TextField(blank=True, verbose_name='output example'),
        ),
        migrations.AddField(
            model_name='project',
            name='control_weights',
            field=models.JSONField(default=dict, help_text='Weights for control tags', null=True, verbose_name='control weights'),
        ),
        migrations.CreateModel(
            name='MLBackendPredictionJob',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('job_id', models.CharField(max_length=128)),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='updated at')),
                ('project', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='ml_backend_prediction_jobs', to='projects.project')),
            ],
        ),
        migrations.DeleteModel(
            name='MLBackendScheduledJob',
        ),
        migrations.AddField(
            model_name='projecttemplate',
            name='organization',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='project_templates', to='organizations.organization'),
        ),
        migrations.CreateModel(
            name='ProjectMember',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('enabled', models.BooleanField(default=True, help_text='Project member is enabled')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='updated at')),
            ],
        ),
        migrations.AddField(
            model_name='project',
            name='token',
            field=models.CharField(blank=True, default=core.utils.common.create_hash, max_length=256, null=True, verbose_name='token'),
        ),
        migrations.AddField(
            model_name='projectmember',
            name='project',
            field=models.ForeignKey(help_text='Project ID', on_delete=django.db.models.deletion.CASCADE, related_name='members', to='projects.project'),
        ),
        migrations.AddField(
            model_name='projectmember',
            name='user',
            field=models.ForeignKey(help_text='User ID', on_delete=django.db.models.deletion.CASCADE, related_name='project_memberships', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='project',
            name='result_count',
            field=models.IntegerField(default=0, help_text='Total results inside of completions counter', verbose_name='result count'),
        ),
        migrations.CreateModel(
            name='Storage',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(help_text='Cloud storage title', max_length=256, null=True, verbose_name='title')),
                ('description', models.TextField(blank=True, help_text='Cloud storage description', null=True, verbose_name='description')),
                ('type', models.CharField(choices=[('s3', 'AWS S3 storage')], default='s3', max_length=32)),
                ('path', models.TextField(blank=True, help_text='Cloud storage path (e.g. bucket name)', null=True, verbose_name='path')),
                ('prefix', models.TextField(blank=True, help_text='Cloud storage prefix (e.g. container path)', null=True, verbose_name='prefix')),
                ('regex', models.TextField(blank=True, help_text='Cloud storage regex for filtering objects', null=True, verbose_name='regex')),
                ('data_key', models.TextField(blank=True, help_text='Data key to connect BLOBs with object tags', null=True, verbose_name='data_key')),
                ('use_blob_urls', models.BooleanField(default=False, help_text='Interpret objects as BLOBs and generate URLs', verbose_name='use BLOB URLs')),
                ('params', models.JSONField(blank=True, default=dict, help_text='Cloud storage specific parameters (e.g. credentials stored in key: value format', null=True, verbose_name='params')),
                ('created_at', models.DateTimeField(auto_now_add=True, help_text='Creation time', verbose_name='created at')),
                ('project', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='storages', to='projects.project')),
            ],
        ),
        migrations.AlterField(
            model_name='project',
            name='task_data_login',
            field=models.CharField(blank=True, help_text='Task data credentials: login', max_length=256, null=True, verbose_name='task_data_login'),
        ),
        migrations.AlterField(
            model_name='project',
            name='task_data_password',
            field=models.CharField(blank=True, help_text='Task data credentials: password', max_length=256, null=True, verbose_name='task_data_password'),
        ),
        migrations.AddField(
            model_name='project',
            name='organization',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='projects', to='organizations.organization'),
        ),
        migrations.CreateModel(
            name='ProjectSummary',
            fields=[
                ('project', annoying.fields.AutoOneToOneField(on_delete=django.db.models.deletion.CASCADE, primary_key=True, related_name='summary', serialize=False, to='projects.project')),
                ('created_at', models.DateTimeField(auto_now_add=True, help_text='Creation time', verbose_name='created at')),
                ('all_data_columns', models.JSONField(default=dict, help_text='All data columns found in imported tasks', null=True, verbose_name='all data columns')),
                ('common_data_columns', models.JSONField(default=list, help_text='Common data columns found in imported tasks', null=True, verbose_name='common data columns')),
                ('created_annotations', models.JSONField(default=dict, help_text='Unique annotation types identified by tuple (from_name, to_name, type)', null=True, verbose_name='created annotations')),
                ('created_labels', models.JSONField(default=dict, help_text='Unique labels', null=True, verbose_name='created labels')),
            ],
        ),
        migrations.DeleteModel(
            name='UploadedDataFile',
        ),
        migrations.DeleteModel(
            name='Storage',
        ),
        migrations.AddField(
            model_name='project',
            name='is_draft',
            field=models.BooleanField(default=False, help_text='Whether the project is in the middle of creation process', verbose_name='is draft'),
        ),
        migrations.AlterField(
            model_name='project',
            name='label_config',
            field=models.TextField(blank=True, default='<View></View>', help_text='Label config in XML format. See more about it in documentation', null=True, verbose_name='label config'),
        ),
        migrations.AlterField(
            model_name='project',
            name='title',
            field=models.CharField(blank=True, help_text='Project name, length is from 3 to 50 chars', max_length=50, null=True, validators=[django.core.validators.MinLengthValidator(3), django.core.validators.MaxLengthValidator(50)], verbose_name='name'),
        ),
        migrations.RemoveField(
            model_name='project',
            name='batch_size',
        ),
        migrations.RemoveField(
            model_name='project',
            name='cluster_annotation',
        ),
        migrations.RemoveField(
            model_name='project',
            name='has_finished',
        ),
        migrations.RemoveField(
            model_name='project',
            name='onboarding_steps',
        ),
        migrations.RemoveField(
            model_name='project',
            name='skip_onboarding',
        ),
        migrations.AddField(
            model_name='project',
            name='description',
            field=models.TextField(blank=True, default='', help_text='Expert instruction in HTML format', null=True, verbose_name='expert instruction'),
        ),
        migrations.RemoveField(
            model_name='mlbackendconnection',
            name='ml_backend',
        ),
        migrations.RemoveField(
            model_name='mlbackendconnection',
            name='project',
        ),
        migrations.RemoveField(
            model_name='mlbackendpredictionjob',
            name='project',
        ),
        migrations.RemoveField(
            model_name='mlbackendtrainjob',
            name='project',
        ),
        migrations.RemoveField(
            model_name='projectstats',
            name='project',
        ),
        migrations.RemoveField(
            model_name='project',
            name='ml_backend_active_connection',
        ),
        migrations.RemoveField(
            model_name='projecttemplate',
            name='ml_backend',
        ),
        migrations.DeleteModel(
            name='MLBackend',
        ),
        migrations.DeleteModel(
            name='MLBackendConnection',
        ),
        migrations.DeleteModel(
            name='MLBackendPredictionJob',
        ),
        migrations.DeleteModel(
            name='MLBackendTrainJob',
        ),
        migrations.DeleteModel(
            name='ProjectStats',
        ),
        migrations.AlterField(
            model_name='project',
            name='title',
            field=models.CharField(blank=True, default='', help_text='Project name, length is from 3 to 50 chars', max_length=50, null=True, validators=[django.core.validators.MinLengthValidator(3), django.core.validators.MaxLengthValidator(50)], verbose_name='title'),
        ),
    ]

# Generated by Django 3.1.14 on 2022-02-11 22:18

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('projects', '0015_merge_20220117_0749'),
    ]

    operations = [
        migrations.AlterField(
            model_name='project',
            name='min_annotations_to_start_training',
            field=models.IntegerField(default=0, help_text='Minimum number of completed tasks after which model training is started', verbose_name='min_annotations_to_start_training'),
        ),
        migrations.RemoveField(
            model_name='project',
            name='parsed_label_config',
        ),
        migrations.AddField(
            model_name='project',
            name='parsed_label_config',
            field=models.JSONField(blank=True, default=None, help_text='Parsed label config in JSON format. See more about it in documentation', null=True, verbose_name='parsed label config'),
        ),
    ]

# Generated by Django 3.2.16 on 2023-02-02 16:43

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('projects', '0018_alter_project_control_weights'),
    ]

    operations = [
        migrations.CreateModel(
            name='LabelStreamHistory',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('data', models.JSONField(default=list)),
                ('project', models.ForeignKey(help_text='Project ID', on_delete=django.db.models.deletion.CASCADE, related_name='histories', to='projects.project')),
                ('user', models.ForeignKey(help_text='User ID', on_delete=django.db.models.deletion.CASCADE, related_name='histories', to=settings.AUTH_USER_MODEL)),
            ],
        ),
    ]

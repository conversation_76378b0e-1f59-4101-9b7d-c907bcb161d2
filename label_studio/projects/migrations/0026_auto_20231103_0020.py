# Generated by Django 3.2.20 on 2023-11-03 00:20

from django.db import migrations
from projects.models import Project
from core.models import AsyncMigrationStatus
from core.redis import start_job_async_or_sync
import logging


logger = logging.getLogger(__name__)


def _fill_label_config_hash(migration_name):
    project_tuples = Project.objects.all().values_list('id', 'parsed_label_config')
    for project_id, parsed_label_config in project_tuples:
        migration = AsyncMigrationStatus.objects.create(
            project_id=project_id,
            name=migration_name,
            status=AsyncMigrationStatus.STATUS_STARTED,
        )

        hashed_label_config = hash(str(parsed_label_config))
        Project.objects.filter(id=project_id).update(label_config_hash=hashed_label_config)

        migration.status = AsyncMigrationStatus.STATUS_FINISHED
        migration.save()


def fill_label_config_hash(migration_name):
    logger.info('Start filling label config hash')
    start_job_async_or_sync(_fill_label_config_hash, migration_name=migration_name)
    logger.info('Finished filling label config hash')


def forward(apps, schema_editor):
    fill_label_config_hash('0026_auto_20231103_0020')


def backwards(apps, schema_editor):
    pass


class Migration(migrations.Migration):

    dependencies = [
        ('projects', '0025_project_label_config_hash'),
    ]

    operations = [
        migrations.RunPython(forward, backwards)
    ]

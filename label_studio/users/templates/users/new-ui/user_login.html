{% extends 'users/new-ui/user_base.html' %}

{% block content %}
  {{ block.super }}
  <script nonce="{{request.csp_nonce}}">
    gaClientIdTrackingIframe('users.login.view');
    // Give time for `ls_gaclient_id` to be set
    setTimeout(() => {
      const ls_gaclient_id = sessionStorage.getItem('ls_gaclient_id');
      __lsa('users.login.view', { ls_gaclient_id });
    }, 2000);
  </script>
{% endblock %}

{% block user_content %}
  <div class="form-wrapper">
    <h2>Log in</h2>
    <form id="login-form" action="{% url 'user-login' %}?next={{ next }}" method="post">
      {% csrf_token %}
      <div class="input-wrapper">
        <label>Email Address</label>
        <input type="text" class="lsf-input-ls" name="email" id="email" value="{{ form.data.email }}">
      </div>
      <div class="input-wrapper">
        <label>Password</label>
        <input type="password" class="lsf-input-ls" name="password" id="password">
      </div>
      {% if form.non_field_errors %}
        {% for error in form.non_field_errors %}
          <p class="error">
            {{ error }}
          </p>
        {% endfor %}
      {% endif %}
      <div class="form-group">
        <input type="checkbox" id="persist_session" name="persist_session" class="lsf-checkbox-ls" checked="checked" style="width: auto;" />
        <label for="persist_session">Keep me logged in this browser</label>
      </div>
      <button type="submit" aria-label="Log In" class="lsf-button-ls lsf-button-ls_look_primary">Log in</button>
    </form>
  </div>
  {% if not settings.DISABLE_SIGNUP_WITHOUT_LINK %}
  <div class="text-wrapper">
    <p class="">Don't have an account?</p>
    <a href="{% url 'user-signup' %}{% querystring %}">Sign up</a>
  </div>
  {% endif %}
{% endblock %}

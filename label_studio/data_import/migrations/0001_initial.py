"""This file and its contents are licensed under the Apache License 2.0. Please see the included NOTICE for copyright information and LIC<PERSON><PERSON> for a copy of the license.
"""
# Generated by Django 3.1.4 on 2021-01-21 17:39

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('projects', '__first__'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='FileUpload',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('file', models.FileField(upload_to='upload')),
                ('project', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='file_uploads', to='projects.project')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='file_uploads', to=settings.AUTH_USER_MODEL)),
            ],
        ),
    ]

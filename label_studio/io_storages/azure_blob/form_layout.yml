# 1x3 grid
title_container_prefix: &title_container_prefix
  - type: text
    name: title
    label: Storage Title
  - type: text
    name: container
    label: Container Name
    required: true
  - type: text
    name: prefix
    label: Container Prefix

# 1x3 grid
azure_params: &azure_params
  - type: password
    name: account_name
    label: Account Name
    autoComplete: "off"
    skipAutofill: true
    allowEmpty: false
    protectedValue: true
  - type: password
    name: account_key
    label: Account Key
    autoComplete: "new-password"
    skipAutofill: true
    allowEmpty: false
    protectedValue: true
  - null

ImportStorage:
  # Title, Bucket, Prefix
  - columnCount: 3
    fields: *title_container_prefix
  # Regex filter
  - columnCount: 1
    fields:
      - type: text
        name: regex_filter
        label: File Filter Regex
        placeholder: '.*csv or .*(jpe?g|png|tiff) or .\w+-\d+.text'
        validators:
          - regexp
  # AWS specific params
  - columnCount: 3
    fields: *azure_params

  # 2 columns grid
  - columnCount: 2
    columns:
      - width: 345
        fields:
          - type: toggle
            name: use_blob_urls
            label: Treat every bucket object as a source file
            description: If unchecked, treat every bucket object as a JSON-formatted task. Optional
      - fields:
          - type: toggle
            name: presign
            label: Use pre-signed URLs
            value: true
          - type: counter
            name: presign_ttl
            label: Expiration minutes
            min: 1
            value: 15
            dependency: presign

ExportStorage:
  - columnCount: 3
    fields: *title_container_prefix
  - columnCount: 2
    fields: *azure_params

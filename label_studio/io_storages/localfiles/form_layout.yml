# 1x3 grid
title_bucket_prefix: &title_bucket_prefix
  - type: text
    name: title
    label: Storage Title
  - type: text
    name: path
    label: Absolute local path
    required: true

ImportStorage:
  # Title, Bucket, Prefix
  - columnCount: 2
    fields: *title_bucket_prefix
  # Regex filter
  - columnCount: 1
    fields:
      - type: text
        name: regex_filter
        label: File Filter Regex
        placeholder: '.*csv or .*(jpe?g|png|tiff) or .\w+-\d+.text'
        validators:
          - regexp

  # 2 columns grid
  - columnCount: 2
    columns:
      - width: 371
        fields:
          - type: toggle
            name: use_blob_urls
            label: Treat every bucket object as a source file
            description: If unchecked, treat every bucket object as a JSON-formatted task. Optional

ExportStorage:
  - columnCount: 3
    fields: *title_bucket_prefix

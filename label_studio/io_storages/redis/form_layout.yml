redis_title: &redis_title
  - type: text
    name: title
    label: Storage Title

# 2x2 grid
redis_params: &redis_params
  - type: text
    name: db
    label: Database Number (db)
    placeholder: 1
  - type: text
    name: path
    label: Path
  - type: password
    name: password
    label: Password
    autoComplete: "new-password"
  - type: text
    name: host
    label: Host
    placeholder: localhost
  - type: text
    name: port
    label: Port

ImportStorage:
  # Title
  - columnCount: 1
    fields: *redis_title
  # Path, Password, Host and Port
  - columnCount: 2
    fields: *redis_params
  # Regex filter
  - columnCount: 1
    fields:
      - type: text
        name: regex_filter
        label: File Filter Regex
        placeholder: '.*csv or .*(jpe?g|png|tiff) or .\w+-\d+.text'
        validators:
          - regexp

  # 2 columns grid
  - columnCount: 1
    fields:
      - type: toggle
        name: use_blob_urls
        label: Treat every bucket object as a source file
        description: If unchecked, treat every bucket object as a JSON-formatted task. Optional

ExportStorage:
  - columnCount: 2
    fields: *redis_params

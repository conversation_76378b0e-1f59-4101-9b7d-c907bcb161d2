# Generated by Django 3.1.13 on 2021-11-29 11:32

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('projects', '0013_project_reveal_preannotations_interactively'),
        ('io_storages', '0007_auto_20210928_1252'),
    ]

    operations = [
        migrations.AlterField(
            model_name='azureblobexportstorage',
            name='project',
            field=models.ForeignKey(help_text='A unique integer value identifying this project.', on_delete=django.db.models.deletion.CASCADE, related_name='io_storages_azureblobexportstorages', to='projects.project'),
        ),
        migrations.AlterField(
            model_name='azureblobimportstorage',
            name='project',
            field=models.ForeignKey(help_text='A unique integer value identifying this project.', on_delete=django.db.models.deletion.CASCADE, related_name='io_storages_azureblobimportstorages', to='projects.project'),
        ),
        migrations.AlterField(
            model_name='gcsexportstorage',
            name='project',
            field=models.ForeignKey(help_text='A unique integer value identifying this project.', on_delete=django.db.models.deletion.CASCADE, related_name='io_storages_gcsexportstorages', to='projects.project'),
        ),
        migrations.AlterField(
            model_name='gcsimportstorage',
            name='project',
            field=models.ForeignKey(help_text='A unique integer value identifying this project.', on_delete=django.db.models.deletion.CASCADE, related_name='io_storages_gcsimportstorages', to='projects.project'),
        ),
        migrations.AlterField(
            model_name='localfilesexportstorage',
            name='project',
            field=models.ForeignKey(help_text='A unique integer value identifying this project.', on_delete=django.db.models.deletion.CASCADE, related_name='io_storages_localfilesexportstorages', to='projects.project'),
        ),
        migrations.AlterField(
            model_name='localfilesimportstorage',
            name='project',
            field=models.ForeignKey(help_text='A unique integer value identifying this project.', on_delete=django.db.models.deletion.CASCADE, related_name='io_storages_localfilesimportstorages', to='projects.project'),
        ),
        migrations.AlterField(
            model_name='redisexportstorage',
            name='project',
            field=models.ForeignKey(help_text='A unique integer value identifying this project.', on_delete=django.db.models.deletion.CASCADE, related_name='io_storages_redisexportstorages', to='projects.project'),
        ),
        migrations.AlterField(
            model_name='redisimportstorage',
            name='project',
            field=models.ForeignKey(help_text='A unique integer value identifying this project.', on_delete=django.db.models.deletion.CASCADE, related_name='io_storages_redisimportstorages', to='projects.project'),
        ),
        migrations.AlterField(
            model_name='s3exportstorage',
            name='project',
            field=models.ForeignKey(help_text='A unique integer value identifying this project.', on_delete=django.db.models.deletion.CASCADE, related_name='io_storages_s3exportstorages', to='projects.project'),
        ),
        migrations.AlterField(
            model_name='s3importstorage',
            name='project',
            field=models.ForeignKey(help_text='A unique integer value identifying this project.', on_delete=django.db.models.deletion.CASCADE, related_name='io_storages_s3importstorages', to='projects.project'),
        ),
    ]

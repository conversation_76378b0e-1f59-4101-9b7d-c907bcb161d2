# Generated by Django 3.2.16 on 2023-04-18 15:10

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('io_storages', '0011_gcsstoragemixin_google_project_id'),
    ]

    operations = [
        migrations.AddField(
            model_name='azureblobexportstorage',
            name='meta',
            field=models.JSONField(default=dict, help_text='Meta and debug information about storage processes', null=True, verbose_name='meta'),
        ),
        migrations.AddField(
            model_name='azureblobexportstorage',
            name='status',
            field=models.CharField(choices=[('created', 'Created'), ('in_progress', 'In progress'), ('failed', 'Failed'), ('completed', 'Completed')], default='created', max_length=64),
        ),
        migrations.AddField(
            model_name='azureblobexportstorage',
            name='traceback',
            field=models.TextField(blank=True, help_text='Traceback report for the last failed sync', null=True),
        ),
        migrations.AddField(
            model_name='azureblobimportstorage',
            name='meta',
            field=models.JSONField(default=dict, help_text='Meta and debug information about storage processes', null=True, verbose_name='meta'),
        ),
        migrations.AddField(
            model_name='azureblobimportstorage',
            name='status',
            field=models.CharField(choices=[('created', 'Created'), ('in_progress', 'In progress'), ('failed', 'Failed'), ('completed', 'Completed')], default='created', max_length=64),
        ),
        migrations.AddField(
            model_name='azureblobimportstorage',
            name='traceback',
            field=models.TextField(blank=True, help_text='Traceback report for the last failed sync', null=True),
        ),
        migrations.AddField(
            model_name='gcsexportstorage',
            name='meta',
            field=models.JSONField(default=dict, help_text='Meta and debug information about storage processes', null=True, verbose_name='meta'),
        ),
        migrations.AddField(
            model_name='gcsexportstorage',
            name='status',
            field=models.CharField(choices=[('created', 'Created'), ('in_progress', 'In progress'), ('failed', 'Failed'), ('completed', 'Completed')], default='created', max_length=64),
        ),
        migrations.AddField(
            model_name='gcsexportstorage',
            name='traceback',
            field=models.TextField(blank=True, help_text='Traceback report for the last failed sync', null=True),
        ),
        migrations.AddField(
            model_name='gcsimportstorage',
            name='meta',
            field=models.JSONField(default=dict, help_text='Meta and debug information about storage processes', null=True, verbose_name='meta'),
        ),
        migrations.AddField(
            model_name='gcsimportstorage',
            name='status',
            field=models.CharField(choices=[('created', 'Created'), ('in_progress', 'In progress'), ('failed', 'Failed'), ('completed', 'Completed')], default='created', max_length=64),
        ),
        migrations.AddField(
            model_name='gcsimportstorage',
            name='traceback',
            field=models.TextField(blank=True, help_text='Traceback report for the last failed sync', null=True),
        ),
        migrations.AddField(
            model_name='localfilesexportstorage',
            name='meta',
            field=models.JSONField(default=dict, help_text='Meta and debug information about storage processes', null=True, verbose_name='meta'),
        ),
        migrations.AddField(
            model_name='localfilesexportstorage',
            name='status',
            field=models.CharField(choices=[('created', 'Created'), ('in_progress', 'In progress'), ('failed', 'Failed'), ('completed', 'Completed')], default='created', max_length=64),
        ),
        migrations.AddField(
            model_name='localfilesexportstorage',
            name='traceback',
            field=models.TextField(blank=True, help_text='Traceback report for the last failed sync', null=True),
        ),
        migrations.AddField(
            model_name='localfilesimportstorage',
            name='meta',
            field=models.JSONField(default=dict, help_text='Meta and debug information about storage processes', null=True, verbose_name='meta'),
        ),
        migrations.AddField(
            model_name='localfilesimportstorage',
            name='status',
            field=models.CharField(choices=[('created', 'Created'), ('in_progress', 'In progress'), ('failed', 'Failed'), ('completed', 'Completed')], default='created', max_length=64),
        ),
        migrations.AddField(
            model_name='localfilesimportstorage',
            name='traceback',
            field=models.TextField(blank=True, help_text='Traceback report for the last failed sync', null=True),
        ),
        migrations.AddField(
            model_name='redisexportstorage',
            name='meta',
            field=models.JSONField(default=dict, help_text='Meta and debug information about storage processes', null=True, verbose_name='meta'),
        ),
        migrations.AddField(
            model_name='redisexportstorage',
            name='status',
            field=models.CharField(choices=[('created', 'Created'), ('in_progress', 'In progress'), ('failed', 'Failed'), ('completed', 'Completed')], default='created', max_length=64),
        ),
        migrations.AddField(
            model_name='redisexportstorage',
            name='traceback',
            field=models.TextField(blank=True, help_text='Traceback report for the last failed sync', null=True),
        ),
        migrations.AddField(
            model_name='redisimportstorage',
            name='meta',
            field=models.JSONField(default=dict, help_text='Meta and debug information about storage processes', null=True, verbose_name='meta'),
        ),
        migrations.AddField(
            model_name='redisimportstorage',
            name='status',
            field=models.CharField(choices=[('created', 'Created'), ('in_progress', 'In progress'), ('failed', 'Failed'), ('completed', 'Completed')], default='created', max_length=64),
        ),
        migrations.AddField(
            model_name='redisimportstorage',
            name='traceback',
            field=models.TextField(blank=True, help_text='Traceback report for the last failed sync', null=True),
        ),
        migrations.AddField(
            model_name='s3exportstorage',
            name='meta',
            field=models.JSONField(default=dict, help_text='Meta and debug information about storage processes', null=True, verbose_name='meta'),
        ),
        migrations.AddField(
            model_name='s3exportstorage',
            name='status',
            field=models.CharField(choices=[('created', 'Created'), ('in_progress', 'In progress'), ('failed', 'Failed'), ('completed', 'Completed')], default='created', max_length=64),
        ),
        migrations.AddField(
            model_name='s3exportstorage',
            name='traceback',
            field=models.TextField(blank=True, help_text='Traceback report for the last failed sync', null=True),
        ),
        migrations.AddField(
            model_name='s3importstorage',
            name='meta',
            field=models.JSONField(default=dict, help_text='Meta and debug information about storage processes', null=True, verbose_name='meta'),
        ),
        migrations.AddField(
            model_name='s3importstorage',
            name='status',
            field=models.CharField(choices=[('created', 'Created'), ('in_progress', 'In progress'), ('failed', 'Failed'), ('completed', 'Completed')], default='created', max_length=64),
        ),
        migrations.AddField(
            model_name='s3importstorage',
            name='traceback',
            field=models.TextField(blank=True, help_text='Traceback report for the last failed sync', null=True),
        ),
    ]

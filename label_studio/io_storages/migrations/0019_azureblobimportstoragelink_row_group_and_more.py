# Generated by Django 5.1.9 on 2025-05-14 17:23

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("io_storages", "0018_alter_azureblobexportstorage_project_and_more"),
    ]

    operations = [
        migrations.AddField(
            model_name="azureblobimportstoragelink",
            name="row_group",
            field=models.IntegerField(
                blank=True, help_text="Parquet row group", null=True
            ),
        ),
        migrations.AddField(
            model_name="azureblobimportstoragelink",
            name="row_index",
            field=models.IntegerField(
                blank=True,
                help_text="Parquet row index, or JSON[L] object index",
                null=True,
            ),
        ),
        migrations.AddField(
            model_name="gcsimportstoragelink",
            name="row_group",
            field=models.IntegerField(
                blank=True, help_text="Parquet row group", null=True
            ),
        ),
        migrations.AddField(
            model_name="gcsimportstoragelink",
            name="row_index",
            field=models.IntegerField(
                blank=True,
                help_text="Parquet row index, or JSON[L] object index",
                null=True,
            ),
        ),
        migrations.AddField(
            model_name="localfilesimportstoragelink",
            name="row_group",
            field=models.IntegerField(
                blank=True, help_text="Parquet row group", null=True
            ),
        ),
        migrations.AddField(
            model_name="localfilesimportstoragelink",
            name="row_index",
            field=models.IntegerField(
                blank=True,
                help_text="Parquet row index, or JSON[L] object index",
                null=True,
            ),
        ),
        migrations.AddField(
            model_name="redisimportstoragelink",
            name="row_group",
            field=models.IntegerField(
                blank=True, help_text="Parquet row group", null=True
            ),
        ),
        migrations.AddField(
            model_name="redisimportstoragelink",
            name="row_index",
            field=models.IntegerField(
                blank=True,
                help_text="Parquet row index, or JSON[L] object index",
                null=True,
            ),
        ),
        migrations.AddField(
            model_name="s3importstoragelink",
            name="row_group",
            field=models.IntegerField(
                blank=True, help_text="Parquet row group", null=True
            ),
        ),
        migrations.AddField(
            model_name="s3importstoragelink",
            name="row_index",
            field=models.IntegerField(
                blank=True,
                help_text="Parquet row index, or JSON[L] object index",
                null=True,
            ),
        ),
    ]

# Generated by Django 3.2.19 on 2023-08-04 14:32

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('io_storages', '0014_init_statuses'),
    ]

    operations = [
        migrations.AddField(
            model_name='azureblobexportstorage',
            name='synchronizable',
            field=models.BooleanField(default=True, help_text='If storage can be synced', verbose_name='synchronizable'),
        ),
        migrations.AddField(
            model_name='azureblobimportstorage',
            name='synchronizable',
            field=models.BooleanField(default=True, help_text='If storage can be synced', verbose_name='synchronizable'),
        ),
        migrations.AddField(
            model_name='gcsexportstorage',
            name='synchronizable',
            field=models.BooleanField(default=True, help_text='If storage can be synced', verbose_name='synchronizable'),
        ),
        migrations.AddField(
            model_name='gcsimportstorage',
            name='synchronizable',
            field=models.BooleanField(default=True, help_text='If storage can be synced', verbose_name='synchronizable'),
        ),
        migrations.AddField(
            model_name='localfilesexportstorage',
            name='synchronizable',
            field=models.BooleanField(default=True, help_text='If storage can be synced', verbose_name='synchronizable'),
        ),
        migrations.AddField(
            model_name='localfilesimportstorage',
            name='synchronizable',
            field=models.BooleanField(default=True, help_text='If storage can be synced', verbose_name='synchronizable'),
        ),
        migrations.AddField(
            model_name='redisexportstorage',
            name='synchronizable',
            field=models.BooleanField(default=True, help_text='If storage can be synced', verbose_name='synchronizable'),
        ),
        migrations.AddField(
            model_name='redisimportstorage',
            name='synchronizable',
            field=models.BooleanField(default=True, help_text='If storage can be synced', verbose_name='synchronizable'),
        ),
        migrations.AddField(
            model_name='s3exportstorage',
            name='synchronizable',
            field=models.BooleanField(default=True, help_text='If storage can be synced', verbose_name='synchronizable'),
        ),
        migrations.AddField(
            model_name='s3importstorage',
            name='synchronizable',
            field=models.BooleanField(default=True, help_text='If storage can be synced', verbose_name='synchronizable'),
        ),
    ]

# Generated by Django 3.1.12 on 2021-09-06 13:23

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('io_storages', '0005_s3importstorage_recursive_scan'),
    ]

    operations = [
        migrations.AlterField(
            model_name='azureblobexportstorage',
            name='title',
            field=models.CharField(blank=True, help_text='Cloud storage title', max_length=256, null=True, verbose_name='title'),
        ),
        migrations.AlterField(
            model_name='azureblobimportstorage',
            name='title',
            field=models.CharField(blank=True, help_text='Cloud storage title', max_length=256, null=True, verbose_name='title'),
        ),
        migrations.AlterField(
            model_name='gcsexportstorage',
            name='title',
            field=models.CharField(blank=True, help_text='Cloud storage title', max_length=256, null=True, verbose_name='title'),
        ),
        migrations.AlterField(
            model_name='gcsimportstorage',
            name='title',
            field=models.CharField(blank=True, help_text='Cloud storage title', max_length=256, null=True, verbose_name='title'),
        ),
        migrations.AlterField(
            model_name='localfilesexportstorage',
            name='title',
            field=models.CharField(blank=True, help_text='Cloud storage title', max_length=256, null=True, verbose_name='title'),
        ),
        migrations.AlterField(
            model_name='localfilesimportstorage',
            name='title',
            field=models.CharField(blank=True, help_text='Cloud storage title', max_length=256, null=True, verbose_name='title'),
        ),
        migrations.AlterField(
            model_name='redisexportstorage',
            name='title',
            field=models.CharField(blank=True, help_text='Cloud storage title', max_length=256, null=True, verbose_name='title'),
        ),
        migrations.AlterField(
            model_name='redisimportstorage',
            name='title',
            field=models.CharField(blank=True, help_text='Cloud storage title', max_length=256, null=True, verbose_name='title'),
        ),
        migrations.AlterField(
            model_name='s3exportstorage',
            name='title',
            field=models.CharField(blank=True, help_text='Cloud storage title', max_length=256, null=True, verbose_name='title'),
        ),
        migrations.AlterField(
            model_name='s3importstorage',
            name='title',
            field=models.CharField(blank=True, help_text='Cloud storage title', max_length=256, null=True, verbose_name='title'),
        ),
    ]

# Generated by Django 3.2.16 on 2023-04-20 02:59

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('io_storages', '0012_auto_20230418_1510'),
    ]

    operations = [
        migrations.AlterField(
            model_name='azureblobexportstorage',
            name='status',
            field=models.CharField(choices=[('initialized', 'Initialized'), ('queued', 'Queued'), ('in_progress', 'In progress'), ('failed', 'Failed'), ('completed', 'Completed')], default='initialized', max_length=64),
        ),
        migrations.AlterField(
            model_name='azureblobimportstorage',
            name='status',
            field=models.CharField(choices=[('initialized', 'Initialized'), ('queued', 'Queued'), ('in_progress', 'In progress'), ('failed', 'Failed'), ('completed', 'Completed')], default='initialized', max_length=64),
        ),
        migrations.Alter<PERSON>ield(
            model_name='gcsexportstorage',
            name='status',
            field=models.CharField(choices=[('initialized', 'Initialized'), ('queued', 'Queued'), ('in_progress', 'In progress'), ('failed', 'Failed'), ('completed', 'Completed')], default='initialized', max_length=64),
        ),
        migrations.AlterField(
            model_name='gcsimportstorage',
            name='status',
            field=models.CharField(choices=[('initialized', 'Initialized'), ('queued', 'Queued'), ('in_progress', 'In progress'), ('failed', 'Failed'), ('completed', 'Completed')], default='initialized', max_length=64),
        ),
        migrations.AlterField(
            model_name='localfilesexportstorage',
            name='status',
            field=models.CharField(choices=[('initialized', 'Initialized'), ('queued', 'Queued'), ('in_progress', 'In progress'), ('failed', 'Failed'), ('completed', 'Completed')], default='initialized', max_length=64),
        ),
        migrations.AlterField(
            model_name='localfilesimportstorage',
            name='status',
            field=models.CharField(choices=[('initialized', 'Initialized'), ('queued', 'Queued'), ('in_progress', 'In progress'), ('failed', 'Failed'), ('completed', 'Completed')], default='initialized', max_length=64),
        ),
        migrations.AlterField(
            model_name='redisexportstorage',
            name='status',
            field=models.CharField(choices=[('initialized', 'Initialized'), ('queued', 'Queued'), ('in_progress', 'In progress'), ('failed', 'Failed'), ('completed', 'Completed')], default='initialized', max_length=64),
        ),
        migrations.AlterField(
            model_name='redisimportstorage',
            name='status',
            field=models.CharField(choices=[('initialized', 'Initialized'), ('queued', 'Queued'), ('in_progress', 'In progress'), ('failed', 'Failed'), ('completed', 'Completed')], default='initialized', max_length=64),
        ),
        migrations.AlterField(
            model_name='s3exportstorage',
            name='status',
            field=models.CharField(choices=[('initialized', 'Initialized'), ('queued', 'Queued'), ('in_progress', 'In progress'), ('failed', 'Failed'), ('completed', 'Completed')], default='initialized', max_length=64),
        ),
        migrations.AlterField(
            model_name='s3importstorage',
            name='status',
            field=models.CharField(choices=[('initialized', 'Initialized'), ('queued', 'Queued'), ('in_progress', 'In progress'), ('failed', 'Failed'), ('completed', 'Completed')], default='initialized', max_length=64),
        ),
    ]

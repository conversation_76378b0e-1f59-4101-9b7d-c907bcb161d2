# Generated by Django 3.2.14 on 2022-10-14 14:08

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('io_storages', '0009_auto_20220310_0922'),
    ]

    operations = [
        migrations.AddField(
            model_name='azureblobexportstorage',
            name='last_sync_job',
            field=models.CharField(blank=True, help_text='Last sync job ID', max_length=256, null=True, verbose_name='last_sync_job'),
        ),
        migrations.AddField(
            model_name='azureblobimportstorage',
            name='last_sync_job',
            field=models.Char<PERSON>ield(blank=True, help_text='Last sync job ID', max_length=256, null=True, verbose_name='last_sync_job'),
        ),
        migrations.AddField(
            model_name='gcsexportstorage',
            name='last_sync_job',
            field=models.CharField(blank=True, help_text='Last sync job ID', max_length=256, null=True, verbose_name='last_sync_job'),
        ),
        migrations.AddField(
            model_name='gcsimportstorage',
            name='last_sync_job',
            field=models.Char<PERSON>ield(blank=True, help_text='Last sync job ID', max_length=256, null=True, verbose_name='last_sync_job'),
        ),
        migrations.AddField(
            model_name='localfilesexportstorage',
            name='last_sync_job',
            field=models.CharField(blank=True, help_text='Last sync job ID', max_length=256, null=True, verbose_name='last_sync_job'),
        ),
        migrations.AddField(
            model_name='localfilesimportstorage',
            name='last_sync_job',
            field=models.CharField(blank=True, help_text='Last sync job ID', max_length=256, null=True, verbose_name='last_sync_job'),
        ),
        migrations.AddField(
            model_name='redisexportstorage',
            name='last_sync_job',
            field=models.CharField(blank=True, help_text='Last sync job ID', max_length=256, null=True, verbose_name='last_sync_job'),
        ),
        migrations.AddField(
            model_name='redisimportstorage',
            name='last_sync_job',
            field=models.CharField(blank=True, help_text='Last sync job ID', max_length=256, null=True, verbose_name='last_sync_job'),
        ),
        migrations.AddField(
            model_name='s3exportstorage',
            name='last_sync_job',
            field=models.CharField(blank=True, help_text='Last sync job ID', max_length=256, null=True, verbose_name='last_sync_job'),
        ),
        migrations.AddField(
            model_name='s3importstorage',
            name='last_sync_job',
            field=models.CharField(blank=True, help_text='Last sync job ID', max_length=256, null=True, verbose_name='last_sync_job'),
        ),
    ]

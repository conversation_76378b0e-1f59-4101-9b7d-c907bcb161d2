# Generated by Django 3.1.12 on 2021-08-11 16:29

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('io_storages', '0004_gcsstoragemixin_google_application_credentials'),
    ]

    operations = [
        migrations.AddField(
            model_name='s3importstorage',
            name='recursive_scan',
            field=models.BooleanField(default=False, help_text='Perform recursive scan over the bucket content', verbose_name='recursive scan'),
        ),
    ]

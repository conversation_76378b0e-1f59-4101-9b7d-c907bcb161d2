# 1x3 grid
title_bucket_prefix: &title_bucket_prefix
  - type: text
    name: title
    label: Storage Title
  - type: text
    name: bucket
    label: Bucket Name
    required: true
  - type: text
    name: prefix
    label: Bucket Prefix

# 2x3 grid
aws_params_import: &aws_params_import
  - type: text
    name: region_name
    label: Region Name
    placeholder: us-east-1
  - type: text
    name: s3_endpoint
    label: S3 Endpoint
  - null
  - type: password
    name: aws_access_key_id
    label: Access Key ID
    autoComplete: "off"
    skipAutofill: true
    allowEmpty: false
    protectedValue: true
  - type: password
    name: aws_secret_access_key
    label: Secret Access Key
    autoComplete: "new-password"
    skipAutofill: true
    allowEmpty: false
    protectedValue: true
  - type: password
    name: aws_session_token
    autoComplete: "new-password"
    label: Session Token
    skipAutofill: true
    allowEmpty: true
    protectedValue: true

# 2x3 grid
aws_params_export: &aws_params_export
  - type: text
    name: region_name
    label: Region Name
    placeholder: us-east-1
  - type: text
    name: s3_endpoint
    label: S3 Endpoint
  - type: text
    name: aws_sse_kms_key_id
    label: SSE KMS Key ID
  - type: password
    name: aws_access_key_id
    label: Access Key ID
    autoComplete: "off"
    skipAutofill: true
    allowEmpty: false
    protectedValue: true
  - type: password
    name: aws_secret_access_key
    label: Secret Access Key
    autoComplete: "new-password"
    skipAutofill: true
    allowEmpty: false
    protectedValue: true
  - type: password
    name: aws_session_token
    autoComplete: "new-password"
    label: Session Token
    skipAutofill: true
    allowEmpty: true
    protectedValue: true


ImportStorage:
  # Title, Bucket, Prefix
  - columnCount: 3
    fields: *title_bucket_prefix
  # Regex filter
  - columnCount: 1
    fields:
      - type: text
        name: regex_filter
        label: File Filter Regex
        placeholder: '.*csv or .*(jpe?g|png|tiff) or .\w+-\d+.text'
        validators:
          - regexp
  # AWS specific params
  - columnCount: 3
    fields: *aws_params_import

  # 2 columns grid
  - columnCount: 2
    columns:
      - width: 345
        fields:
          - type: toggle
            name: use_blob_urls
            label: Treat every bucket object as a source file
            description: If unchecked, treat every bucket object as a JSON-formatted task. Optional
          - type: toggle
            name: recursive_scan
            label: Recursive scan
            description: Perform recursive scan over the bucket content
      - fields:
          - type: toggle
            name: presign
            label: Use pre-signed URLs
            value: true
          - type: counter
            name: presign_ttl
            label: Expiration minutes
            min: 1
            value: 15
            dependency: presign

ExportStorage:
  - columnCount: 3
    fields: *title_bucket_prefix
  - columnCount: 3
    fields: *aws_params_export
  # 1 columns grid
  - columnCount: 1
    columns:
      - width: 345
        fields:
          - type: toggle
            name: can_delete_objects
            label: Can delete objects from storage
            description: If unchecked, annotations will not be deleted from storage

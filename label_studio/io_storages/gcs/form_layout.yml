# 1x3 grid
title_bucket_prefix: &title_bucket_prefix
  - type: text
    name: title
    label: Storage Title
  - type: text
    name: bucket
    label: Bucket Name
    required: true
  - type: text
    name: prefix
    label: Bucket Prefix

gcs_credentials: &gcs_credentials
  - type: password
    name: google_application_credentials
    label: Google Application Credentials, leave blank to use default credentials (ADC)
    placeholder: Paste the contents of credentials.json here OR leave blank to use ADC
    autoComplete: "new-password"
    skipAutofill: true
    allowEmpty: false
    validators:
      - json

project_id: &project_id
  - type: text
    name: google_project_id
    label: Google Project ID
    placeholder: 'Leave blank to inherit from the Google Application Credentials...'

ImportStorage:
  # Title, Bucket, Prefix
  - columnCount: 3
    fields: *title_bucket_prefix
  # Regex filter
  - columnCount: 1
    fields:
      - type: text
        name: regex_filter
        label: File Filter Regex
        placeholder: '.*csv or .*(jpe?g|png|tiff) or .\w+-\d+.text'
        validators:
          - regexp

  # 2 columns grid
  - columnCount: 2
    columns:
      - width: 345
        fields:
          - type: toggle
            name: use_blob_urls
            label: Treat every bucket object as a source file
            description: If unchecked, treat every bucket object as a JSON-formatted task. Optional
      - fields:
          - type: toggle
            name: presign
            label: Use pre-signed URLs
            value: true
          - type: counter
            name: presign_ttl
            label: Expiration minutes
            min: 1
            value: 15
            dependency: presign

  # GCS credentials
  - columnCount: 1
    fields: *gcs_credentials
  # Project ID
  - columnCount: 1
    fields: *project_id

ExportStorage:
  - columnCount: 3
    fields: *title_bucket_prefix
  - columnCount: 1
    fields: *gcs_credentials
  # Project ID
  - columnCount: 1
    fields: *project_id

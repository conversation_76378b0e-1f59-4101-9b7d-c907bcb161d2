# Generated by Django 3.2.25 on 2024-07-22 20:54

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('ml_model_providers', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='modelproviderconnection',
            name='deployment_name',
            field=models.CharField(blank=True, help_text='Azure OpenAI deployment name', max_length=512, null=True),
        ),
        migrations.AddField(
            model_name='modelproviderconnection',
            name='endpoint',
            field=models.Char<PERSON>ield(blank=True, help_text='Azure OpenAI endpoint', max_length=512, null=True),
        ),
        migrations.AlterField(
            model_name='modelproviderconnection',
            name='provider',
            field=models.CharField(choices=[('OpenAI', 'OpenAI'), ('AzureOpenAI', 'AzureOpenAI')], default='OpenAI', max_length=255),
        ),
    ]

node_modules
*.pyc
*.ipynb
__pycache__
.local/

# docs
__generated__
\#$
docs/data/
etc/
/src/
yarn-error.log
/dist/

# mobile/builds
db/
logfile
venv/
.venv
.vscode
.autoenv
.env
.envrc
# web/static

deploy/license.txt
deploy/logs/
deploy/redis-data/
deploy/dockerfiles/postgres-data
deploy/dockerfiles/mysql-data
postgres-data
mysql-data

label_studio/core/media
label_studio/core/downloads
label_studio/core/export

label_studio/core/static_build
label_studio/core/version_.py
label_studio/core/core

archive

*.rtl.css
*.rtl.min.css
*.idea/*
.vscode/*
pyrightconfig.json
tags.temp
mydatabase
label_studio.sqlite3
*.egg-info
tmp
.DS_Store
*.sqlite3
label_studio/core/settings/local.py
deploy/nginx/certs/*
!deploy/nginx/certs/.placeholder
mydata/
deploy/pgsql/certs/*
!deploy/pgsql/certs/.placeholder

*.swp
*.swo
*.swn
1/*
*/*/1/*
docker-compose.override.yml

.config/*
.python_history
.viminfo
.bash_history

.pdm-python

# actions-hub
.github/actions-hub

# Local Netlify folder
.netlify

services:
  nginx:
    build: .
    image: heartexlabs/label-studio:latest
    restart: unless-stopped
    ports:
      - "8080:8085"
      - "8081:8086"
    depends_on:
      - app
    environment:
      - LABEL_STUDIO_HOST=${LABEL_STUDIO_HOST:-}
    #   Optional: Specify SSL termination certificate & key
    #   Just drop your cert.pem and cert.key into folder 'deploy/nginx/certs'
    #      - NGINX_SSL_CERT=/certs/cert.pem
    #      - NGINX_SSL_CERT_KEY=/certs/cert.key
    volumes:
      - ./mydata:/label-studio/data:rw
      - ./deploy/nginx/certs:/certs:ro
    #   Optional: Override nginx default conf
    #      - ./deploy/my.conf:/etc/nginx/nginx.conf
    command: nginx

  app:
    stdin_open: true
    tty: true
    build: .
    image: heartexlabs/label-studio:latest
    restart: unless-stopped
    expose:
      - "8000"
    depends_on:
      - db
    environment:
      - DJANGO_DB=default
      - POSTGRE_NAME=postgres
      - POSTGRE_USER=postgres
      - POSTGRE_PASSWORD=
      - POSTGRE_PORT=5432
      - POSTGRE_HOST=db
      - LABEL_STUDIO_HOST=${LABEL_STUDIO_HOST:-}
      - JSON_LOG=1
    #      - LOG_LEVEL=DEBUG
    volumes:
      - ./mydata:/label-studio/data:rw
    command: label-studio-uwsgi

  db:
    image: pgautoupgrade/pgautoupgrade:13-alpine
    hostname: db
    restart: unless-stopped
    # Optional: Enable TLS on PostgreSQL
    # Just drop your server.crt and server.key into folder 'deploy/pgsql/certs'
    # NOTE: Both files must have permissions u=rw (0600) or less
    #    command: >
    #      -c ssl=on
    #      -c ssl_cert_file=/var/lib/postgresql/certs/server.crt
    #      -c ssl_key_file=/var/lib/postgresql/certs/server.key
    environment:
      - POSTGRES_HOST_AUTH_METHOD=trust
      - POSTGRES_USER=postgres
    volumes:
      - ${POSTGRES_DATA_DIR:-./postgres-data}:/var/lib/postgresql/data
      - ./deploy/pgsql/certs:/var/lib/postgresql/certs:ro

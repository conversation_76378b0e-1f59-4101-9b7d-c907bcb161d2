// For format details, see https://aka.ms/devcontainer.json. For config options, see the README at:
// https://github.com/microsoft/vscode-dev-containers/tree/v0.166.1/containers/docker-existing-docker-compose
// If you want to run as a non-root user in the container, see .devcontainer/docker-compose.yml.
{
	"name": "Existing Docker Compose (Extend)",
	// Update the 'dockerComposeFile' list if you have more compose files or use different names.
	// The .devcontainer/docker-compose.yml file contains any overrides you need/want to make.
	"dockerComposeFile": [
		"../docker-compose.yml",
		"docker-compose.yml"
	],
	// The 'service' property is the name of the service for the container that VS Code should
	// use. Update this value and .devcontainer/docker-compose.yml to the real service name.
	"service": "app",
	"runServices": [
		"db",
		"app",
	],
	// The optional 'workspaceFolder' property is the path VS Code should open by default when
	// connected. This is typically a file mount in .devcontainer/docker-compose.yml
	"workspaceFolder": "/workspace",
	// Set *default* container specific settings.json values on container create.
	"settings": {
		"terminal.integrated.defaultProfile.linux": "bash",
		"python.defaultInterpreterPath": "/usr/bin/python3",
		"python.pythonPath": "/usr/bin/python3",
		"files.eol": "\n",
		"editor.formatOnSave": true,
	},
	// Add the IDs of extensions you want installed when the container is created.
	"extensions": [
		"ms-python.python",
		"ms-python.vscode-pylance",
		"visualstudioexptteam.vscodeintellicode",
		"littlefoxteam.vscode-python-test-adapter",
		"kevinrose.vsc-python-indent",
		"shardulm94.trailing-spaces",
		"redhat.vscode-yaml",
		"oderwat.indent-rainbow",
		"medo64.render-crlf",
		"ms-azuretools.vscode-docker",
		"ms-vscode-remote.vscode-remote-extensionpack",
	],
	// Use 'forwardPorts' to make a list of ports inside the container available locally.
	// "forwardPorts": [],
	// Uncomment the next line if you want start specific services in your Docker Compose config.
	// "runServices": [],
	// Uncomment the next line if you want to keep your containers running after VS Code shuts down.
	// "shutdownAction": "none",
	// Uncomment the next line to run commands after the container is created - for example installing curl.
	//"postCreateCommand": "",
	// Uncomment to connect as a non-root user if you've added one. See https://aka.ms/vscode-remote/containers/non-root.
	// "remoteUser": "vscode"
}
name: Algolia Indexer for HumanSignal Docs

on:
  push:
    branches: ["develop"]
    paths:
      - "docs/**"
      - ".github/workflows/algolia-crawler-hs-docs.yml"

jobs:
  algolia_indexer:
    runs-on: ubuntu-latest
    env:
      APPLICATION_ID: "M7RXTHKYPM"
      API_KEY: ${{ secrets.ALGOLIA_ADMIN_API_KEY }}
      INDEX_NAME: "ghaction"
      CONFIG: >
        {"index_name": "ghaction",
        "stop_urls": ["https://docs.humansignal.com/guide/index.html", "https://docs.humansignal.com/guide/notion-faq.html", "https://labelstud.io/sdk/index.html"], 
        "selectors_exclude": [".home-page-index"],
        "sitemap_urls": ["https://docs.humansignal.com/guide/sitemap-docs.xml", "https://labelstud.io/sitemap-blog.xml"],
        "selectors": {
            "default": {
              "lvl0": ".content h1, .ResourcesBannerHeading, .BlogTitle",
              "lvl1": ".content h2, .ResourcesContent h1, .ResourcesContent h2",
              "lvl2": ".content h3, .ResourcesContent h3",
              "lvl3": ".content h4, .ResourcesContent h4",
              "lvl4": ".content h5, .ResourcesContent h5",
              "lvl5": ".content h6, .ResourcesContent h6", 
              "content": ".content-markdown > *, .ResourcesContent > .Text"
            }
          }
        }
    name: Index Algolia
    steps:
      - name: Algolia Docsearch Action
        id: algolia
        uses: adapttive/algolia-docsearch-action@1.1.1

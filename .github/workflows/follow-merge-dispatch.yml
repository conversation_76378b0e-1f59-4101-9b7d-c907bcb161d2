name: 'Follow Merge: Dispatch'

on:
  pull_request_target:
    types:
      - opened
      - closed
      - converted_to_draft
      - ready_for_review
      - synchronize
    branches:
      - develop
      - 'lse-release/**'
    paths-ignore:
      - 'label_studio/frontend/**'
      - 'docs/**'
#      - 'web/dist/**'

concurrency:
  group: ${{ github.workflow }}-${{ github.head_ref }}

jobs:
  sync:
    name: "Sync"
    if: startsWith(github.head_ref, 'fb-') || (startsWith(github.head_ref, 'revert-') && contains(github.head_ref, '-fb-') )
    runs-on: ubuntu-latest
    steps:
      - uses: hmarr/debug-action@v3.0.0

      - name: Check user's membership
        uses: actions/github-script@v7
        id: check-membership
        with:
          github-token: ${{ secrets.GIT_PAT }}
          script: |
            const { repo, owner } = context.repo;
            try {
              return (await github.rest.orgs.getMembershipForUser({
                org: owner,
                username: '${{ github.actor }}',
              }))?.data?.state == "active";
            } catch (error) {
              return false;
            }

      - name: Notify user on failure
        if: steps.check-membership.outputs.result == 'false'
        uses: actions/github-script@v7
        with:
          github-token: ${{ secrets.GIT_PAT }}
          script: |
            const { repo, owner } = context.repo;
            const result = await github.rest.issues.createComment({
              owner,
              repo,
              issue_number: '${{ github.event.number }}',
              body: [
                'Hi @${{ github.actor }}!',
                '',
                `Unfortunately you don't have membership in ${owner} organization, Follow Merge dispatch is skipped.`
              ].join('\n')
            });
            throw `${{ github.actor }} don't have membership in ${owner} organization`

      - name: Checkout Actions Hub
        uses: actions/checkout@v4
        with:
          token: ${{ secrets.GIT_PAT }}
          repository: HumanSignal/actions-hub
          path: ./.github/actions-hub

      - name: Dispatch label-studio-enterprise Follow Merge
        uses: ./.github/actions-hub/actions/follow-merge-dispatch
        with:
          github_token: ${{ secrets.GIT_PAT }}
          downstream_repository: "label-studio-enterprise"

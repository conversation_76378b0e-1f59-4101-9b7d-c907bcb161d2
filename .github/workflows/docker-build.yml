name: "Docker build & push"

on:
  workflow_call:
    inputs:
      sha:
        required: true
        type: string
      branch_name:
        required: true
        type: string
    outputs:
      image_version:
        description: "Docker branch tag"
        value: ${{ jobs.docker_build_and_push.outputs.image_version }}
      build_version:
        description: "Docker version tag"
        value: ${{ jobs.docker_build_and_push.outputs.build_version }}

env:
  DOCKER_CLI_EXPERIMENTAL: enabled
  IMAGE_NAME: "${{ vars.DOCKERHUB_ORG }}/label-studio"
  DOCKER_IMAGE_TAG_CHECK_NAME: "Docker image tag"
  POETRY_VERSION: 2.1.3

jobs:
  docker_build_and_push:
    name: "Docker image"
    timeout-minutes: 90
    runs-on: ubuntu-latest
    outputs:
      image_version: ${{ steps.version.outputs.image_version }}
      build_version: ${{ steps.version.outputs.build_version }}
    steps:
      - uses: hmarr/debug-action@v3.0.0

      - name: Check user's membership
        uses: actions/github-script@v7
        id: actor-membership
        env:
          ACTOR: ${{ github.actor }}
        with:
          github-token: ${{ secrets.GIT_PAT }}
          script: |
            const { repo, owner } = context.repo;
            const actor = process.env.ACTOR;
            const { data: membership } = await github.rest.orgs.getMembershipForUser({
              org: owner,
              username: actor,
            });
            core.setOutput("state", membership.state);
            core.setOutput("active", membership.state == "active");

      - name: Checkout
        uses: actions/checkout@v4
        with:
          submodules: 'recursive'
          ref: ${{ inputs.sha }}
          fetch-depth: 0

      - name: Calculate version
        id: version
        env:
          BRANCH_NAME: ${{ inputs.branch_name }}
        run: |
          set -x
          sha="$(git rev-parse HEAD)"
          echo "sha=$sha" >> $GITHUB_OUTPUT
           
          pretty_branch_name="$(echo -n "${BRANCH_NAME#refs/heads/}" | sed -E 's#[/_\.-]+#-#g' | tr '[:upper:]' '[:lower:]' | cut -c1-25 | sed -E 's#-$##g')"
          echo "pretty_branch_name=${pretty_branch_name}" >> "${GITHUB_OUTPUT}"

          regexp='^ls-release\/(.*)$';
          if [[ "$BRANCH_NAME" =~ $regexp ]]; then
            image_version="${BASH_REMATCH[1]}rc${sha}"
          else
            image_version="${pretty_branch_name}"
          fi
          echo "image_version=${image_version}" >> $GITHUB_OUTPUT
          echo "ubi_image_version=ubi_${image_version}" >> $GITHUB_OUTPUT
          
          current_time="$(date +'%Y%m%d.%H%M%S')"
          branch="-${pretty_branch_name}"
          short_sha="$(git rev-parse --short HEAD)"
          long_sha="$(git rev-parse HEAD)"
          echo "sha=$long_sha" >> $GITHUB_OUTPUT
          short_sha_length="$(echo $short_sha | awk '{print length}')"
          current_time_length="$(echo $current_time | awk '{print length}')"
          version="${current_time}$(echo $branch | cut -c1-$((50 - short_sha_length - current_time_length)))-${short_sha}"
          echo "build_version=$version" >> $GITHUB_OUTPUT

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3.10.0

      - name: Login to DockerHub
        uses: docker/login-action@v3.4.0
        with:
          username: ${{ vars.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}

      - name: Download LaunchDarkly Community config
        env:
          LAUNCHDARKLY_COMMUNITY_SDK_KEY: ${{ secrets.LAUNCHDARKLY_COMMUNITY_SDK_KEY }}
          LAUNCHDARKLY_DOWNLOAD_PATH: "label_studio/feature_flags.json"
        run: |
          set -xeuo pipefail
          curl \
            --connect-timeout 30 \
            --retry 5 \
            --retry-delay 10 \
            -H "Authorization: $LAUNCHDARKLY_COMMUNITY_SDK_KEY" \
            "https://sdk.launchdarkly.com/sdk/latest-all" >"$LAUNCHDARKLY_DOWNLOAD_PATH"
          if [ "$(jq 'has("flags")' <<< cat $LAUNCHDARKLY_DOWNLOAD_PATH)" = "true" ]; then 
            echo "feature_flags.json is valid"
          else
            echo "feature_flags.json is invalid"
            cat $LAUNCHDARKLY_DOWNLOAD_PATH
            exit 1
          fi

      - name: Extract Docker metadata
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.IMAGE_NAME }}
          labels: |
            org.opencontainers.image.revision=${{ inputs.sha }}
          tags: |
            type=raw,value=${{ steps.version.outputs.image_version }}
            type=raw,value=${{ steps.version.outputs.build_version }}

      - name: Push Docker image
        uses: docker/build-push-action@v6.17.0
        id: docker_build_and_push
        with:
          context: .
          file: Dockerfile
          platforms: linux/amd64,linux/arm64
          push: ${{ steps.actor-membership.outputs.active }}
          sbom: true
          provenance: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max
          build-args: |
            BRANCH_OVERRIDE=${{ inputs.branch_name }}
            VERSION_OVERRIDE=${{ steps.version.outputs.build_version }}
            POETRY_VERSION=${{ env.POETRY_VERSION }}

      - name: Create Docker image tag Check
        uses: actions/github-script@v7
        with:
          script: |
            const { repo, owner } = context.repo;
            const details = {
              "branch": "${{ inputs.branch_name }}",
              "pretty_branch_name": "${{ steps.version.outputs.pretty_branch_name }}",
              "image_version": "${{ steps.version.outputs.image_version }}",
              "sha": "${{ steps.version.outputs.sha }}"
            }
            const { data: check } = await github.rest.checks.create({
              owner,
              repo,
              name: '${{ env.DOCKER_IMAGE_TAG_CHECK_NAME }}',
              head_sha: '${{ steps.version.outputs.sha }}',
              status: 'in_progress',
              output: {
                title: '${{ env.DOCKER_IMAGE_TAG_CHECK_NAME }}',
                summary: JSON.stringify(details)
              }
            });
            await github.rest.checks.update({
              owner,
              repo,
              check_run_id: check.id,
              status: 'completed',
              conclusion: 'success'
            });

      - name: Notify to Slack on failure
        if: failure() && github.event_name == 'push' && inputs.branch_name == 'develop'
        env:
          GITHUB_REPOSITORY: "${{ github.repository }}"
          GITHUB_RUN_ID: "${{ github.run_id }}"
          GITHUB_SHA: "${{ github.sha }}"
          SLACK_BOT_TOKEN: ${{ secrets.SLACK_LSE_BOT_TOKEN }}
        uses: slackapi/slack-github-action@v1.27
        with:
          channel-id: 'C01RJV08UJK'
          slack-message: |
            ❌ Docker build failed for *develop* branch! <!subteam^${{ vars.SLACK_GR_DEVOPS }}> 

            ><https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }}|[Workflow run]>

name: "CodeQL"

on:
  push:
    branches: [ "develop", "ls-release/**", "lse-release/**" ]
  schedule:
    - cron: '35 22 * * 0'

jobs:
  analyze:
    name: Analyze
    runs-on: 'ubuntu-latest'
    timeout-minutes: 45
    permissions:
      actions: read
      contents: read
      security-events: write

    strategy:
      fail-fast: false
      matrix:
        language: [ 'javascript-typescript', 'python' ]

    steps:
    - name: Checkout repository
      uses: actions/checkout@v4

    - name: Initialize CodeQL
      uses: github/codeql-action/init@v3
      with:
        languages: ${{ matrix.language }}
        config-file: ./.github/codeql/codeql-config.yaml

    - name: Perform CodeQL Analysis
      uses: github/codeql-action/analyze@v3
      with:
        category: "/language:${{matrix.language}}"

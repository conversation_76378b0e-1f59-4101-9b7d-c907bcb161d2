name: 'Follow Merge: Set LSE Check for non Follow Merge PR'

on:
  pull_request:
    types:
      - opened
      - synchronize
      - reopened
      - ready_for_review
    branches:
      - develop
      - 'ls-release/**'

jobs:
  set_lse_check:
    name: "Set LSE Check"
    if: "${{ ! ( startsWith(github.head_ref, 'fb-') || (startsWith(github.head_ref, 'revert-') && contains(github.head_ref, '-fb-') ) ) }}"
    runs-on: ubuntu-latest
    steps:
      - uses: hmarr/debug-action@v3.0.0

      - name: Set Label Studio status
        uses: actions/github-script@v7
        env:
          SHA: "${{ github.event.pull_request.head.sha || github.event.after }}"
          WORKFLOW_RUN_LINK: "https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }}"
        with:
          github-token: ${{ secrets.GIT_PAT }}
          script: |
            const {repo, owner} = context.repo;
            const sha = process.env.SHA;
            const workflow_run_link = process.env.WORKFLOW_RUN_LINK;
            
            const { data: commit_status} = await github.rest.repos.createCommitStatus({
              owner,
              repo,
              sha: sha,
              state: "success",
              context: "External / LSE PyTest",
              description: "",
              target_url: workflow_run_link,
            });
            console.log(`Commit Status created: ${commit_status.url}`);

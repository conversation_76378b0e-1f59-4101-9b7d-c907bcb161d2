name: "Conda"

on:
  workflow_call:
    inputs:
      head_sha:
        required: true
        type: string

env:
  CACHE_NAME_PREFIX: v1
  POETRY_VERSION: 2.1.3

jobs:
  conda:
    name: Conda
    runs-on: ${{ matrix.os }}
    strategy:
      fail-fast: false
      matrix:
        os: [ "ubuntu-latest", "windows-latest" ]
        python-version: [ "3.12" ]
        include:
          - os: "ubuntu-latest"
            pip-cache-path: "~/.cache/pip"
          - os: "windows-latest"
            pip-cache-path: "~\\AppData\\Local\\pip\\Cache"
    # required for poetry action
    # see https://github.com/marketplace/actions/install-poetry-action#running-on-windows
    defaults:
      run:
        shell: bash

    steps:
      - uses: hmarr/debug-action@v3.0.0

      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 1
          ref: ${{ inputs.head_sha }}

      - name: Setup Conda
        uses: conda-incubator/setup-miniconda@v3.1.1
        with:
          auto-update-conda: true
          python-version: ${{ matrix.python-version }}
          activate-environment: test-environment

      - name: "Install poetry"
        run: pipx install "poetry==${{ env.POETRY_VERSION }}"

      - name: "Set up Python"
        id: setup_python
        uses: actions/setup-python@v5
        with:
          python-version: '3.12'
          cache: 'poetry'

      - name: Install Python dependencies
        run: |
          poetry install --with test

      - name: Conda info
        shell: bash -el {0}
        run: |
          conda info
          conda list

name: "Check"

on:
  pull_request_target:
    types:
      - opened
      - edited
      - reopened
      - synchronize
      - ready_for_review
    branches:
      - master
      - develop
      - 'release/**'
      - 'lse-release/**'
      - 'ls-release/**'

env:
  ACTIONS_STEP_DEBUG: '${{ secrets.ACTIONS_STEP_DEBUG }}'

jobs:
  autolabel:
    name: "PR label validator"
    runs-on: ubuntu-latest
    permissions:
      pull-requests: write
    steps:

      - uses: hmarr/debug-action@v3.0.0

      - name: "Validate PR's title"
        uses: thehanimo/pr-title-checker@v1.4.3
        with:
          GITHUB_TOKEN: ${{ github.token }}
          pass_on_octokit_error: false
          configuration_path: ".github/pr-title-checker-config.json"

      - name: "Set PR's label based on title"
        uses: release-drafter/release-drafter@v6.1.0
        with:
          disable-releaser: true
          config-name: autolabeler.yml
        env:
          GITHUB_TOKEN: ${{ github.token }}

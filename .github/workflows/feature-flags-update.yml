name: "Update Feature Flags"

on:
  schedule:
    - cron: '0 6 * * 1-5'
  workflow_call:
    inputs:
      ref:
        default: develop
        required: true
        type: string
  workflow_dispatch:
    inputs:
      ref:
        description: "Ref"
        default: develop
        required: true
        type: string

env:
  LAUNCHDARKLY_DOWNLOAD_PATH: "label_studio/feature_flags.json"
  FEATURE_FLAGS_COMMIT_MESSAGE: "ci: Update Feature Flags"

jobs:
  commit-feature-flags:
    name: "Commit Feature Flags"

    runs-on: ubuntu-latest
    steps:
      - uses: hmarr/debug-action@v3.0.0

      - name: Add Workflow link to chat ops command comment
        if: github.event.client_payload.github.payload.comment.id && github.event.client_payload.github.payload.repository.full_name
        uses: peter-evans/create-or-update-comment@v4
        with:
          token: ${{ secrets.GIT_PAT }}
          repository: ${{ github.event.client_payload.github.payload.repository.full_name }}
          comment-id: ${{ github.event.client_payload.github.payload.comment.id }}
          body: |
            > [Workflow run](https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }})

      - name: Configure git
        shell: bash
        run: |
          set -xeuo pipefail
          git config --global user.name 'robot-ci-heartex'
          git config --global user.email '<EMAIL>'

      - name: Checkout
        uses: actions/checkout@v4
        with:
          token: ${{ secrets.GIT_PAT }}
          fetch-depth: 1
          ref: ${{ inputs.ref }}

      - name: Download feature flags
        env:
          LAUNCHDARKLY_COMMUNITY_SDK_KEY: ${{ secrets.LAUNCHDARKLY_COMMUNITY_SDK_KEY }}
        run: |
          set -xeuo pipefail
          curl \
            --connect-timeout 30 \
            --retry 5 \
            --retry-delay 10 \
            -H "Authorization: $LAUNCHDARKLY_COMMUNITY_SDK_KEY" \
            "https://sdk.launchdarkly.com/sdk/latest-all" | jq >"${{ env.LAUNCHDARKLY_DOWNLOAD_PATH }}"
          if [ "$(jq 'has("flags")' <<< cat ${{ env.LAUNCHDARKLY_DOWNLOAD_PATH }})" = "true" ]; then 
            echo "feature_flags.json is valid"
          else
            echo "feature_flags.json is invalid"
            cat ${{ env.LAUNCHDARKLY_DOWNLOAD_PATH }}
            exit 1
          fi

      - name: Commit and Push
        run: |
          git add "${{ env.LAUNCHDARKLY_DOWNLOAD_PATH }}"
          git status -s
          git commit -m '${{ env.FEATURE_FLAGS_COMMIT_MESSAGE }}' -m 'Workflow run: https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }}' || true
          git push origin HEAD

      - name: Add reaction to chat ops command comment
        if: always() && github.event.client_payload.github.payload.comment.id && github.event.client_payload.github.payload.repository.full_name
        uses: peter-evans/create-or-update-comment@v4
        with:
          token: ${{ secrets.GIT_PAT }}
          repository: ${{ github.event.client_payload.github.payload.repository.full_name }}
          comment-id: ${{ github.event.client_payload.github.payload.comment.id }}
          reactions: ${{ job.status == 'success' && '+1' || '-1' }}

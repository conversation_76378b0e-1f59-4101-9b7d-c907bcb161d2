name: "ZenDesk: Comment GitHub Issue on Zendesk Ticket Comment"

on:
  workflow_dispatch:
    inputs:
      external_id:
        description: "GitHub issue url"
        required: true
        type: string
      custom_field:
        description: "Space separated list of labels"
        required: false
        default: ""
        type: string
      comment_body:
        description: "Zendesk comment body"
        required: true
        type: string
      author:
        description: "Zendesk comment author"
        required: false
        default: ""
        type: string

jobs:
  process_comment_and_labels:
    runs-on: ubuntu-latest
    steps:
      - uses: hmarr/debug-action@v3.0.0

      - uses: actions/github-script@v7
        env:
          WORKFLOW_RUN_LINK: "https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }}"
        with:
          github-token: ${{ secrets.GIT_PAT_HEIDI }}
          script: |
            // Extract issue details from the Zendesk external_id
            const parts = context.payload.inputs.external_id.split("/");
            const issue_number = parts[parts.length - 1];
            const issue_repo = parts[parts.length - 3];
            const issue_owner = parts[parts.length - 4];

            // Extract comment details
            const comment_author = context.payload.inputs.author || "HumanSignal Support";
            const comment_body = context.payload.inputs.comment_body;
            const formatted_comment_body =
            `${comment_body}
            
            > Comment by ${comment_author}
            > [Workflow Run](${process.env.WORKFLOW_RUN_LINK})`;

            // Add a comment to the GitHub issue
            if (comment_body.startsWith('[GITHUB_ISSUE_')) {
              core.notice(`Skipping comment creation.`);
            } else {
              const { data: comment } = await github.rest.issues.createComment({
                owner: issue_owner,
                repo: issue_repo,
                issue_number: issue_number,
                body: formatted_comment_body
              });
              core.notice(`Comment created ${comment.html_url}`);
            }

            // Extract labels from the custom_field
            let new_labels = [];
            if (context.payload.inputs.custom_field) {
              new_labels = context.payload.inputs.custom_field.split(" ").map(label => label.trim());
            }

            // Get the current labels on the GitHub issue
            const { data: current_labels } = await github.rest.issues.listLabelsOnIssue({
              owner: issue_owner,
              repo: issue_repo,
              issue_number: issue_number
            });

            const current_label_names = current_labels.map(label => label.name);

            // Labels to be added
            const labels_to_add = new_labels.filter(label => !current_label_names.includes(label));

            // Labels to be removed
            const labels_to_remove = current_label_names.filter(label => !new_labels.includes(label));

            // Remove labels that are not in the new labels list
            for (const label of labels_to_remove) {
              await github.rest.issues.removeLabel({
                owner: issue_owner,
                repo: issue_repo,
                issue_number: issue_number,
                name: label
              });
            }

            // Add the new labels
            if (labels_to_add.length > 0) {
              await github.rest.issues.addLabels({
                owner: issue_owner,
                repo: issue_repo,
                issue_number: issue_number,
                labels: labels_to_add
              });
            }

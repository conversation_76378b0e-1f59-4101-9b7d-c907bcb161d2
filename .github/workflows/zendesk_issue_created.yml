name: "ZenDesk: Create a zendesk ticket out of an issue"

on:
  issues:
    types:
      - opened

jobs:
  issue_created:
    name: Issue created
    runs-on: ubuntu-latest
    steps:
      - uses: hmarr/debug-action@v3.0.0

      - env:
          ZENDESK_HOST: ${{ vars.ZENDESK_HOST }}
          ZENDESK_USER: ${{ vars.ZENDESK_USER }}
          ZENDESK_TOKEN: ${{ secrets.ZENDESK_TOKEN }}
          ISSUE_TITLE: ${{ github.event.issue.title }}
          ISSUE_BODY: ${{ github.event.issue.body }}
          ISSUE_USER: ${{ github.event.issue.user.login }}
          ISSUE_URL: ${{ github.event.issue.html_url }}
          WORKFLOW_RUN_LINK: "https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }}"
          REPO_NAME: ${{ github.event.repository.name }}
        run: |
          body=$(jq -n --arg body "$ISSUE_BODY" '{body: $body}' | jq .body)
          echo "$body"
          
          curl https://${ZENDESK_HOST}/api/v2/tickets \
            --request POST \
            --user "${ZENDESK_USER}/token:${ZENDESK_TOKEN}" \
            --header "Content-Type: application/json" \
            --data-binary @- <<DATA
          {
            "ticket": {
              "subject": "Github_Issue - ${REPO_NAME}: ${ISSUE_TITLE:0:35}",
              "comment": {
                "body": "[GITHUB_ISSUE_DESCRIPTION]\n\n${body:1:-1}\n\nGITHUB ISSUE URL: ${ISSUE_URL}\nWORKFLOW RUN: ${WORKFLOW_RUN_LINK}"
              },
              "tags": ["gh-issue"],
              "external_id": "$ISSUE_URL",
              "requester": {
                "locale_id": 1,
                "name": "$ISSUE_USER from Github",
                "email": "$<EMAIL>"
              }
            }
          }
          DATA

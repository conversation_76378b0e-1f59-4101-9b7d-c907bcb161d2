name: Apply linters

on:
  workflow_dispatch:
    inputs:
      branch_name:
        description: 'Branch name to run the workflow on'
        required: true
        type: string

env:
  NODE: "18"
  FRONTEND_MONOREPO_DIR: "web"

jobs:
  lint:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          token: ${{ secrets.GIT_PAT }}
          ref: ${{ inputs.branch_name }}

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: '3.12'

      - name: Install pre-commit
        run: |
          python -m pip install --upgrade pip
          pip install pre-commit

      - name: Setup frontend environment
        uses: ./.github/actions/setup-frontend-environment
        with:
          node-version: "${{ env.NODE }}"
          directory: "${{ env.FRONTEND_MONOREPO_DIR }}"

      - name: Run formatters
        run: make fmt-all || true

      - name: Ensure no lint remains
        run: make fmt-all

      - name: Commit changes
        run: |
          git config --global user.name "robot-ci-heartex"
          git config --global user.email "<EMAIL>"
          git add .
          git commit -m "Apply pre-commit linters" || echo "No changes to commit"
          git push

name: 'Follow Merge: Upstream repo sync v2'

on:
  workflow_call:
    inputs:
      branch_name:
        required: true
        type: string
  workflow_dispatch:
    inputs:
      branch_name:
        description: 'Branch name'
        required: true
        type: string

concurrency:
  group: ${{ github.workflow }}-${{ inputs.branch_name }}
  cancel-in-progress: true

env:
  POETRY_VERSION: 2.1.3

jobs:
  sync:
    name: Sync PR
    runs-on: ubuntu-latest
    outputs:
      label-studio-sdk: "${{ fromJSON(steps.upstream-prs.outputs.shas).label-studio-sdk }}"
    steps:
      - uses: hmarr/debug-action@v3.0.0

      - name: Add Workflow link to chat ops command comment
        if: github.event.client_payload.github.payload.comment.id && github.event.client_payload.github.payload.repository.full_name
        uses: peter-evans/create-or-update-comment@v4
        with:
          token: ${{ secrets.GIT_PAT }}
          repository: ${{ github.event.client_payload.github.payload.repository.full_name }}
          comment-id: ${{ github.event.client_payload.github.payload.comment.id }}
          body: |
            > [Workflow run](https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }})

      - name: Checkout Actions Hub
        uses: actions/checkout@v4
        with:
          token: ${{ secrets.GIT_PAT }}
          repository: HumanSignal/actions-hub
          path: ./.github/actions-hub

      - name: Team details
        id: team_details
        uses: ./.github/actions-hub/actions/team-details
        with:
          jira-server: "${{ vars.JIRA_SERVER }}"
          jira-username: "${{ secrets.JIRA_USERNAME }}"
          jira-token: "${{ secrets.JIRA_TOKEN }}"
          github_token: "${{ secrets.GIT_PAT }}"
        continue-on-error: true

      - name: Git Configure
        uses: ./.github/actions-hub/actions/git-configure
        with:
          username: "${{ steps.team_details.outputs.github-username }}"

      - name: Get Upstream PRs
        uses: ./.github/actions-hub/actions/follow-merge-upstream-prs
        id: upstream-prs
        with:
          branch_name: "${{ inputs.branch_name }}"
          poetry_repositories: "label-studio-sdk"
          infra_repositories: ""
          github_token: "${{ secrets.GIT_PAT }}"

      - name: Find or Create branch
        uses: ./.github/actions-hub/actions/github-find-or-create-branch
        id: get-branch
        with:
          github_token: ${{ secrets.GIT_PAT }}
          base_branch_name: "${{ steps.upstream-prs.outputs.base_branch_name }}"
          branch_name: "${{ steps.upstream-prs.outputs.branch_name }}"

      - name: Checkout
        uses: actions/checkout@v4
        with:
          token: ${{ secrets.GIT_PAT }}
          ref: ${{ steps.get-branch.outputs.branch_name }}
          fetch-depth: 0

      - name: Checkout Actions Hub
        uses: actions/checkout@v4
        with:
          token: ${{ secrets.GIT_PAT }}
          repository: HumanSignal/actions-hub
          path: ./.github/actions-hub

      - name: "Install poetry"
        run: pipx install "poetry==${{ env.POETRY_VERSION }}"

      - name: "Set up Python"
        id: setup_python
        uses: actions/setup-python@v5
        with:
          python-version: '3.12'
          cache: 'poetry'

      - name: Commit submodule
        shell: bash
        env:
          SDK_SHA: "${{ fromJSON(steps.upstream-prs.outputs.shas).label-studio-sdk }}"
          COMMIT_MESSAGE: "Sync Follow Merge dependencies"
          COMMIT_MESSAGE_WORKFLOW_LINK: "Workflow run: https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }}"
        run: |
          set -xeuo pipefail
          
          if [[ -n "${SDK_SHA}" ]]; then
            poetry add "https://github.com/HumanSignal/label-studio-sdk/archive/${SDK_SHA}.zip" --lock
          fi
          
          poetry lock
          
          git diff
          git add -A
          git status -s
          git commit --allow-empty -m "${COMMIT_MESSAGE}" -m "${COMMIT_MESSAGE_WORKFLOW_LINK}"

      - name: Git Merge
        id: merge
        continue-on-error: true
        uses: ./.github/actions-hub/actions/git-merge
        with:
          base_branch: ${{ steps.upstream-prs.outputs.base_branch_name }}
          head_branch: ${{ steps.get-branch.outputs.branch_name }}
          our_files: "pyproject.toml poetry.lock web/package.json web/yarn.lock"

      - name: Git Push
        uses: ./.github/actions-hub/actions/git-push

      - name: Find or Create PR
        uses: ./.github/actions-hub/actions/follow-merge-find-or-create-pull-request
        id: get-pr
        with:
          github_token: ${{ secrets.GIT_PAT }}
          branch_name: "${{ steps.get-branch.outputs.branch_name }}"
          base_branch_name: "${{ steps.upstream-prs.outputs.base_branch_name }}"
          title: "${{ steps.upstream-prs.outputs.title }}"
          upstream_prs_urls: "${{ steps.upstream-prs.outputs.upstream_prs_urls }}"

      - name: Add PR Assignees
        if: steps.upstream-prs.outputs.assignees
        uses: ./.github/actions-hub/actions/github-add-pull-request-assignees
        continue-on-error: true
        with:
          github_token: ${{ secrets.GIT_PAT }}
          pullrequest_number: "${{ steps.get-pr.outputs.number }}"
          assignees: "${{ steps.upstream-prs.outputs.assignees }}"

      - name: Add PR state Labels
        if: steps.upstream-prs.outputs.status == 'stale'
        uses: ./.github/actions-hub/actions/github-add-pull-request-labels
        continue-on-error: true
        with:
          github_token: ${{ secrets.GIT_PAT }}
          pullrequest_number: "${{ steps.get-pr.outputs.number }}"
          labels: "FM Stale"

      - name: Convert to ready for review
        if: steps.upstream-prs.outputs.status == 'merged' || steps.upstream-prs.outputs.status == 'stale'
        id: ready-for-review-pr
        shell: bash
        env:
          GIT_PAT: "${{ secrets.GIT_PAT }}"
        run: |
          echo "$GIT_PAT" | gh auth login --with-token
          gh api graphql -F id='${{ steps.get-pr.outputs.node_id }}' -f query='
            mutation($id: ID!) {
              markPullRequestReadyForReview(input: { pullRequestId: $id }) {
                pullRequest {
                  id
                }
              }
            }
          '

      - name: Merge Stale PR
        if: steps.upstream-prs.outputs.status == 'stale'
        continue-on-error: true
        shell: bash
        env:
          GIT_PAT: "${{ secrets.GIT_PAT }}"
          BRANCH_NAME: "origin/${{ steps.get-branch.outputs.branch_name }}"
          BASE_BRANCH_NAME: "origin/${{ steps.upstream-prs.outputs.base_branch_name }}"
          PR_NUMBER: "${{ steps.get-pr.outputs.number }}"
          TITLE: "${{ steps.get-pr.outputs.title }}"
        run: |
          if git diff --quiet "${BASE_BRANCH_NAME}..${BRANCH_NAME}"; then
            echo "No changes detected."
            echo "Merging PR as a Stale."
            echo "$GIT_PAT" | gh auth login --with-token
            gh pr merge "${PR_NUMBER}" --squash --admin --subject "${TITLE} (stale) (#${PR_NUMBER})"
          else
            echo "Changes detected in PR."
          fi

      - name: Enable AutoMerge
        if: steps.upstream-prs.outputs.status == 'merged'
        continue-on-error: true
        shell: bash
        env:
          GIT_PAT: ${{ secrets.GIT_PAT }}
        run: |
          echo "$GIT_PAT" | gh auth login --with-token
          gh api graphql -f pull='${{ steps.get-pr.outputs.node_id }}' -f query='
            mutation($pull: ID!) {
              enablePullRequestAutoMerge(input: {pullRequestId: $pull, mergeMethod: SQUASH}) {
                pullRequest {
                  id
                  number
                }
              }
            }'

      - name: Close PR
        if: steps.upstream-prs.outputs.status == 'closed'
        continue-on-error: true
        shell: bash
        env:
          GIT_PAT: ${{ secrets.GIT_PAT }}
        run: |
          echo "$GIT_PAT" | gh auth login --with-token
          gh api graphql -f pull='${{ steps.get-pr.outputs.node_id }}' -f query='
            mutation($pull: ID!) {
              closePullRequest(input: {pullRequestId: $pull }) {
                pullRequest {
                  id
                  state
                }
              }
            }'

  #      - name: Notify on failure
  #        uses: ./.github/actions-hub/actions/github-create-comment
  #        if: failure()
  #        with:
  #          github_token: ${{ secrets.GIT_PAT }}
  #          repository: "${{ steps.fm.outputs.repo_name }}"
  #          issue_number: "${{ steps.fm.outputs.pr_number }}"
  #          body: |
  #            Follow Merge downstream workflow has been failed.
  #            > [Workflow run](https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }})

      - name: Add reaction to chat ops command comment
        if: always() && github.event.client_payload.github.payload.comment.id && github.event.client_payload.github.payload.repository.full_name
        uses: peter-evans/create-or-update-comment@v4
        with:
          token: ${{ secrets.GIT_PAT }}
          repository: ${{ github.event.client_payload.github.payload.repository.full_name }}
          comment-id: ${{ github.event.client_payload.github.payload.comment.id }}
          reactions: ${{ job.status == 'success' && '+1' || '-1' }}

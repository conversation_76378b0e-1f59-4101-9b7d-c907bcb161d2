name: "/git command"

on:
  repository_dispatch:
    types: [ git-command ]

concurrency:
  group: ${{ github.workflow }}-${{ github.event.client_payload.github.payload.issue.number }}-${{ github.event.client_payload.slash_command.command }}-${{ github.event.client_payload.slash_command.args.unnamed.arg1 || github.event.client_payload.slash_command.args.all }}

jobs:
  merge:
    if: ${{ github.event.client_payload.slash_command.args.unnamed.arg1 == 'merge' }}
    runs-on: ubuntu-latest
    timeout-minutes: 10
    steps:
      - uses: hmarr/debug-action@v3.0.0

      - name: Add Workflow link to command comment
        uses: peter-evans/create-or-update-comment@v4
        with:
          token: ${{ secrets.GIT_PAT }}
          repository: ${{ github.event.client_payload.github.payload.repository.full_name }}
          comment-id: ${{ github.event.client_payload.github.payload.comment.id }}
          body: |
            > [Workflow run](https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }})

      - name: Checkout
        uses: actions/checkout@v4
        with:
          token: ${{ secrets.GIT_PAT }}
          repository: ${{ github.event.client_payload.pull_request.head.repo.full_name }}
          ref: ${{ github.event.client_payload.pull_request.head.ref }}
          submodules: 'recursive'
          fetch-depth: 0

      - name: Checkout Actions Hub
        uses: actions/checkout@v4
        with:
          token: ${{ secrets.GIT_PAT }}
          repository: HumanSignal/actions-hub
          path: ./.github/actions-hub

      - name: Git Configure
        uses: ./.github/actions-hub/actions/git-configure
        with:
          username: ${{ github.event.client_payload.github.actor }}

# Merge

      - name: Git Merge
        id: merge
        uses: ./.github/actions-hub/actions/git-merge
        with:
          base_branch: ${{ github.event.client_payload.slash_command.args.unnamed.arg2 || github.event.client_payload.pull_request.base.ref }}
          head_branch: ${{ github.event.client_payload.pull_request.head.ref }}
          our_files: "pyproject.toml poetry.lock web"

      - name: Git Push
        if: steps.merge.outputs.result == 'true'
        uses: ./.github/actions-hub/actions/git-push

      - name: Add reaction to command comment
        uses: peter-evans/create-or-update-comment@v4
        if: always()
        with:
          token: ${{ secrets.GIT_PAT }}
          repository: ${{ github.event.client_payload.github.payload.repository.full_name }}
          comment-id: ${{ github.event.client_payload.github.payload.comment.id }}
          body: |
            > ${{ steps.merge.outputs.message || '**Error**: Workflow failed' }}
          reactions: ${{ steps.merge.outputs.reaction || '-1' }}

  help:
    if: ${{ github.event.client_payload.slash_command.args.unnamed.arg1 == 'help' || !contains(fromJson('["merge"]'), github.event.client_payload.slash_command.args.unnamed.arg1) }}
    runs-on: ubuntu-latest
    timeout-minutes: 1
    steps:
      - name: Update comment
        uses: peter-evans/create-or-update-comment@v4
        with:
          token: ${{ secrets.GIT_PAT }}
          repository: ${{ github.event.client_payload.github.payload.repository.full_name }}
          comment-id: ${{ github.event.client_payload.github.payload.comment.id }}
          body: |
            > Command | Description
            > --- | ---
            > /git merge `branch` | Merge branch `branch` into current branch
          reactions: hooray

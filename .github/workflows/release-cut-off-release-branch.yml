name: "Release: Cut off release branch"

on:
  workflow_dispatch:
    inputs:
      version:
        description: 'Release version'
        required: true
        type: string
      ref:
        description: 'Commit SHA or ref name or tag'
        required: true
        default: 'develop'
        type: string

env:
  RELEASE_BRANCH_PREFIX: "ls-release/"

jobs:
  draft-new-release:
    name: "Draft new release/hotfix"
    runs-on: ubuntu-latest
    outputs:
      next_develop_version: ${{ steps.calculate_branch_name_and_version.outputs.next_develop_version }}
      release_version: ${{ steps.calculate_branch_name_and_version.outputs.release_version }}
      release_branch: ${{ steps.calculate_branch_name_and_version.outputs.release_branch }}
    steps:
      - uses: hmarr/debug-action@v3.0.0

      - name: Checkout
        uses: actions/checkout@v4
        with:
          token: ${{ secrets.GIT_PAT }}
          ref: ${{ inputs.ref }}
          submodules: 'recursive'
          fetch-depth: 0

      - name: Configure git
        shell: bash
        run: |
          set -xeuo pipefail
          git config --global user.name 'robot-ci-heartex'
          git config --global user.email '<EMAIL>'

      - name: Calculate branch name and version
        id: calculate_branch_name_and_version
        shell: bash
        env:
          VERSION: "${{ inputs.version }}"
        run: |
          set -xeuo pipefail
          
          regexp='^[v]?([0-9]+)\.([0-9]+)\.0$';
          
          if [[ "${VERSION}" =~ $regexp ]]; then
              first="${BASH_REMATCH[1]}"
              second="${BASH_REMATCH[2]}"
              third="0"
          else
              echo "::error::${VERSION} does not mach the regexp ${regexp}"
              exit 1
          fi
          
          release_version="${first}.${second}.${third}"
          release_branch="${{ env.RELEASE_BRANCH_PREFIX }}${first}.${second}.${third}"
          next_develop_version="${first}.$(($second + 1)).0.dev0"
          
          echo "release_branch=${release_branch}" >> "${GITHUB_OUTPUT}"
          echo "release_version=${release_version}" >> "${GITHUB_OUTPUT}"
          echo "next_develop_version=${next_develop_version}" >> "${GITHUB_OUTPUT}"

      - name: Cut release branch
        shell: bash
        run: |
          set -xeuo pipefail
          
          git checkout -b "${{ steps.calculate_branch_name_and_version.outputs.release_branch }}"
          echo "commit=$(git rev-parse HEAD)" >> $GITHUB_OUTPUT
          git push origin HEAD:refs/heads/${{ steps.calculate_branch_name_and_version.outputs.release_branch }}

  set-version-default-branch:
    name: 'Set version in default branch'
    uses: ./.github/workflows/release-set-version.yml
    needs:
      - draft-new-release
    with:
      version: ${{ needs.draft-new-release.outputs.next_develop_version }}
      branch: ${{ github.event.repository.default_branch }}
    permissions:
      contents: write
    secrets: inherit

  update-feature-flags:
    name: "Update feature flags in release branch"
    needs:
      - draft-new-release
    permissions:
      contents: write
    uses: ./.github/workflows/feature-flags-update.yml
    with:
      ref: ${{ needs.draft-new-release.outputs.release_branch }}
    secrets: inherit

  set-version-release-branch:
    name: 'Set version in release branch'
    uses: ./.github/workflows/release-set-version.yml
    needs:
      - update-feature-flags
      - draft-new-release
    with:
      version: ${{ needs.draft-new-release.outputs.release_version }}
      branch: ${{ needs.draft-new-release.outputs.release_branch }}
    permissions:
      contents: write
    secrets: inherit

name: "/ff command"

on:
  repository_dispatch:
    types: [ ff-command ]

concurrency:
  group: ${{ github.workflow }}-${{ github.event.client_payload.github.payload.issue.number }}-${{ github.event.client_payload.slash_command.command }}-${{ github.event.client_payload.slash_command.args.unnamed.arg1 || github.event.client_payload.slash_command.args.all }}

jobs:
  update-ff:
    name: "Update: Update Feature Flags"
    if: github.event.client_payload.slash_command.args.unnamed.arg1 == 'update'
    uses: ./.github/workflows/feature-flags-update.yml
    with:
      ref: ${{ github.event.client_payload.pull_request.head.ref }}
    secrets: inherit

  help:
    if: ${{ github.event.client_payload.slash_command.args.unnamed.arg1 == 'help' || !contains(fromJson('["update"]'), github.event.client_payload.slash_command.args.unnamed.arg1) }}
    runs-on: ubuntu-latest
    timeout-minutes: 1
    steps:
      - name: Update comment
        uses: peter-evans/create-or-update-comment@v4
        with:
          token: ${{ secrets.GIT_PAT }}
          repository: ${{ github.event.client_payload.github.payload.repository.full_name }}
          comment-id: ${{ github.event.client_payload.github.payload.comment.id }}
          body: |
            > Command | Description
            > --- | ---
            > /ff update | Update feature flags
          reactions: hooray

name: "Delete PR branch"

on:
  pull_request_target:
    types:
      - closed

jobs:
  delete-branch:
    if: github.event.pull_request.merged == true && startsWith(github.event.pull_request.head.ref, 'fb-')
    runs-on: ubuntu-latest
    steps:
      - uses: hmarr/debug-action@v3.0.0

      - name: Delete branch
        uses: actions/github-script@v7
        env:
          HEAD_REF: ${{ github.event.pull_request.head.ref }}
        with:
          github-token: ${{ secrets.GIT_PAT }}
          script: |
            const { repo, owner } = context.repo;
            const head_ref = process.env.HEAD_REF;
            await github.rest.git.deleteRef({
              owner,
              repo,
              ref: `heads/${head_ref}`,
            });
            console.log(`Branch ${head_ref} is deleted`)

name: Slash Command Dispatch
on:
  issue_comment:
    types: [created]

env:
  pull_request_commands_list: |
    help
    frontend
    ff
    git
    jira
    docker
    fm

  issue_commands_list: |
    help
    jira

jobs:
  slashCommandDispatch:
    if: startsWith(github.event.comment.body, '/')
    timeout-minutes: 1
    runs-on: ubuntu-latest
    steps:
      - uses: hmarr/debug-action@v3.0.0

      - name: "React to comment"
        uses: peter-evans/create-or-update-comment@v4
        with:
          token: ${{ secrets.GIT_PAT }}
          comment-id: ${{ github.event.comment.id }}
          reactions: eyes

      - name: "Validate command"
        id: determine_command
        uses: actions/github-script@v7
        env:
          COMMANDS_LIST: ${{ github.event.issue.pull_request && env.pull_request_commands_list || env.issue_commands_list }}
        with:
          github-token: ${{ secrets.GIT_PAT }}
          script: |
            const body = context.payload.comment.body.toLowerCase().trim();
            const commands_list = process.env.COMMANDS_LIST.split("\n");
            console.log(commands_list);
            const command = body.replace(/^\/+/, '').split(/\s+/)[0];
            core.setOutput('command', command);            
            core.setOutput('command-state', commands_list.includes(command) ? 'known' : 'unknown');

      - name: "Slash Command Dispatch"
        id: scd_issues
        if: steps.determine_command.outputs.command-state == 'known'
        uses: peter-evans/slash-command-dispatch@v4
        with:
          token: ${{ secrets.GIT_PAT }}
          reaction-token: ${{ secrets.GIT_PAT }}
          issue-type: ${{ github.event.issue.pull_request && 'pull-request' || 'issue' }}
          reactions: true
          commands: ${{ github.event.issue.pull_request && env.pull_request_commands_list || env.issue_commands_list }}

      - name: "Edit comment with error message"
        if: steps.determine_command.outputs.command-state != 'known'
        uses: peter-evans/create-or-update-comment@v4
        with:
          token: ${{ secrets.GIT_PAT }}
          comment-id: ${{ github.event.comment.id }}
          body: |
            > '/${{ steps.determine_command.outputs.command }}' is an unknown ${{ github.event.issue.pull_request && 'pull-request' || 'issue' }} command.
            > See '/help'
          reactions: confused

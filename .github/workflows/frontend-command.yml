name: "/frontend command"

on:
  repository_dispatch:
    types: [ frontend-command ]

env:
  NODE: '18'
  CACHE_NAME_PREFIX: v0

jobs:
  build:
    if: ${{ github.event.client_payload.slash_command.args.unnamed.arg1 == 'build' }}
    runs-on: ubuntu-latest
    timeout-minutes: 10
    steps:
      - uses: hmarr/debug-action@v3.0.0

      - name: Add Workflow link to command comment
        uses: peter-evans/create-or-update-comment@v4
        with:
          token: ${{ secrets.GIT_PAT }}
          repository: ${{ github.event.client_payload.github.payload.repository.full_name }}
          comment-id: ${{ github.event.client_payload.github.payload.comment.id }}
          body: |
            > [Workflow run](https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }})

      - name: Checkout on chat command
        uses: actions/checkout@v4
        with:
          token: ${{ secrets.GIT_PAT }}
          repository: ${{ github.event.client_payload.pull_request.head.repo.full_name }}
          ref: ${{ github.event.client_payload.pull_request.head.ref }}

      - name: Setup node
        uses: actions/setup-node@v4
        with:
          node-version: "${{ env.NODE }}"

      - name: Get npm cache directory
        id: npm-cache-dir
        run: echo "dir=$(npm config get cache)" >> $GITHUB_OUTPUT

      - uses: actions/cache@v4
        name: Configure npm cache
        id: npm-cache
        with:
          path: ${{ steps.npm-cache-dir.outputs.dir }}
          key: ${{ env.CACHE_NAME_PREFIX }}-${{ runner.os }}-node-${{ env.NODE }}

      - name: Get build
        id: get_build
        env:
          GITHUB_TOKEN: ${{ secrets.GIT_PAT }}
          REPO: ${{ github.event.client_payload.slash_command.args.unnamed.arg2 || 'all' }}
          SHA: ${{ github.event.client_payload.slash_command.args.unnamed.arg3 || 'master' }}
        run: |
          set -xeuo pipefail
          if [ "${REPO}" = "all" ]; then
            node label_studio/frontend/get-build.js lsf | tee -a /tmp/output
            grep 'Build link:' /tmp/output | cut -d":" -f2- >> /tmp/info_commit_msg 
            node label_studio/frontend/get-build.js dm | tee -a /tmp/output
            grep 'Build link:' /tmp/output | cut -d":" -f2- >> /tmp/info_commit_msg 
          else
            node label_studio/frontend/get-build.js "${REPO}" "${SHA}" | tee -a /tmp/output
            grep 'Build link:' /tmp/output | cut -d":" -f2- >> /tmp/info_commit_msg 
          fi
          
          {
            echo "COMMIT_MSG_FILE<<EOF"
            cat /tmp/info_commit_msg
            echo "EOF" 
          } >> "${GITHUB_ENV}"

      - name: Commit and push
        id: commit_and_push
        shell: bash
        env:
          REPO: ${{ github.event.client_payload.slash_command.args.unnamed.arg2 || 'all' }}
        run: |
          set -xeuo pipefail
          
          git config --global user.name '${{ github.event.client_payload.github.actor }}'
          git config --global user.email '${{ github.event.client_payload.github.actor }}@users.noreply.github.com'
          
          git add -A
          git status -s
          if git diff-index --quiet HEAD; then
            echo "changes=no" >> $GITHUB_OUTPUT
            exit 0
          else 
            git commit -m "[frontend] Get build ${REPO}" -m 'Workflow run: https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }}'
          fi
          git push origin HEAD

      - name: Add reaction to command comment on nothing to do
        if: steps.commit_and_push.outputs.changes == 'no'
        uses: peter-evans/create-or-update-comment@v4
        with:
          token: ${{ secrets.GIT_PAT }}
          repository: ${{ github.event.client_payload.github.payload.repository.full_name }}
          comment-id: ${{ github.event.client_payload.github.payload.comment.id }}
          body: |
            > Already up-to-date. Nothing to commit.
          reactions: "confused"

      - name: Add reaction to command comment on success
        if: steps.commit_and_push.outputs.changes != 'no'
        uses: peter-evans/create-or-update-comment@v4
        with:
          token: ${{ secrets.GIT_PAT }}
          repository: ${{ github.event.client_payload.github.payload.repository.full_name }}
          comment-id: ${{ github.event.client_payload.github.payload.comment.id }}
          body: |
            > Successfully pushed new changes
            > ${{ env.COMMIT_MSG_FILE }}
          reactions: "+1"

      - name: Add reaction to command comment on failure
        uses: peter-evans/create-or-update-comment@v4
        if: failure()
        with:
          token: ${{ secrets.GIT_PAT }}
          repository: ${{ github.event.client_payload.github.payload.repository.full_name }}
          comment-id: ${{ github.event.client_payload.github.payload.comment.id }}
          body: |
            > **Error**: failed to get build
          reactions: "-1"

  help:
    if: ${{ github.event.client_payload.slash_command.args.unnamed.arg1 == 'help' }}
    runs-on: ubuntu-latest
    timeout-minutes: 1
    steps:
      - name: Update comment
        uses: peter-evans/create-or-update-comment@v4
        with:
          token: ${{ secrets.GIT_PAT }}
          repository: ${{ github.event.client_payload.github.payload.repository.full_name }}
          comment-id: ${{ github.event.client_payload.github.payload.comment.id }}
          body: |
            > Command | Description
            > --- | ---
            > /frontend build           | Get build frontend static for all repos
            > /frontend build lsf [sha] | Get build frontend static for ${{ github.repository_owner }}/label-studio-frontend only
            > /frontend build dm [sha]  | Get build frontend static for ${{ github.repository_owner }}/dm2 only
          reactions: hooray

name: "Docker build & push OnTop"

on:
  workflow_call:
    inputs:
      base_docker_image_version:
        description: 'Base Docker Image version'
        type: string
        required: true
      tags:
        description: 'Comma separated tags'
        type: string
        required: true
      dockerfile_path:
        description: "Dockerfile path"
        type: string
        required: true
      ref:
        description: "Dockerfile ref or sha"
        type: string
        required: true
  workflow_dispatch:
    inputs:
      base_docker_image_version:
        description: 'Base Docker Image version'
        type: string
        required: true
      tags:
        description: 'Comma separated tags'
        type: string
        required: true
      dockerfile_path:
        description: "Dockerfile path"
        type: string
        required: true
      ref:
        description: "Dockerfile ref or sha"
        type: string
        required: true
        default: develop

env:
  IMAGE_NAME: "${{ vars.DOCKERHUB_ORG }}/label-studio"

jobs:
  docker_build_and_push:
    name: "Docker image"
    timeout-minutes: 90
    runs-on: ubuntu-latest
    steps:
      - uses: hmarr/debug-action@v3.0.0

      - name: Check user's membership
        uses: actions/github-script@v7
        id: actor-membership
        env:
          ACTOR: ${{ github.actor }}
        with:
          github-token: ${{ secrets.GIT_PAT }}
          script: |
            const { repo, owner } = context.repo;
            const actor = process.env.ACTOR;
            const { data: membership } = await github.rest.orgs.getMembershipForUser({
              org: owner,
              username: actor,
            });
            core.setOutput("state", membership.state);
            core.setOutput("active", membership.state == "active");

      - name: Checkout
        id: checkout
        uses: actions/checkout@v4
        with:
          submodules: 'recursive'
          ref: ${{ inputs.ref }}
          fetch-depth: 0

      - name: Calculate Docker tags
        id: calculate-docker-tags
        uses: actions/github-script@v7
        with:
          script: |
            core.setOutput('docker-tags', `${{ inputs.tags }}`.split(",").join("\n"))

      - name: Edit Dockerfile
        env:
          BASE_DOCKER_IMAGE_VERSION: ${{ inputs.base_docker_image_version }}
          DOCKERFILE_PATH: ${{ inputs.dockerfile_path }}
        run: |
          sed -i "s#^FROM .*#FROM ${IMAGE_NAME}:${BASE_DOCKER_IMAGE_VERSION}#g" "${DOCKERFILE_PATH}"
          cat "${DOCKERFILE_PATH}"

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3.10.0

      - name: Login to DockerHub
        uses: docker/login-action@v3.4.0
        with:
          username: ${{ vars.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}

      - name: Extract Docker metadata
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.IMAGE_NAME }}
          labels: |
            org.opencontainers.image.revision=${{ steps.checkout.outputs.commit }}
          tags: |
            ${{ steps.calculate-docker-tags.outputs.docker-tags }}

      - name: Push Docker image
        uses: docker/build-push-action@v6.17.0
        id: docker_build_and_push
        with:
          context: .
          file: ${{ inputs.dockerfile_path }}
          platforms: linux/amd64,linux/arm64
          push: ${{ steps.actor-membership.outputs.active }}
          sbom: true
          provenance: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max

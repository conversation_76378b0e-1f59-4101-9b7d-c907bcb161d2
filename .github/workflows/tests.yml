name: "Tests"

on:
  workflow_call:
    inputs:
      head_sha:
        required: true
        type: string
  workflow_dispatch:
    inputs:
      head_sha:
        description: 'ref or SHA'
        required: true
        type: string
        default: 'develop'

env:
  NODE: '18'
  CACHE_NAME_PREFIX: v1
  POETRY_VERSION: 2.1.3
  DJANGO_SETTINGS_MODULE: core.settings.label_studio
  COVERAGE_PROCESS_START: 1
  LOG_DIR: pytest_logs
  COLLECT_ANALYTICS: false
  DEBUG_CONTEXTLOG: true
  LABEL_STUDIO_TEST_ENVIRONMENT: false
  SENTRY_RATE: 0
  JSON_LOG: 0

jobs:
  run_pytest_sqlite:
    name: LS SQLite Ubuntu
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
      matrix:
        python-version:
          - '3.10'
          - '3.11'
          - '3.12'
          - '3.13'

    env:
      SENTRY_ENVIRONMENT: tests-ubuntu-sqlite
      DJANGO_DB: sqlite

    steps:
      - uses: hmarr/debug-action@v3.0.0

      - name: Checkout
        uses: actions/checkout@v4
        with:
          ref: ${{ inputs.head_sha }}

      - name: "Install poetry"
        run: pipx install "poetry==${{ env.POETRY_VERSION }}"

      - name: "Set up Python ${{ matrix.python-version }}"
        id: setup_python
        uses: actions/setup-python@v5
        with:
          python-version: '${{ matrix.python-version }}'
          cache: 'poetry'

      - name: Install OS dependencies
        run: |
          sudo apt-get update
          sudo apt-get install libffi8 libldap2-dev libsasl2-dev libssl-dev libxml2-dev libxslt-dev python3-dev

      - name: Install Python dependencies
        run: poetry install --with test

      - name: Generate version file
        run: poetry run python label_studio/core/version.py

      - name: Run functional tests
        working-directory: label_studio
        run: poetry run pytest --disable-warnings --durations=30 -n auto

  run_pytest_postgresql:
    name: LS PostgreSQL Ubuntu
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
      matrix:
        python-version: [ '3.12' ]
    env:
      DJANGO_DB: default
      COVERAGE_PROCESS_START: .coveragerc
      PYTHONPATH: .
      LOG_LEVEL: ERROR
      SENTRY_ENVIRONMENT: tests-ubuntu-postgresql

    services:
      postgres:
        image: postgres:13
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_USER: postgres
          POSTGRES_DB: postgres
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
    steps:
      - uses: hmarr/debug-action@v3.0.0

      - name: Checkout
        uses: actions/checkout@v4
        with:
          ref: ${{ inputs.head_sha }}

      - name: "Install poetry"
        run: pipx install "poetry==${{ env.POETRY_VERSION }}"

      - name: "Set up Python ${{ matrix.python-version }}"
        id: setup_python
        uses: actions/setup-python@v5
        with:
          python-version: '${{ matrix.python-version }}'
          cache: 'poetry'

      - name: Install OS dependencies
        run: |
          sudo apt-get update
          sudo apt-get install libffi8 libldap2-dev libsasl2-dev libssl-dev libxml2-dev libxslt-dev python3-dev

      - name: Install Python dependencies
        run: poetry install --with test

      - name: Generate version file
        run: poetry run python label_studio/core/version.py

      - name: Run functional tests
        working-directory: label_studio
        run: poetry run pytest --disable-warnings --cov=. --cov-report=xml --junitxml=functional.xml --durations=30 -n auto

      - name: Upload coverage to Codecov
        if: ${{ github.event.pull_request.head.repo.fork == false && github.event.pull_request.user.login != 'dependabot[bot]' }}
        uses: codecov/codecov-action@v5.4.3
        with:
          name: codecov-python-${{ matrix.python-version }}
          flags: pytests
          files: ./label_studio/coverage.xml
          token: ${{ secrets.CODECOV_TOKEN }}
          verbose: true
          fail_ci_if_error: true

      - name: "Upload functional tests results to Codecov"
        if: ${{ github.event.pull_request.head.repo.fork == false && github.event.pull_request.user.login != 'dependabot[bot]' }}
        uses: codecov/test-results-action@v1
        with:
          files: ./label_studio/functional.xml
          name: codecov-general-test-results
          flags: functional
          token: ${{ secrets.CODECOV_TOKEN }}
          fail_ci_if_error: false

  windows_label_studio:
    name: LS SQLite Windows
    runs-on: windows-latest
    strategy:
      fail-fast: false
      matrix:
        python-version:
          - '3.10'
    defaults:
      run:
        shell: bash
    env:
      SENTRY_ENVIRONMENT: tests-windows-sqlite
      DJANGO_DB: sqlite
    steps:
      - uses: hmarr/debug-action@v3.0.0

      - name: Checkout
        uses: actions/checkout@v4
        with:
          ref: ${{ inputs.head_sha }}

      - name: "Install poetry"
        run: pipx install "poetry==${{ env.POETRY_VERSION }}"

      - name: "Set up Python ${{ matrix.python-version }}"
        id: setup_python
        uses: actions/setup-python@v5
        with:
          python-version: '${{ matrix.python-version }}'
          cache: 'poetry'

      - name: Install Python dependencies
        run: poetry install --with test

      - name: Init project
        run: |
          set PYTHONIOENCODING=utf-8
          set PYTHONLEGACYWINDOWSSTDIO=utf-8
          bash -c "poetry run label-studio init my_project --username <EMAIL> --password testpwd"
        shell: cmd

      - name: Test with pytest
        working-directory: label_studio
        run: poetry run pytest --disable-warnings --durations=30 -n auto

name: "Tests"

on:
  workflow_call:
    inputs:
      head_sha:
        required: true
        type: string

env:
  DJ<PERSON><PERSON><PERSON>_SETTINGS_MODULE: core.settings.label_studio
  COVERAGE_PROCESS_START: 1
  LOG_DIR: pytest_logs
  COLLECT_ANALYTICS: true
  DEBUG_CONTEXTLOG: true
  LABEL_STUDIO_TEST_ENVIRONMENT: false
  SENTRY_RATE: 0
  JSON_LOG: 0
  POETRY_VERSION: 2.1.3

jobs:
  sqlite-migrations:
    name: "sqlite-migrations"
    runs-on: ubuntu-latest
    timeout-minutes: 20
    env:
      DJANGO_DB: sqlite
      SENTRY_ENVIRONMENT: tests-ubuntu-sqlite

    steps:
      - uses: hmarr/debug-action@v3.0.0

      - name: "Checkout"
        uses: actions/checkout@v4
        with:
          ref: ${{ inputs.ref }}

      - name: "Install poetry"
        run: pipx install "poetry==${{ env.POETRY_VERSION }}"

      - name: "Set up Python"
        id: setup_python
        uses: actions/setup-python@v5
        with:
          python-version: '3.12'
          cache: 'poetry'

      - name: "Install OS dependencies"
        run: |
          sudo apt-get update
          sudo apt-get install libsasl2-dev python3-dev libldap2-dev libssl-dev libxml2-dev libxslt-dev

      - name: "Install dependencies"
        run: poetry install

      - name: "Test migrations"
        run: |
          output=$(poetry run python label_studio/manage.py makemigrations)
          if ! grep 'No changes detected' <<< "${output}"; then
            error="${output}"
            error="${error//'%'/'%25'}"
            error="${error//$'\n'/'%0A'}"
            error="${error//$'\r'/'%0D'}"
            echo "::error::${error}"
            exit 1
          fi

  pgsql-migrations:
    name: "pgsql-migrations"
    runs-on: ubuntu-latest
    timeout-minutes: 20
    env:
      SENTRY_ENVIRONMENT: tests-ubuntu-pgsql
      DJANGO_DB: default
      CI: true

    services:
      postgres:
        image: postgres:13.8
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_USER: postgres
          POSTGRES_DB: postgres
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

    steps:
      - uses: hmarr/debug-action@v3.0.0

      - name: "Checkout"
        uses: actions/checkout@v4
        with:
          ref: ${{ inputs.ref }}
          fetch-depth: 0

      - name: "Install poetry"
        run: pipx install "poetry==${{ env.POETRY_VERSION }}"

      - name: "Set up Python"
        id: setup_python
        uses: actions/setup-python@v5
        with:
          python-version: '3.12'
          cache: 'poetry'

      - name: "Install OS dependencies"
        run: |
          sudo apt-get update
          sudo apt-get install libsasl2-dev python3-dev libldap2-dev libssl-dev libxml2-dev libxslt-dev

      - name: "Install dependencies"
        run: |
          poetry install

      - name: "Test migrations"
        run: |
          poetry run python label_studio/manage.py makemigrations

      - name: "Lint migrations"
        run: |
          poetry run python label_studio/manage.py lintmigrations --warnings-as-errors --project-root-path '.' --git-commit-id ${{ github.event.pull_request.base.sha || github.event.before }}
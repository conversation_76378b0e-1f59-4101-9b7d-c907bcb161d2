name: "Bandit"

on:
  workflow_call:
    inputs:
      head_sha:
        required: true
        type: string

env:
  BANDIT_VERSION: 1.7.8
  PROJECT_PATH: 'label_studio/'
  REPORT_PATH: 'bandit_results/bandit_security_report.txt'
  ACTIONS_STEP_DEBUG: '${{ secrets.ACTIONS_STEP_DEBUG }}'

jobs:
  bandit:
    name: "Bandit"
    timeout-minutes: 5
    runs-on: ubuntu-latest
    steps:
      - uses: hmarr/debug-action@v3.0.0

      - name: Checkout
        uses: actions/checkout@v4
        with:
          ref: ${{ inputs.head_sha }}

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: '3.12'

      - name: Install Bandit
        run: |
          pip install bandit==$BANDIT_VERSION

      - name: Run Bandit
        run: |
          mkdir -p bandit_results
          touch ${{ env.REPORT_PATH }}
          bandit -r $PROJECT_PATH -o ${{ env.REPORT_PATH }} -f 'txt' -ll

      - name: Print scan results
        if: always()
        run: cat ${{ env.REPORT_PATH }}

      - uses: actions/upload-artifact@v4
        if: always()
        with:
          name: Security check results
          path: ${{ env.REPORT_PATH }}

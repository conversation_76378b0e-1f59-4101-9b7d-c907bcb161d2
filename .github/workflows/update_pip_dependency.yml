name: Update PIP dependency

on:
  repository_dispatch:
    types:
      - upstream_pip_dependency_update

concurrency:
  group: ${{ github.workflow }}-${{ github.event.client_payload.branch_name || 'schedule' }}

env:
  POETRY_VERSION: 2.1.3

jobs:
  open:
    name: Sync PR
    runs-on: ubuntu-latest
    steps:
      - uses: hmarr/debug-action@v3.0.0

      - name: Checkout
        uses: actions/checkout@v4
        with:
          token: ${{ secrets.GIT_PAT }}
          ref: ${{ github.event.repository.default_branch }}

      - name: Configure git
        shell: bash
        run: |
          set -xeuo pipefail
          git config --global user.name 'robot-ci-heartex'
          git config --global user.email '<EMAIL>'

      - name: "Install poetry"
        run: pipx install "poetry==${{ env.POETRY_VERSION }}"

      - name: "Set up Python"
        id: setup_python
        uses: actions/setup-python@v5
        with:
          python-version: '3.12'
          cache: 'poetry'

      - name: Commit version files to 'develop'
        id: make-develop-commit
        run: |
          set -euo pipefail

          git checkout develop

          branch='fb-bump-${{ github.event.client_payload.dependency_name }}-version-${{ github.event.client_payload.version }}'
          echo "branch=${branch}" >> $GITHUB_OUTPUT
          git checkout -b "${branch}"

          poetry add "${{ github.event.client_payload.dependency_name }}==${{ github.event.client_payload.version }}" --lock

          git add -A
          git commit -m "chore: Bump ${{ github.event.client_payload.dependency_name }} version to ${{ github.event.client_payload.version }}" -m 'Workflow run: https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }}'

          echo "commit=$(git rev-parse HEAD)" >> $GITHUB_OUTPUT
          git push origin HEAD:refs/heads/${branch}

      - name: Create PR to 'develop'
        uses: actions/github-script@v7
        id: create-pr
        with:
          github-token: ${{ secrets.GIT_PAT }}
          script: |
            const {repo, owner} = context.repo;
            const createPullResponse = await github.rest.pulls.create({
              title: 'chore: Bump ${{ github.event.client_payload.dependency_name }} version to ${{ github.event.client_payload.version }}',
              owner,
              repo,
              head: '${{ steps.make-develop-commit.outputs.branch }}',
              base: 'develop',
              body: [
                'Hi @${{ github.actor }}!',
                '',
                'This PR was created in response to a manual trigger of the release workflow here: https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }}.',
              ].join('\n')
            });
            github.rest.pulls.requestReviewers({
              owner,
              repo,
              pull_number: createPullResponse.data.number,
               reviewers: [ '${{ github.actor }}' ]
            });
            return createPullResponse

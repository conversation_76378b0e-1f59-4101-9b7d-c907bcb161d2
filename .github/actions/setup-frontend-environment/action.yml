name: Setup frontend environment
description: Setup frontend environment

inputs:
  node-version:
    description: node version
    default: "18"
    required: false
  yarn-version:
    description: yarn version
    default: "1.22"
    required: false
  directory:
    description: frontend directory
    default: "web"
    required: false

runs:
  using: composite
  steps:
    - name: Setup node
      uses: actions/setup-node@v4
      with:
        node-version: "${{ inputs.node-version }}"

    - name: Upgrade Yarn
      env:
        VERSION: "${{ inputs.yarn-version }}"
      shell: bash
      run: npm install -g "yarn@${VERSION}"

    - name: Get yarn cache directory path
      id: yarn-cache-dir-path
      shell: bash
      run: echo "dir=$(yarn cache dir)" >> $GITHUB_OUTPUT

    - name: Configure yarn cache
      uses: actions/cache@v4
      with:
        path: ${{ steps.yarn-cache-dir-path.outputs.dir }}
        key: ${{ runner.os }}-node-${{ inputs.node-version }}-${{ hashFiles('**/yarn.lock') }}

    - name: Print Yarn cache size
      shell: bash
      run: du -d 0 -h ${{ steps.yarn-cache-dir-path.outputs.dir }}

    - name: Install yarn dependencies
      working-directory: "${{ inputs.directory }}"
      shell: bash
      run: yarn install --frozen-lockfile

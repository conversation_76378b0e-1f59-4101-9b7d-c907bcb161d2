# rotating logs so they do not overflow
x-logging:
  logging: &default-logging
    driver: "local"
    options:
      max-size: "10m"
      max-file: "3"

services:
  # not replicated setup for test setup, use a proper aws S3 compatible cluster in production
  minio:
    image: "minio/minio:${MINIO_VERSION:-RELEASE.2025-04-22T22-12-26Z}"
    command: server /data --console-address ":9009"
    restart: unless-stopped
    logging: *default-logging
    ports:
      - "9000:9000"
      - "9009:9009"
    volumes:
      - minio-data:/data
    # configure env vars in .env file or your systems environment
    environment:
      - MINIO_ROOT_USER=${MINIO_ROOT_USER:-minio_admin_do_not_use_in_production}
      - MINIO_ROOT_PASSWORD=${MINIO_ROOT_PASSWORD:-minio_admin_do_not_use_in_production}
      - MINIO_PROMETHEUS_URL=${MINIO_PROMETHEUS_URL:-http://prometheus:9090}
      - MINI<PERSON>_PROMETHEUS_AUTH_TYPE=${MINIO_PROMETHEUS_AUTH_TYPE:-public}
  prometheus:
    image: quay.io/prometheus/prometheus:v2.37.1
    restart: unless-stopped
    logging: *default-logging
    #    ports:
    #      - "9090:9090"
    volumes:
      - "./prometheus/minio/prometheus.yml:/etc/prometheus/prometheus.yml"
      - "prometheus-data:/prometheus"
    command:
      - "--config.file=/etc/prometheus/prometheus.yml"
volumes:
  minio-data:
  prometheus-data:



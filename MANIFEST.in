# react LSF / react-app with dm
recursive-include web/dist/libs/editor *
include web/dist/apps/labelstudio/*
recursive-include web/dist/libs/datamanager/ *

# html template files
recursive-include label_studio *.html

# exclude node modules
prune web/node_modules
prune web/build-tmp

# annotation templates
recursive-include label_studio/annotation_templates *

# core
recursive-include label_studio/core/static *
recursive-include label_studio/core/static_build *
include label_studio/core/utils/schema/*.json
include label_studio/core/templatetags/*.py
include label_studio/core/version_.py
include label_studio/core/all_urls.json

# io storages
recursive-include label_studio/io_storages *.yml

# tests
recursive-include label_studio/tests *.sh
recursive-include label_studio/tests/loadtests *.txt
recursive-include label_studio/tests/test_data *.yml
recursive-include label_studio/tests/test_suites/samples *
recursive-include label_studio/tests/test_suites *.yml

include label_studio/pytest.ini

# feature flags
include label_studio/feature_flags.json

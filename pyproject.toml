[project]
name = "label-studio"
version = "1.20.0.dev0"
description = "Label Studio annotation tool"
authors = [
    { name = "HumanSignal", email = "<EMAIL>" },
]
readme = "README.md"
license = "Apache-2.0"
classifiers = [
    "License :: OSI Approved :: Apache Software License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
]
requires-python = ">=3.10,<4"
dependencies = [
    "wheel (>=0.38.1,<=0.40.0)",
    "appdirs (>=1.4.3)",
    "attr (==0.3.1)",
    "attrs (>=19.2.0)",
    "pyyaml (>=6.0.0)",
    "azure-storage-blob (>=12.6.0)",
    "boto (>=2.49.0,<3.0.0)",
    "boto3 (>=1.28.58,<2.0.0)",
    "botocore (>=1.31.58,<2.0.0)",
    "bleach (>=5.0.0,<5.1.0)",
    "cryptography (>=44.0.1)",
    "Django (>=5.1.8,<5.2.0)",
    "django-storages (==1.12.3)",
    "django-annoying (==0.10.6)",
    "django-debug-toolbar (==3.2.1)",
    "django-environ (==0.10.0)",
    "django-filter (==24.3)",
    "django-model-utils (==4.1.1)",
    "django-rq (>=2.10.2,<3.0.0)",
    "django-cors-headers (==4.7.0)",
    "django-extensions (==3.2.3)",
    "django-user-agents (==0.4.0)",
    "django-ranged-fileresponse (>=0.1.2)",
    "drf-dynamic-fields (==0.3.0)",
    "djangorestframework (==3.15.2)",
    "drf-flex-fields (==0.9.5)",
    "humansignal-drf-yasg (>=1.21.10.post1)",
    "drf-generators (==0.3.0)",
    "lockfile (>=0.12.0)",
    "lxml[html-clean] (>=4.9.4)",
    "defusedxml (>=0.7.1)",
    "numpy (>=1.26.4,<2.0.0)",
    "ordered-set (==4.0.2)",
    "pandas (>=2.2.3)",
    "psycopg2-binary (==2.9.10)",
    "pydantic (>=2.9.2)",
    "python-dateutil (>=2.8.1)",
    "pytz (>=2022.1,<2023.0)",
    "requests (>=2.32.3,<2.33.0)",
    "urllib3 (>=1.26.18,<2.0.0)",
    "rq (>=1.16.2,<2.0.0)",
    "rules (==3.4)",
    "ujson (>=3.0.0)",
    "xmljson (==0.2.1)",
    "colorama (>=0.4.4)",
    "pyboxen (>=1.3.0)",
    "redis (>=5.2.1,<5.3.0)",
    "sentry-sdk (>=2.16.0)",
    "launchdarkly-server-sdk (==8.2.1)",
    "python-json-logger (==2.0.4)",
    "google-cloud-storage (>=2.13.0,<3.0.0)",
    "google-cloud-logging (>=3.10.0,<4.0.0)",
    "django-csp (==3.7)",
    "openai (>=1.10.0,<2.0.0)",
    "django-migration-linter (>=5.1.0,<6.0.0)",
    "setuptools (>=75.4.0)",
    "djangorestframework-simplejwt[crypto] (>=5.4.0,<6.0.0)",
    "tldextract (>=5.1.3)",
    ## HumanSignal repo dependencies :start
    "label-studio-sdk @ https://github.com/HumanSignal/label-studio-sdk/archive/adb48c7c0019a1371e93b8244284f3f72329351e.zip",
    ## HumanSignal repo dependencies :end
]

[project.urls]
Repository = "https://github.com/HumanSignal/label-studio"

[project.scripts]
label-studio = "label_studio.server:main"

[project.optional-dependencies]
uwsgi = ['pyuwsgi (==2.0.28.post1)', 'uwsgitop (==0.12)']
[tool.ruff]
# Enable the pycodestyle (`E`) and Pyflakes (`F`) rules by default.
# Unlike Flake8, Ruff doesn't enable pycodestyle warnings (`W`) or
# McCabe complexity (`C901`) by default.
select = [
    # Pyflakes
    "F",
    # Pycodestyle
    "E",
    # isort
    "I001"
]
ignore = [
    "E501",
    "E402", # ignores: Module level import not at top of file.  (isort takes care of this and if not, there is usually a good reason)
]



# Allow autofix for all enabled rules (when `--fix`) is provided.
fixable = ["ALL"]
unfixable = []

# Exclude a variety of commonly ignored directories.
exclude = [
    ".bzr",
    ".direnv",
    ".eggs",
    ".git",
    ".git-rewrite",
    ".hg",
    ".mypy_cache",
    ".nox",
    ".pants.d",
    ".pytype",
    ".ruff_cache",
    ".svn",
    ".tox",
    ".venv",
    "__pypackages__",
    "_build",
    "buck-out",
    "build",
    "dist",
    "node_modules",
    "venv",
    "**/migrations/*.py",
]

# Same as Blue.
line-length = 119

# Allow unused variables when underscore-prefixed.
dummy-variable-rgx = "^(_+|(_+[a-zA-Z0-9_]*[a-zA-Z0-9]+?))$"

# Assume Python 3.8 at a minimum
target-version = "py38"

[tool.ruff.per-file-ignores]
# Ignore deleting imports in __init__.py files as they're actually being used.
"__init__.py" = ["F401"]
"**/settings/label_studio.py" = ["F405"]
"test*.py" = ["F811"]


[tool.blue]

line-length = 119

include = 'label_studio/.*\.py$'

# Exclude a variety of commonly ignored directories, as well as all migrations files.
force-exclude = '''
/(
    \.bzr
  | \.direnv
  | \.eggs
  | \.git
  | \.github
  | \.git-rewrite
  | \.hg
  | \.mypy_cache
  | \.nox
  | \.pants.d
  | \.pytype
  | \.ruff_cache
  | \.svn
  | \.tox
  | \.venv
  | \.hg
  | _build
  | buck-out
  | build
  | dist
  | node_modules
  | migrations
  | venv
  | __pycache__
)/
'''

target-version = ['py38']

[tool.poetry]
name = "label-studio"
version = "1.20.0.dev0"
description = "Label Studio annotation tool"
authors = [ "HumanSignal<<EMAIL>>"]


packages = [
    { include = "label_studio" },
]
include = [
    { path = "web/dist/libs/datamanager/**/*", format = ["sdist", "wheel"] },
    { path = "web/dist/libs/editor/**/*", format = ["sdist", "wheel"] },
    { path = "web/dist/apps/labelstudio/**/*", format = ["sdist", "wheel"] },
    { path = "label_studio/annotation_templates/**/*", format = ["sdist", "wheel"] },
    { path = "label_studio/core/static/**/*", format = ["sdist", "wheel"] },
    { path = "label_studio/core/static_build/**/*", format = ["sdist", "wheel"] },
    { path = "label_studio/core/utils/schema/*.json", format = ["sdist", "wheel"] },
    { path = "label_studio/core/templatetags/*.py", format = ["sdist", "wheel"] },
    { path = "label_studio/core/version_.py", format = ["sdist", "wheel"] },
    { path = "label_studio/core/all_urls.json", format = ["sdist", "wheel"] },
    { path = "label_studio/io_storages/**/*.yml", format = ["sdist", "wheel"] },
]

exclude = [
    { path = "label_studio/frontend", format = ["sdist", "wheel"] },
]

[tool.poetry.dependencies]
python = ">=3.10,<4"

[tool.poetry.group.test.dependencies]
pytest = "7.2.2"
pytest-cov = "5.0.0"
pytest-django = "4.9.0"
pytest-mock = "3.14.0"
tavern = "2.3.0"
fakeredis = "~=2.26.1"
pytest-env = "0.6.2"
responses = "0.13.0"
pytest-xdist = "3.6.1"
psutil = "7.0.0"
freezegun = "~=1.5.1"
pre-commit = "3.3.3"
s3transfer = "0.7.0"
mock = ">=5.1.0"
moto = ">=4.2.6"
requests-mock = "1.12.1"
factory-boy = "^3.3.3"

[tool.poetry.group.build.dependencies]
twine = ">=6.1.0"
packaging = ">=24.2"

[build-system]
requires = ['poetry-core (>=2.0.0,<3.0.0)']
build-backend = "poetry.core.masonry.api"

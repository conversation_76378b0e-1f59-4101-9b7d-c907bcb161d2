---
description: Tailwind CSS and UI component best practices for the LabelStudio clientside application
globs: **/*.css,tailwind.config.ts,tailwind.config.js,**/*.scss,**/*.tsx,**/*.jsx
---

# Tailwind CSS Best Practices

## Component Styling
- Use utility classes over custom CSS
- Group related utilities with @apply when needed
- Use custom component styles as a SCSS module only when necessary to improve the readability and maintainability of the components
- Use proper responsive design utilities
- Use proper state variants
- Keep component styles consistent

## Tokens
- All tokens from Figma are programatically generated and defined in the `web/libs/ui/src/tokens/tokens.scss` file
- Tokens can be regenerated by exporting from Figma using the plugin `Figma Variable Exporter` and replacing the contents of `web/design-tokens.json` with the newly exported json file then using the `cd web/ && yarn design-tokens` command
- When regenerating tokens, ensure that the project root level `make fmt-all` command is run to ensure all the files are linted and formatted properly
- Do not use any of the tokens not defined through the Figma Variable Exporter plugin and established in the `web/libs/ui/src/tokens/tokens.scss` file
- Do not use any of the default tailwind css classes, only use the ones defined through the `web/libs/ui/src/tokens/tokens.js` file

## Layout
- Use semantic spacing utilities ie. `p-tight` instead of `p-200` or `--spacing-tight` instead of `--spacing-200`
- Use Flexbox and Grid utilities effectively
- Use container queries when needed
- Implement proper responsive breakpoints
- Use proper padding and margin utilities
- Implement proper alignment utilities

## Typography
- Use semantic typography utilities ie. `text-body-medium` instead of `text-16` or `--font-size-body-medium` instead of `--font-size-16`
- Use proper font size utilities
- Implement proper line height
- Use proper font weight utilities
- Configure custom fonts properly
- Use proper text alignment
- Implement proper text decoration

## Colors
- Use semantic color naming only which ensures dark mode compatibility ie. `bg-primary-background` instead of `bg-grape-000` or `--color-primary-background` instead of `--color-grape-000`
- Implement proper color contrast
- Use opacity utilities effectively
- Configure custom colors properly
- Use proper gradient utilities
- Implement proper hover states

## Components
- Use shadcn/ui components when available from `web/libs/ui/src/shad`
- Only use shadcn/ui components re-exported from `web/libs/ui` not the raw ones defined in `web/libs/ui/src/shad`
- The import of these components in other libs and apps should be `@humansignal/ui`
- Compose components properly
- Keep component variants consistent
- Implement proper animations
- Use proper transition utilities
- Keep accessibility in mind
- UI components should be defined and exported from `web/libs/ui` and follow the best practices of Code Organization

## Responsive Design
- Use mobile-first approach
- Implement proper breakpoints
- Use container queries effectively
- Handle different screen sizes properly
- Implement proper responsive typography
- Use proper responsive spacing

## Performance
- Use proper purge configuration
- Minimize custom CSS
- Use proper caching strategies
- Implement proper code splitting
- Optimize for production
- Monitor bundle size

## Best Practices
- Follow naming conventions
- Keep styles organized
- Use proper documentation
- Implement proper testing
- Follow accessibility guidelines
- Use proper version control

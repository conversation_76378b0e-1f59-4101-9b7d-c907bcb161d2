[uwsgi]
chdir = /label-studio/label_studio
http = :8000
module = core.wsgi:application
master = true
cheaper = true
single-interpreter = true

if-env = UWSGI_PROCESSES
processes = $(UWSGI_PROCESSES)
endif =
if-not-env = UWSGI_PROCESSES
processes = 4
endif =

vacuum = true
die-on-term = true
pidfile = /tmp/%n.pid
buffer-size = 65535
http-timeout = 300
stats = :1717
stats-http = true
memory-report = true
# Proc naming
auto-procname = true
procname-prefix = ls-
need-app = true

env = APP_WEBSERVER=uwsgi

# Avoid errors on aborted client connections
ignore-sigpipe = true
ignore-write-errors = true
disable-write-exception = true

post-buffering = 4096

# Configure uwsgi output
### Start of Application logs config
logger = applogger stdio
log-route = applogger {
log-encoder = format:applogger ${msg}
### End of Application logs config

### Start of uWSGI server logs config
logger = default stdio
log-route = default ^((?!\{).)*$
log-encoder = json:default {"timestamp": "${strftime:%%d/%%b/%%Y:%%H:%%M:%%S %%z}", "source": "uwsgi", "levelname": "INFO", "message": "${msg}"}
log-encoder = nl
### End of uWSGI server logs config
# Disable request logging
disable-logging = True
log-5xx = true
skip-atexit-teardown = True

# https://docs.launchdarkly.com/sdk/server-side/python#configuring-uwsgi
enable-threads = True

# uwsgi recommends this to prevent thundering herd on accept.
thunder-lock = True

# https://ddtrace.readthedocs.io/en/stable/advanced_usage.html#uwsgi
# This ensures that file descriptors aren't shared between keystone processes.
lazy-apps = True

# Worker Recycling <!>
# This configuration will restart a worker process after any of the following events:
#     The worker has allocated 1 GB of memory
#     20 minutes has passed + jitter 60s
#     The worker serving request more then 91 seconds

if-env = UWSGI_WORKER_RELOAD_ON_RSS
reload-on-rss = $(UWSGI_WORKER_RELOAD_ON_RSS)
endif =
if-not-env = UWSGI_WORKER_RELOAD_ON_RSS
reload-on-rss = 1024
endif =

if-env = UWSGI_WORKER_MAX_LIFETIME
max-worker-lifetime = $(UWSGI_WORKER_MAX_LIFETIME)
endif =
if-not-env = UWSGI_WORKER_MAX_LIFETIME
max-worker-lifetime = 1200
endif

if-env = UWSGI_WORKER_MAX_LIFETIME_DELTA
max-worker-lifetime-delta = $(UWSGI_WORKER_MAX_LIFETIME_DELTA)
endif =
if-not-env = UWSGI_WORKER_MAX_LIFETIME_DELTA
max-worker-lifetime-delta = 60
endif =

if-env = UWSGI_WORKER_HARAKIRI
harakiri = $(UWSGI_WORKER_HARAKIRI)
endif =
if-not-env = UWSGI_WORKER_HARAKIRI
harakiri = 91
endif =
harakiri-verbose = true

# How long to wait before forcefully killing workers
reload-mercy = 3
worker-reload-mercy = 3

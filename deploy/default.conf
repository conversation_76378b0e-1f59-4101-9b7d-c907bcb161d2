worker_processes auto;
daemon off;
pid /tmp/nginx.pid;

events {
  worker_connections 1024;
}

http {
  ##
  # Temp folders
  ##
  proxy_temp_path /tmp/proxy_temp;
  client_body_temp_path /tmp/client_temp;
  fastcgi_temp_path /tmp/fastcgi_temp;
  uwsgi_temp_path /tmp/uwsgi_temp;
  scgi_temp_path /tmp/scgi_temp;

  ##
  # Basic Settings
  ##
  sendfile on;
  tcp_nopush on;
  tcp_nodelay on;
  types_hash_max_size 2048;
  server_tokens off;

  ##
  # Logging Settings
  ##
  map $msec $msec_no_decimal { ~(.*)\.(.*) $1$2; }

  log_format json_detailed escape=json
  '{'
    '"timestamp":"$msec_no_decimal",'
    '"http":{'
      '"method":"$request_method",'
      '"request_id":"$request_id",'
      '"status_code":$status,'
      '"content_type":"$content_type",'
      '"useragent":"$http_user_agent",'
      '"referrer":"$http_referer",'
      '"origin":"$http_origin",'
      '"x_forwarded_for":"$http_x_forwarded_for",'
      '"url":"$request_uri",'
      '"version":"$server_protocol",'
      '"connection":"$connection",'
      '"connection_requests":"$connection_requests"'
    '},'
    '"network":{'
      '"bytes_written":$bytes_sent,'
      '"bytes_read":$request_length,'
      '"client":{'
        '"ip":"$remote_addr",'
        '"port":$remote_port'
      '},'
      '"destination":{'
        '"ip":"$server_addr",'
        '"port":$server_port'
      '},'
      '"nginx":{'
        '"request_time":"$request_time",'
        '"upstream_connect_time":"$upstream_connect_time",'
        '"upstream_response_time":"$upstream_response_time",'
        '"upstream_header_time":"$upstream_header_time"'
      '}'
    '}'
  '}';
  access_log /dev/stdout json_detailed;
  error_log /dev/stderr info;

  # MIME
  include /etc/nginx/mime.types;
  default_type application/octet-stream;

  ##
  # SSL Settings
  ##
  ssl_protocols          TLSv1.2 TLSv1.3;
  ssl_ciphers            ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-CHACHA20-POLY1305:ECDHE-RSA-CHACHA20-POLY1305:DHE-RSA-AES128-GCM-SHA256:DHE-RSA-AES256-GCM-SHA384;

  ##
  # Gzip Settings
  ##
  gzip            on;
  gzip_vary       on;
  gzip_proxied    any;
  gzip_comp_level 6;
  gzip_types      text/plain text/css text/xml application/json application/javascript application/rss+xml application/atom+xml image/svg+xml;

  include resolv.conf;

  server {
    listen      8085;
    add_header X-Request-ID $request_id; # Return to client

    # The lines below are handled by entrypoint.sh.
    # Do not delete it.
    #listen 8086 ssl;
    #ssl_certificate @cert@;
    #ssl_certificate_key @certkey@;

    if ($request_method !~ ^(GET|POST|PUT|PATCH|DELETE|HEAD|CONNECT|OPTIONS)$) {
      return 405;
    }

    add_header Access-Control-Allow-Origin *;
    add_header Access-Control-Max-Age 3600;
    add_header Access-Control-Expose-Headers Content-Length;
    add_header Access-Control-Allow-Headers Range;
    add_header Strict-Transport-Security "max-age=31536000";
    add_header X-Frame-Options "SAMEORIGIN" always;

    client_max_body_size 200m;   # adjust to taste

    if ($request_method = OPTIONS) {
        return 204;
    }

    location /static/ {
        alias /label-studio/label_studio/core/static_build/;
    }

    # LabelStudio frontend
    # Source: https://github.com/HumanSignal/label-studio/blob/HEAD/label_studio/frontend/src
    location /react-app/ {
        alias /label-studio/web/dist/apps/labelstudio/;
    }

    location /nginx_health {
        return 200;
    }

    location = /favicon.ico {
      alias /label-studio/label_studio/core/static_build/images/favicon.ico;
    }

    location ~ ^/file_download/(.*?)/(.*?)/(.*) {
        internal;
        # Extract the remote URL parts
        set $download_protocol $1;
        set $download_host $2;
        set $download_path $3;
        # Reconstruct the remote URL
        set $download_url $download_protocol://$download_host/$download_path;
        # Hide GCS headers
        proxy_hide_header      x-goog-hash;
        proxy_hide_header      x-goog-generation;
        proxy_hide_header      x-goog-metageneration;
        proxy_hide_header      x-goog-stored-content-encoding;
        proxy_hide_header      x-goog-stored-content-length;
        proxy_hide_header      x-goog-storage-class;
        proxy_hide_header      x-guploader-uploadid;
        # Hide AWS headers
        proxy_hide_header      x-amz-delete-marker;
        proxy_hide_header      x-amz-id-2;
        proxy_hide_header      x-amz-request-id;
        proxy_hide_header      x-amz-version-id;
        proxy_hide_header      x-amz-meta-server-side-encryption;
        proxy_hide_header      x-amz-server-side-encryption;
        # Headers for the remote server, unset Authorization and Cookie for security reasons.
        proxy_set_header Host $download_host;
        proxy_set_header Authorization '';
        proxy_set_header Cookie '';
        proxy_hide_header Content-Disposition;
        add_header Content-Disposition $upstream_http_content_disposition;
        # Stops the local disk from being written to (just forwards data through)
        proxy_max_temp_file_size 0;
        # Proxy the remote file through to the client
        proxy_pass $download_url$is_args$args;
        proxy_intercept_errors on;
        error_page 301 302 307 = @handle_redirect;
    }

    location @handle_redirect {
        set $saved_redirect_location '$upstream_http_location';
        proxy_pass $saved_redirect_location;
    }

    location / {
        absolute_redirect off;
        proxy_connect_timeout 90;
        proxy_send_timeout 90;
        proxy_read_timeout 90;
        send_timeout 90;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header Host $http_host;
        proxy_set_header X-Request-ID $request_id;
        proxy_pass_header Content-Type;
        proxy_redirect off;
        proxy_pass http://localhost:8000/;
    }

    error_page   500 502 503 504  /50x.html;

    location = /50x.html {
        root   /usr/share/nginx/html;
    }
  }
}

{"$schema": "https://schema.management.azure.com/schemas/2015-01-01/deploymentTemplate.json#", "contentVersion": "*******", "parameters": {"hostingPlanName": {"type": "string", "minLength": 1, "metadata": {"description": "Name of the hosting plan to use in Azure."}}, "appName": {"type": "string", "minLength": 1, "metadata": {"description": "Name of the Azure Web app to create."}}, "skuName": {"type": "string", "defaultValue": "F1", "allowedValues": ["F1", "D1", "B1", "B2", "B3", "S1", "S2", "S3", "P1", "P2", "P3", "P4"], "metadata": {"description": "Describes plan's pricing tier and instance size. Check details at https://azure.microsoft.com/en-us/pricing/details/app-service/"}}, "skuCapacity": {"type": "int", "defaultValue": 1, "minValue": 1, "maxValue": 3, "metadata": {"description": "Describes plan's instance count"}}, "location": {"type": "string", "defaultValue": "[resourceGroup().location]", "metadata": {"description": "Location for all resources."}}, "imageTag": {"type": "string", "defaultValue": "latest"}, "username": {"type": "string", "defaultValue": "username"}, "password": {"type": "string", "minLength": 6}}, "resources": [{"apiVersion": "2018-11-01", "name": "[parameters('hostingPlanName')]", "type": "Microsoft.Web/serverfarms", "location": "[parameters('location')]", "kind": "linux", "tags": {"displayName": "HostingPlan"}, "sku": {"name": "[parameters('skuName')]", "capacity": "[parameters('skuCapacity')]"}, "properties": {"name": "[parameters('hostingPlanName')]", "reserved": true, "zoneRedundant": false}}, {"apiVersion": "2018-11-01", "name": "[parameters('appName')]", "type": "Microsoft.Web/sites", "location": "[parameters('location')]", "tags": {"[concat('hidden-related:', resourceGroup().id, '/providers/Microsoft.Web/serverfarms/', parameters('hostingPlanName'))]": "Resource", "displayName": "Website"}, "dependsOn": ["[concat('Microsoft.Web/serverfarms/', parameters('hostingPlanName'))]"], "properties": {"name": "[parameters('appName')]", "serverFarmId": "[resourceId('Microsoft.Web/serverfarms', parameters('hostingPlanName'))]", "siteConfig": {"appSettings": [{"name": "DOCKER_REGISTRY_SERVER_URL", "value": "https://index.docker.io"}, {"name": "DOCKER_REGISTRY_SERVER_USERNAME", "value": ""}, {"name": "DOCKER_REGISTRY_SERVER_PASSWORD", "value": null}, {"name": "WEBSITES_ENABLE_APP_SERVICE_STORAGE", "value": "false"}, {"name": "DISABLE_SIGNUP_WITHOUT_LINK", "value": "0"}, {"name": "LABEL_STUDIO_ONE_CLICK_DEPLOY", "value": "1"}], "linuxFxVersion": "[concat('DOCKER|heartexlabs/label-studio:', parameters('imageTag'))]", "appCommandLine": "[concat('label-studio --username ', parameters('username'), ' --password ', parameters('password'))]"}, "clientAffinityEnabled": false}}]}
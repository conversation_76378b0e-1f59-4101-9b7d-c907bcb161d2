[project]
name = "my-label-studio"
version = "0.1.0"
description = ""
authors = [
    {name = "zhaoquanjun",email = "<EMAIL>"}
]
readme = "README.md"
requires-python = ">=3.10, <4"
dependencies = [
    "label-studio (>=1.18.0,<2.0.0)"
]

[tool.poetry]
packages = [{include = "my_label_studio", from = "src"}]


[build-system]
requires = ["poetry-core>=2.0.0,<3.0.0"]
build-backend = "poetry.core.masonry.api"

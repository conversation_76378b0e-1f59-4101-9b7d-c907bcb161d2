# Documentation of Label Studio

## Use and deploy Hexo

### Installing Dependencies

```shell
npm install
```

### Starting Development server

```shell
npm run server
```

Starts a local server. By default, this is at http://localhost:4000/.

### Deploying Documentation

```shell
npm run publish
```

## Deploy the docs locally using Hexo
To deploy the docs locally on your machine using Hexo, use the following steps. 

### Prerequisites
- Install Hexo
- Clone the Label Studio Github repository 

### Deploy the docs locally
In the label-studio/docs directory of the cloned repo, do the following:
1. (First time) Install required dependencies:
```shell
npm install
```
2. Start the Hexo server:

```shell
hexo serve
```


## Hexo Official Documentation 
[https://hexo.io/docs/](https://hexo.io/docs/)

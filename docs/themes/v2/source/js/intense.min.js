window.requestAnimFrame=function(){return window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||function(e){window.setTimeout(e,1e3/60)}}(),window.cancelRequestAnimFrame=function(){return window.cancelAnimationFrame||window.webkitCancelRequestAnimationFrame||window.mozCancelRequestAnimationFrame||window.oCancelRequestAnimationFrame||window.msCancelRequestAnimationFrame||clearTimeout}();var Intense=function(){"use strict";function e(e,n){for(var t in n)t in e||(e[t]=n[t]);return e}function n(e,n){for(var t in n)e.style[t]=n[t]}function t(e){var n=window.innerHeight/e.h;if(e.w*n>window.innerWidth)return{w:e.w*n,h:e.h*n,fit:!0};var t=window.innerWidth/e.w;return{w:e.w*t,h:e.h*t,fit:!1}}function i(e){var n;if(e.length)for(n=0;n<e.length;n++)r(e[n]);else r(e)}function r(e){(e.getAttribute("data-image")||e.src||e.href)&&e.addEventListener("click",function(n){"A"===e.tagName&&n.preventDefault(),L||A(this)},!1)}function o(){s()}function a(){cancelRequestAnimFrame(D)}function s(){D=requestAnimFrame(s),x()}function c(){z=document.body.style.overflow,document.body.style.overflow="hidden"}function d(){document.body.style.overflow=z}function l(e,n){if(e)e.className=e.className.replace("intense--loading",""),e.className=e.className.replace("intense--viewing",""),e.className+=" "+n;else{var t=document.querySelectorAll(".intense--viewing");[].forEach.call(t,function(e){e.className=e.className.replace("intense--viewing","").trim()})}}function u(e,t){var i={backgroundColor:"rgba(255,255,255,1)",width:"100%",height:"100%",position:"fixed",top:"0px",left:"0px",overflow:"hidden",zIndex:"999999",margin:"0px",webkitTransition:"opacity 150ms cubic-bezier( 0, 0, .26, 1 )",MozTransition:"opacity 150ms cubic-bezier( 0, 0, .26, 1 )",transition:"opacity 150ms cubic-bezier( 0, 0, .26, 1 )",webkitBackfaceVisibility:"hidden",opacity:"0"};R=document.createElement("figure"),R.appendChild(E),n(R,i);var r={cursor:'url( "/images/design/close_cursor.svg" ) 25 25, no-drop'};n(E,r);var o={fontFamily:'Georgia, Times, "Times New Roman", serif',position:"fixed",bottom:"0px",left:"0px",padding:"20px",color:"#fff",wordSpacing:"0.2px",webkitFontSmoothing:"antialiased",textShadow:"-1px 0px 1px rgba(0,0,0,0.4)"},a=document.createElement("figcaption");if(n(a,o),e){var s={margin:"0px",padding:"0px",fontWeight:"normal",fontSize:"40px",letterSpacing:"0.5px",lineHeight:"35px",textAlign:"left"},c=document.createElement("h1");n(c,s),c.innerHTML=e,a.appendChild(c)}if(t){var d={margin:"0px",padding:"0px",fontWeight:"normal",fontSize:"20px",letterSpacing:"0.1px",maxWidth:"500px",textAlign:"left",background:"none",marginTop:"5px"},l=document.createElement("h2");n(l,d),l.innerHTML=t,a.appendChild(l)}R.appendChild(a),w(),V.xCurr=V.xDest=window.innerWidth/2,V.yCurr=V.yDest=window.innerHeight/2,document.body.appendChild(R),setTimeout(function(){R.style.opacity="1"},10)}function m(){d(),p(),a(),document.body.removeChild(R),L=!1,l(!1)}function w(){var e=t(k);E.width=e.w,E.height=e.h,Y=e.fit,H={w:E.width,h:E.height},N={w:window.innerWidth,h:window.innerHeight},I={x:N.w-H.w,y:N.h-H.h}}function A(e){l(e,"intense--loading");var n=e.getAttribute("data-image")||e.src||e.href,t=e.getAttribute("data-title")||e.title,i=e.getAttribute("data-caption");T&&(T.onload=null),T=new Image,T.onload=function(){k={w:T.width,h:T.height},E=this,u(t,i),c(),f(),s(),l(e,"intense--viewing")},T.src=n}function f(){R.addEventListener("mousemove",h,!1),R.addEventListener("touchmove",v,!1),window.addEventListener("resize",w,!1),window.addEventListener("keyup",g,!1),E.addEventListener("click",m,!1)}function p(){R.removeEventListener("mousemove",h,!1),R.removeEventListener("touchmove",v,!1),window.removeEventListener("resize",w,!1),window.removeEventListener("keyup",g,!1),E.removeEventListener("click",m,!1)}function h(e){V.xDest=e.clientX,V.yDest=e.clientY}function v(e){e.preventDefault(),V.xDest=window.innerWidth-e.touches[0].clientX,V.yDest=window.innerHeight-e.touches[0].clientY}function g(e){e.preventDefault(),e.keyCode===B&&m()}function x(){function e(e,n){return q?(n-e)/n:e/n}if(V.xCurr+=.05*(V.xDest-V.xCurr),V.yCurr+=.05*(V.yDest-V.yCurr),Y===!0){if(F+=V.xCurr-F,V.xCurr!==C){var n=parseFloat(e(F,N.w));n=I.x*n,E.style.webkitTransform="translate("+n+"px, 0px)",E.style.MozTransform="translate("+n+"px, 0px)",E.style.msTransform="translate("+n+"px, 0px)",C=V.xCurr}}else if(Y===!1&&(F+=V.yCurr-F,V.yCurr!==C)){var n=parseFloat(e(F,N.h));n=I.y*n,E.style.webkitTransform="translate( 0px, "+n+"px)",E.style.MozTransform="translate( 0px, "+n+"px)",E.style.msTransform="translate( 0px, "+n+"px)",C=V.yCurr}}function y(e){"invertInteractionDirection"in e&&(q=e.invertInteractionDirection)}function b(e,n){if(!e)throw"You need to pass an element!";n&&y(n),i(e)}var D,T,C,k,E,R,z,B=27,V={xCurr:0,yCurr:0,xDest:0,yDest:0},Y=!0,q=!1,F=0,H={w:0,h:0},N={w:0,h:0},I={x:0,y:0},L=!1;return e(b,{resize:w,start:o,stop:a,config:y})}();"undefined"!=typeof module&&module.exports&&(module.exports=Intense);

window.onload = function() {
	// Intensify all images on the page.
    var elements = document.querySelectorAll('.make-intense-zoom');
		if(elements?.length > 0) Intense(elements);
}

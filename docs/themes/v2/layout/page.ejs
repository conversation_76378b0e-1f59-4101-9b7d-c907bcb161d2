<% const isEnterpriseTheme = theme.tier === "enterprise" %>
<% const tocPageTypes = page.type === "guide" || page.type === "blog" || page.type === "templates" || page.type === "tags" || page.type === "plugins" %>

<% const formattedContent = removeContent(page.content) %>
<% const tocContent = replaceContent(formattedContent) %>
<% const pageHasHeading = /<h[1-6]\b[^>]*>/i.test(formattedContent); %>
<% const showToc = tocPageTypes && pageHasHeading %>

<div class="content-grid">
  <div class="content-markdown">
    <% if(page.type && page.parent !== undefined) { %>
      <%- breadcrumb({pageType: page.type, parentPage: isEnterpriseTheme && page.parent_enterprise ? page.parent_enterprise : page.parent, currentPage: page.title, parentPageExtension: page.parent_page_extension }) %>
    <% } %>
    <% if (!page.hide_frontmatter_title && page.title.trim()) { %>
      <%- partial("component/heading", {text: page.title, size: "XLarge", tag: "h1", customClass: "home-page-title"}) %>
    <% } %>
    <%- formattedContent %>
  </div>
  <div class="page-rightsidebar">
    <% if (showToc) { %>
      <div class="toc">
        <%- partial("component/text", {text: "In this article", tag: "h3", size: "Eyebrow"}) %>
        <%- tableOfContent(tocContent, {max_depth: 3}) %>
      </div>
    <% } %>
    <div class="toc-enterprise-cta">
      <% if(isEnterpriseTheme) { %>
        <span class="toc-enterprise-cta-copy">Designed for teams of all sizes</span>
        <%- partial("component/button", { url: "https://humansignal.com/contact-sales", label: "Contact Sales", theme: "Tertiary" }) %>
      <% } else { %>
        <span class="toc-enterprise-cta-copy">Designed for teams of all sizes</span>
        <%- partial("component/button", { url: "https://humansignal.com/pricing", label: "Compare Versions", theme: "Tertiary" }) %>
      <% } %>
    </div>
  </div>
</div>

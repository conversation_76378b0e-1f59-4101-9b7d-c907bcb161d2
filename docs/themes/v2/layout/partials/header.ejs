<% const tier = theme.tier || "all" %>
<% const isEnterpriseTheme = tier === "enterprise" %>

<% if (file_exists('partials/alert-enterprise.ejs') && isEnterpriseTheme) { %>
  <%- partial('partials/alert-enterprise') %>
<% } else if (file_exists('partials/alert.ejs')) { %>
  <%- partial('partials/alert') %>
<% } %>

<header class="page-header">
  <div class="page-header-top-level">
    <div class="page-header-logo">
      <a href="<% if(isEnterpriseTheme) { %>https://docs.humansignal.com/guide/<% } else { %>https://labelstud.io/guide<% } %>" class="HeaderLogo" arial-label="Homepage">
        <% if(isEnterpriseTheme) { %>
          <svg width="268" height="22" viewBox="0 0 268 22" fill="none" xmlns="http://www.w3.org/2000/svg">
            <g clip-path="url(#clip0_492_4794)">
            <path fill-rule="evenodd" clip-rule="evenodd" d="M5.68667 7.03563H4.3376e-05V1.36534H5.68667V7.03563ZM20.2194 7.03565H14.5328V1.36536H20.2194V7.03565ZM0 21.5265H5.68663V15.8562H0V21.5265ZM20.2194 21.5265H14.5328V15.8562H20.2194V21.5265ZM1.89527 7.03573H3.79081V15.8562H1.89527V7.03573ZM18.3237 7.03573H16.4281V15.8562H18.3237V7.03573ZM5.68677 3.25559H14.5326V5.14568H5.68677V3.25559ZM14.5326 17.746H5.68677V19.6361H14.5326V17.746Z" fill="#FF7557"/>
            </g>
            <path d="M27.6371 4.75327V15.9051H33.9906V18.0293H25.4181V4.75327H27.6371Z" fill="#FDFDFC"/>
            <path d="M44.3163 18.0293H42.1921V16.7396C41.9645 17.1695 41.5726 17.5235 41.0162 17.8017C40.4599 18.0799 39.8656 18.2189 39.2335 18.2189C38.4116 18.2189 37.6593 18.004 36.9765 17.5741C36.2938 17.1442 35.7564 16.5626 35.3644 15.8293C34.9725 15.0959 34.7765 14.2804 34.7765 13.3827C34.7765 12.4976 34.9725 11.6884 35.3644 10.9551C35.7564 10.2217 36.2938 9.64011 36.9765 9.21021C37.6593 8.78032 38.4116 8.56538 39.2335 8.56538C39.8656 8.56538 40.4599 8.70446 41.0162 8.98263C41.5726 9.26079 41.9645 9.61482 42.1921 10.0447V8.75504H44.3163V18.0293ZM36.8438 13.3827C36.8438 13.9137 36.9639 14.3942 37.2041 14.8241C37.457 15.254 37.7984 15.5953 38.2283 15.8482C38.6708 16.0885 39.1513 16.2086 39.6697 16.2086C40.1881 16.2086 40.6812 16.0758 41.149 15.8103C41.6168 15.5448 41.9645 15.2034 42.1921 14.7861V12.0171C41.9772 11.5999 41.6358 11.2585 41.168 10.993C40.7001 10.7148 40.2007 10.5757 39.6697 10.5757C39.1386 10.5757 38.6582 10.7022 38.2283 10.9551C37.7984 11.1953 37.457 11.5304 37.2041 11.9602C36.9639 12.3775 36.8438 12.8516 36.8438 13.3827Z" fill="#FDFDFC"/>
            <path d="M48.6353 10.0447C48.8629 9.61482 49.2548 9.26079 49.8112 8.98263C50.3801 8.70446 50.9744 8.56538 51.5939 8.56538C52.4158 8.56538 53.1681 8.78032 53.8509 9.21021C54.5336 9.64011 55.071 10.2217 55.4629 10.9551C55.8549 11.6884 56.0509 12.4976 56.0509 13.3827C56.0509 14.2804 55.8549 15.0959 55.4629 15.8293C55.071 16.5626 54.5336 17.1442 53.8509 17.5741C53.1681 18.004 52.4158 18.2189 51.5939 18.2189C50.9744 18.2189 50.3801 18.0799 49.8112 17.8017C49.2548 17.5235 48.8629 17.1695 48.6353 16.7396V18.0293H46.5301V4.75327H48.6353V10.0447ZM48.6353 14.7861C48.8629 15.2034 49.2106 15.5448 49.6784 15.8103C50.1462 16.0758 50.6393 16.2086 51.1577 16.2086C51.6761 16.2086 52.1503 16.0885 52.5802 15.8482C53.0227 15.5953 53.3641 15.254 53.6043 14.8241C53.8572 14.3942 53.9836 13.9137 53.9836 13.3827C53.9836 12.8516 53.8572 12.3775 53.6043 11.9602C53.3641 11.5304 53.029 11.1953 52.5991 10.9551C52.1692 10.7022 51.6888 10.5757 51.1577 10.5757C50.6267 10.5757 50.1272 10.7148 49.6594 10.993C49.1916 11.2585 48.8502 11.5999 48.6353 12.0171V14.7861Z" fill="#FDFDFC"/>
            <path d="M61.967 18.2189C61.0946 18.2189 60.2917 18.0103 59.5584 17.5931C58.825 17.1632 58.2434 16.5752 57.8135 15.8293C57.3836 15.0833 57.1687 14.2551 57.1687 13.3447C57.1687 12.4597 57.3773 11.6568 57.7945 10.9361C58.2244 10.2028 58.8061 9.62746 59.5394 9.21021C60.2727 8.78032 61.0756 8.56538 61.948 8.56538C62.7699 8.56538 63.5222 8.76136 64.205 9.15332C64.8877 9.54528 65.4251 10.0763 65.8171 10.7464C66.209 11.4166 66.405 12.1625 66.405 12.9844C66.405 13.212 66.3797 13.5154 66.3291 13.8948H59.217C59.3308 14.666 59.6342 15.2729 60.1273 15.7155C60.6331 16.158 61.2716 16.3793 62.0429 16.3793C62.5613 16.3793 63.0417 16.2718 63.4843 16.0568C63.9394 15.8293 64.2682 15.5385 64.4705 15.1844L66.1395 16.2086C65.7981 16.8408 65.2544 17.3339 64.5084 17.6879C63.7751 18.0419 62.9279 18.2189 61.967 18.2189ZM64.2619 12.3775C64.186 11.7832 63.9205 11.2964 63.4653 10.9171C63.0101 10.5378 62.4728 10.3482 61.8532 10.3482C61.1831 10.3482 60.6204 10.5315 60.1653 10.8982C59.7227 11.2522 59.4256 11.7453 59.2739 12.3775H64.2619Z" fill="#FDFDFC"/>
            <path d="M70.2681 18.0293H68.1629V4.75327H70.2681V18.0293Z" fill="#FDFDFC"/>
            <path d="M81.0831 4.56361C81.9303 4.56361 82.7774 4.74694 83.6246 5.11361C84.4843 5.46764 85.1418 5.9165 85.597 6.46018L84.3073 8.18606C83.8648 7.73089 83.3337 7.37054 82.7142 7.10502C82.1073 6.8395 81.5067 6.70674 80.9125 6.70674C80.3055 6.70674 79.8061 6.84582 79.4142 7.12398C79.0348 7.40215 78.8452 7.7625 78.8452 8.20503C78.8452 8.58435 78.9653 8.90044 79.2055 9.15332C79.4584 9.4062 79.7682 9.6085 80.1349 9.76022C80.5142 9.8993 81.0326 10.0637 81.69 10.2533C82.5878 10.5062 83.3148 10.7654 83.8711 11.0309C84.4401 11.2838 84.9205 11.6694 85.3125 12.1878C85.7171 12.6936 85.9194 13.3637 85.9194 14.1982C85.9194 14.9189 85.7234 15.589 85.3315 16.2086C84.9522 16.8155 84.4022 17.3023 83.6815 17.6689C82.9608 18.0356 82.1263 18.2189 81.178 18.2189C80.1918 18.2189 79.2182 18.004 78.2572 17.5741C77.2963 17.1442 76.5187 16.6005 75.9245 15.9431L77.3469 14.312C77.9285 14.8683 78.5228 15.3045 79.1297 15.6206C79.7366 15.9241 80.4193 16.0758 81.178 16.0758C81.9113 16.0758 82.5056 15.9051 82.9608 15.5637C83.4159 15.2224 83.6435 14.7735 83.6435 14.2172C83.6435 13.8505 83.5234 13.5534 83.2832 13.3258C83.0429 13.0855 82.7395 12.8959 82.3728 12.7568C82.0061 12.6177 81.5004 12.4597 80.8556 12.2827C79.9578 12.0424 79.2245 11.8022 78.6555 11.562C78.0992 11.3091 77.6187 10.9298 77.2141 10.424C76.8095 9.90563 76.6072 9.22286 76.6072 8.37572C76.6072 7.66767 76.7969 7.02283 77.1762 6.44122C77.5682 5.8596 78.0992 5.40442 78.7693 5.07568C79.4521 4.7343 80.2234 4.56361 81.0831 4.56361Z" fill="#FDFDFC"/>
            <path d="M91.5648 18.162C90.5154 18.162 89.6683 17.846 89.0234 17.2138C88.3912 16.5689 88.0751 15.6839 88.0751 14.5586V10.7654H86.2924V8.75504H88.0751V6.27053H90.1803V8.75504H92.6079V10.7654H90.1803V14.4637C90.1803 14.9948 90.3384 15.4183 90.6545 15.7344C90.9832 16.0379 91.3878 16.1896 91.8683 16.1896C92.0959 16.1896 92.3361 16.1517 92.589 16.0758L92.7028 17.9345C92.3993 18.0862 92.02 18.162 91.5648 18.162Z" fill="#FDFDFC"/>
            <path d="M100.804 16.7965C100.526 17.2264 100.134 17.5741 99.6279 17.8396C99.1222 18.0925 98.5785 18.2189 97.9969 18.2189C96.8337 18.2189 95.9107 17.846 95.2279 17.1C94.5451 16.3413 94.2037 15.3235 94.2037 14.0465V8.75504H96.3089V13.7051C96.3089 14.4511 96.5049 15.039 96.8969 15.4689C97.3015 15.8988 97.8388 16.1137 98.509 16.1137C98.9768 16.1137 99.413 16 99.8176 15.7724C100.235 15.5448 100.564 15.235 100.804 14.843L100.823 8.75504H102.947V18.0293H100.804V16.7965Z" fill="#FDFDFC"/>
            <path d="M112.11 12.0171C111.895 11.5999 111.553 11.2585 111.086 10.993C110.618 10.7148 110.118 10.5757 109.587 10.5757C109.056 10.5757 108.576 10.7022 108.146 10.9551C107.716 11.1953 107.375 11.5304 107.122 11.9602C106.882 12.3775 106.761 12.8516 106.761 13.3827C106.761 13.9137 106.882 14.3942 107.122 14.8241C107.375 15.254 107.716 15.5953 108.146 15.8482C108.588 16.0885 109.069 16.2086 109.587 16.2086C110.106 16.2086 110.599 16.0758 111.067 15.8103C111.534 15.5448 111.882 15.2034 112.11 14.7861V12.0171ZM104.694 13.3827C104.694 12.4976 104.89 11.6884 105.282 10.9551C105.674 10.2217 106.211 9.64011 106.894 9.21021C107.577 8.78032 108.329 8.56538 109.151 8.56538C109.783 8.56538 110.378 8.70446 110.934 8.98263C111.49 9.26079 111.882 9.61482 112.11 10.0447V4.75327H114.215V18.0293H112.11V16.7396C111.882 17.1695 111.49 17.5235 110.934 17.8017C110.378 18.0799 109.783 18.2189 109.151 18.2189C108.329 18.2189 107.577 18.004 106.894 17.5741C106.211 17.1442 105.674 16.5626 105.282 15.8293C104.89 15.0959 104.694 14.2804 104.694 13.3827Z" fill="#FDFDFC"/>
            <path d="M118.553 8.75504V18.0293H116.448V8.75504H118.553ZM117.51 4.44981C117.876 4.44981 118.193 4.58257 118.458 4.84809C118.736 5.11361 118.875 5.42971 118.875 5.79638C118.875 6.1757 118.736 6.49811 118.458 6.76363C118.193 7.02915 117.876 7.16192 117.51 7.16192C117.143 7.16192 116.827 7.02915 116.562 6.76363C116.296 6.49811 116.163 6.1757 116.163 5.79638C116.163 5.42971 116.296 5.11361 116.562 4.84809C116.827 4.58257 117.143 4.44981 117.51 4.44981Z" fill="#FDFDFC"/>
            <path d="M125.125 8.56538C126.01 8.56538 126.819 8.78032 127.552 9.21021C128.286 9.62746 128.867 10.2091 129.297 10.9551C129.727 11.6884 129.942 12.5039 129.942 13.4016C129.942 14.2994 129.727 15.1149 129.297 15.8482C128.867 16.5816 128.286 17.1632 127.552 17.5931C126.819 18.0103 126.01 18.2189 125.125 18.2189C124.227 18.2189 123.412 18.0103 122.678 17.5931C121.945 17.1632 121.363 16.5816 120.933 15.8482C120.516 15.1149 120.308 14.2994 120.308 13.4016C120.308 12.5039 120.516 11.6884 120.933 10.9551C121.363 10.2091 121.945 9.62746 122.678 9.21021C123.424 8.78032 124.24 8.56538 125.125 8.56538ZM122.375 13.4016C122.375 13.92 122.495 14.3942 122.735 14.8241C122.975 15.2413 123.304 15.5764 123.721 15.8293C124.151 16.0695 124.619 16.1896 125.125 16.1896C125.631 16.1896 126.092 16.0695 126.509 15.8293C126.927 15.5764 127.255 15.2413 127.496 14.8241C127.736 14.3942 127.856 13.92 127.856 13.4016C127.856 12.8832 127.736 12.4091 127.496 11.9792C127.255 11.5493 126.927 11.2143 126.509 10.974C126.092 10.7212 125.631 10.5947 125.125 10.5947C124.619 10.5947 124.151 10.7212 123.721 10.974C123.304 11.2143 122.975 11.5493 122.735 11.9792C122.495 12.4091 122.375 12.8832 122.375 13.4016Z" fill="#FDFDFC"/>
            <path d="M143.804 4.75327V6.87743H138.19V10.0068H143.197V12.1309H138.19V15.9051H143.804V18.0293H135.971V4.75327H143.804Z" fill="#FDFDFC"/>
            <path d="M147.91 9.98781C148.2 9.54528 148.592 9.19757 149.085 8.94469C149.591 8.69182 150.135 8.56538 150.716 8.56538C151.88 8.56538 152.803 8.9447 153.485 9.70332C154.168 10.4493 154.51 11.4608 154.51 12.7378V18.0293H152.404V13.0792C152.404 12.3332 152.202 11.7453 151.798 11.3154C151.406 10.8855 150.875 10.6706 150.204 10.6706C149.737 10.6706 149.3 10.7844 148.896 11.012C148.491 11.2396 148.162 11.5493 147.91 11.9413V18.0293H145.785V8.75504H147.91V9.98781Z" fill="#FDFDFC"/>
            <path d="M160.705 18.162C159.655 18.162 158.808 17.846 158.163 17.2138C157.531 16.5689 157.215 15.6839 157.215 14.5586V10.7654H155.432V8.75504H157.215V6.27053H159.32V8.75504H161.748V10.7654H159.32V14.4637C159.32 14.9948 159.478 15.4183 159.794 15.7344C160.123 16.0379 160.528 16.1896 161.008 16.1896C161.236 16.1896 161.476 16.1517 161.729 16.0758L161.843 17.9345C161.539 18.0862 161.16 18.162 160.705 18.162Z" fill="#FDFDFC"/>
            <path d="M167.353 18.2189C166.48 18.2189 165.678 18.0103 164.944 17.5931C164.211 17.1632 163.629 16.5752 163.199 15.8293C162.769 15.0833 162.555 14.2551 162.555 13.3447C162.555 12.4597 162.763 11.6568 163.18 10.9361C163.61 10.2028 164.192 9.62746 164.925 9.21021C165.659 8.78032 166.461 8.56538 167.334 8.56538C168.156 8.56538 168.908 8.76136 169.591 9.15332C170.274 9.54528 170.811 10.0763 171.203 10.7464C171.595 11.4166 171.791 12.1625 171.791 12.9844C171.791 13.212 171.766 13.5154 171.715 13.8948H164.603C164.717 14.666 165.02 15.2729 165.513 15.7155C166.019 16.158 166.657 16.3793 167.429 16.3793C167.947 16.3793 168.428 16.2718 168.87 16.0568C169.325 15.8293 169.654 15.5385 169.856 15.1844L171.525 16.2086C171.184 16.8408 170.64 17.3339 169.894 17.6879C169.161 18.0419 168.314 18.2189 167.353 18.2189ZM169.648 12.3775C169.572 11.7832 169.306 11.2964 168.851 10.9171C168.396 10.5378 167.859 10.3482 167.239 10.3482C166.569 10.3482 166.006 10.5315 165.551 10.8982C165.109 11.2522 164.811 11.7453 164.66 12.3775H169.648Z" fill="#FDFDFC"/>
            <path d="M173.549 8.75504H175.654V10.0257C175.881 9.59585 176.223 9.24815 176.678 8.98263C177.133 8.70446 177.582 8.56538 178.025 8.56538C178.455 8.56538 178.809 8.61596 179.087 8.71711L178.954 10.7085C178.6 10.5821 178.233 10.5189 177.854 10.5189C177.475 10.5189 177.07 10.6643 176.64 10.9551C176.21 11.2459 175.881 11.6315 175.654 12.112V18.0293H173.549V8.75504Z" fill="#FDFDFC"/>
            <path d="M180.605 8.75504H182.71V10.0447C182.938 9.61482 183.33 9.26079 183.886 8.98263C184.455 8.70446 185.05 8.56538 185.669 8.56538C186.491 8.56538 187.243 8.78032 187.926 9.21021C188.609 9.64011 189.146 10.2217 189.538 10.9551C189.93 11.6884 190.126 12.5039 190.126 13.4016C190.126 14.2867 189.93 15.0959 189.538 15.8293C189.146 16.5626 188.609 17.1442 187.926 17.5741C187.243 18.004 186.491 18.2189 185.669 18.2189C185.05 18.2189 184.455 18.0799 183.886 17.8017C183.33 17.5235 182.938 17.1695 182.71 16.7396V21.2914H180.605V8.75504ZM182.71 14.7672C182.925 15.1844 183.267 15.5321 183.735 15.8103C184.202 16.0758 184.702 16.2086 185.233 16.2086C185.764 16.2086 186.244 16.0885 186.674 15.8482C187.104 15.5953 187.439 15.2603 187.68 14.843C187.932 14.4132 188.059 13.9327 188.059 13.4016C188.059 12.8706 187.932 12.3901 187.68 11.9602C187.439 11.5304 187.098 11.1953 186.655 10.9551C186.225 10.7022 185.751 10.5757 185.233 10.5757C184.715 10.5757 184.221 10.7085 183.754 10.974C183.286 11.2396 182.938 11.5809 182.71 11.9982V14.7672Z" fill="#FDFDFC"/>
            <path d="M191.885 8.75504H193.99V10.0257C194.218 9.59585 194.559 9.24815 195.014 8.98263C195.469 8.70446 195.918 8.56538 196.361 8.56538C196.791 8.56538 197.145 8.61596 197.423 8.71711L197.29 10.7085C196.936 10.5821 196.569 10.5189 196.19 10.5189C195.811 10.5189 195.406 10.6643 194.976 10.9551C194.546 11.2459 194.218 11.6315 193.99 12.112V18.0293H191.885V8.75504Z" fill="#FDFDFC"/>
            <path d="M201.047 8.75504V18.0293H198.941V8.75504H201.047ZM200.003 4.44981C200.37 4.44981 200.686 4.58257 200.952 4.84809C201.23 5.11361 201.369 5.42971 201.369 5.79638C201.369 6.1757 201.23 6.49811 200.952 6.76363C200.686 7.02915 200.37 7.16192 200.003 7.16192C199.637 7.16192 199.321 7.02915 199.055 6.76363C198.79 6.49811 198.657 6.1757 198.657 5.79638C198.657 5.42971 198.79 5.11361 199.055 4.84809C199.321 4.58257 199.637 4.44981 200.003 4.44981Z" fill="#FDFDFC"/>
            <path d="M206.708 18.2379C205.962 18.2379 205.216 18.1178 204.47 17.8776C203.737 17.6247 203.143 17.2959 202.687 16.8913L203.522 15.2982C203.876 15.6396 204.35 15.9178 204.944 16.1327C205.551 16.3477 206.12 16.4551 206.651 16.4551C207.106 16.4551 207.473 16.3603 207.751 16.1706C208.042 15.981 208.187 15.7344 208.187 15.431C208.187 15.1149 208.036 14.8873 207.732 14.7482C207.429 14.5965 206.942 14.4384 206.272 14.2741C205.589 14.1097 205.033 13.9453 204.603 13.781C204.173 13.6039 203.8 13.3321 203.484 12.9654C203.18 12.5988 203.029 12.1057 203.029 11.4861C203.029 10.9171 203.18 10.4114 203.484 9.96885C203.787 9.52631 204.211 9.18493 204.755 8.94469C205.311 8.69182 205.937 8.56538 206.632 8.56538C207.302 8.56538 207.947 8.66021 208.567 8.84987C209.186 9.03952 209.698 9.31769 210.103 9.68436L209.268 11.3154C208.927 11.012 208.472 10.7717 207.903 10.5947C207.347 10.4177 206.853 10.3292 206.424 10.3292C206.032 10.3292 205.716 10.4177 205.475 10.5947C205.248 10.7717 205.134 11.012 205.134 11.3154C205.134 11.6062 205.279 11.8212 205.57 11.9602C205.861 12.0993 206.335 12.2574 206.993 12.4344C207.688 12.624 208.257 12.8137 208.7 13.0034C209.142 13.1804 209.521 13.4585 209.837 13.8379C210.166 14.2172 210.331 14.7229 210.331 15.3551C210.331 15.9114 210.173 16.4109 209.856 16.8534C209.553 17.2833 209.123 17.6247 208.567 17.8776C208.023 18.1178 207.403 18.2379 206.708 18.2379Z" fill="#FDFDFC"/>
            <path d="M216.304 18.2189C215.432 18.2189 214.629 18.0103 213.896 17.5931C213.162 17.1632 212.581 16.5752 212.151 15.8293C211.721 15.0833 211.506 14.2551 211.506 13.3447C211.506 12.4597 211.715 11.6568 212.132 10.9361C212.562 10.2028 213.143 9.62746 213.877 9.21021C214.61 8.78032 215.413 8.56538 216.285 8.56538C217.107 8.56538 217.86 8.76136 218.542 9.15332C219.225 9.54528 219.763 10.0763 220.155 10.7464C220.546 11.4166 220.742 12.1625 220.742 12.9844C220.742 13.212 220.717 13.5154 220.667 13.8948H213.554C213.668 14.666 213.972 15.2729 214.465 15.7155C214.971 16.158 215.609 16.3793 216.38 16.3793C216.899 16.3793 217.379 16.2718 217.822 16.0568C218.277 15.8293 218.606 15.5385 218.808 15.1844L220.477 16.2086C220.136 16.8408 219.592 17.3339 218.846 17.6879C218.113 18.0419 217.265 18.2189 216.304 18.2189ZM218.599 12.3775C218.523 11.7832 218.258 11.2964 217.803 10.9171C217.348 10.5378 216.81 10.3482 216.191 10.3482C215.521 10.3482 214.958 10.5315 214.503 10.8982C214.06 11.2522 213.763 11.7453 213.611 12.3775H218.599Z" fill="#FDFDFC"/>
            <path d="M264.169 17.7545C263.422 17.7545 262.675 17.6342 261.927 17.3935C261.193 17.1402 260.597 16.8108 260.141 16.4055L260.977 14.8095C261.332 15.1515 261.807 15.4302 262.402 15.6455C263.01 15.8608 263.58 15.9685 264.112 15.9685C264.568 15.9685 264.936 15.8735 265.214 15.6835C265.506 15.4935 265.651 15.2465 265.651 14.9425C265.651 14.6258 265.499 14.3978 265.195 14.2585C264.891 14.1065 264.404 13.9482 263.732 13.7835C263.048 13.6188 262.491 13.4542 262.06 13.2895C261.63 13.1122 261.256 12.8398 260.939 12.4725C260.635 12.1052 260.483 11.6112 260.483 10.9905C260.483 10.4205 260.635 9.91383 260.939 9.4705C261.243 9.02717 261.668 8.68517 262.212 8.4445C262.77 8.19117 263.397 8.0645 264.093 8.0645C264.765 8.0645 265.411 8.1595 266.031 8.3495C266.652 8.5395 267.165 8.81817 267.57 9.1855L266.734 10.8195C266.392 10.5155 265.936 10.2748 265.366 10.0975C264.809 9.92017 264.315 9.8315 263.884 9.8315C263.492 9.8315 263.175 9.92017 262.934 10.0975C262.706 10.2748 262.592 10.5155 262.592 10.8195C262.592 11.1108 262.738 11.3262 263.029 11.4655C263.321 11.6048 263.796 11.7632 264.454 11.9405C265.151 12.1305 265.721 12.3205 266.164 12.5105C266.608 12.6878 266.988 12.9665 267.304 13.3465C267.634 13.7265 267.798 14.2332 267.798 14.8665C267.798 15.4238 267.64 15.9242 267.323 16.3675C267.019 16.7982 266.589 17.1402 266.031 17.3935C265.487 17.6342 264.866 17.7545 264.169 17.7545Z" fill="#FF7557"/>
            <path d="M259.17 15.1895C258.816 15.9622 258.252 16.5828 257.479 17.0515C256.719 17.5075 255.858 17.7355 254.895 17.7355C253.983 17.7355 253.154 17.5265 252.406 17.1085C251.672 16.6905 251.089 16.1142 250.658 15.3795C250.24 14.6322 250.031 13.8088 250.031 12.9095C250.031 12.0102 250.24 11.1932 250.658 10.4585C251.089 9.71117 251.672 9.1285 252.406 8.7105C253.154 8.27983 253.977 8.0645 254.876 8.0645C255.776 8.0645 256.58 8.2735 257.289 8.6915C258.011 9.09683 258.543 9.6415 258.885 10.3255L257.099 11.3325C256.909 10.9525 256.612 10.6548 256.206 10.4395C255.814 10.2115 255.37 10.0975 254.876 10.0975C254.078 10.0975 253.42 10.3698 252.9 10.9145C252.381 11.4465 252.121 12.1115 252.121 12.9095C252.121 13.4288 252.242 13.9038 252.482 14.3345C252.723 14.7525 253.052 15.0882 253.47 15.3415C253.901 15.5822 254.376 15.7025 254.895 15.7025C255.44 15.7025 255.921 15.5822 256.339 15.3415C256.757 15.1008 257.074 14.7652 257.289 14.3345L259.17 15.1895Z" fill="#FF7557"/>
            <path d="M244.077 8.0645C244.964 8.0645 245.775 8.27983 246.509 8.7105C247.244 9.1285 247.827 9.71117 248.257 10.4585C248.688 11.1932 248.903 12.0102 248.903 12.9095C248.903 13.8088 248.688 14.6258 248.257 15.3605C247.827 16.0952 247.244 16.6778 246.509 17.1085C245.775 17.5265 244.964 17.7355 244.077 17.7355C243.178 17.7355 242.361 17.5265 241.626 17.1085C240.892 16.6778 240.309 16.0952 239.878 15.3605C239.46 14.6258 239.251 13.8088 239.251 12.9095C239.251 12.0102 239.46 11.1932 239.878 10.4585C240.309 9.71117 240.892 9.1285 241.626 8.7105C242.374 8.27983 243.191 8.0645 244.077 8.0645ZM241.322 12.9095C241.322 13.4288 241.443 13.9038 241.683 14.3345C241.924 14.7525 242.253 15.0882 242.671 15.3415C243.102 15.5822 243.571 15.7025 244.077 15.7025C244.584 15.7025 245.046 15.5822 245.464 15.3415C245.882 15.0882 246.212 14.7525 246.452 14.3345C246.693 13.9038 246.813 13.4288 246.813 12.9095C246.813 12.3902 246.693 11.9152 246.452 11.4845C246.212 11.0538 245.882 10.7182 245.464 10.4775C245.046 10.2242 244.584 10.0975 244.077 10.0975C243.571 10.0975 243.102 10.2242 242.671 10.4775C242.253 10.7182 241.924 11.0538 241.683 11.4845C241.443 11.9152 241.322 12.3902 241.322 12.9095Z" fill="#FF7557"/>
            <path d="M231.37 4.2455C232.611 4.2455 233.739 4.5305 234.752 5.1005C235.765 5.6705 236.563 6.46217 237.146 7.4755C237.729 8.47617 238.02 9.59717 238.02 10.8385C238.02 12.0925 237.729 13.2325 237.146 14.2585C236.563 15.2845 235.765 16.0888 234.752 16.6715C233.739 17.2542 232.611 17.5455 231.37 17.5455H227V4.2455H231.37ZM231.522 15.4175C232.32 15.4175 233.036 15.2212 233.669 14.8285C234.315 14.4232 234.822 13.8722 235.189 13.1755C235.556 12.4788 235.74 11.6998 235.74 10.8385C235.74 9.97717 235.556 9.21083 235.189 8.5395C234.834 7.8555 234.334 7.3235 233.688 6.9435C233.042 6.5635 232.32 6.3735 231.522 6.3735H229.223V15.4175H231.522Z" fill="#FF7557"/>
            <defs>
            <clipPath id="clip0_492_4794">
            <rect width="20.2191" height="20.161" fill="white" transform="translate(0 1.36534)"/>
            </clipPath>
            </defs>
            </svg>
        <% } else { %>
          <svg width="177" height="21" viewBox="0 0 177 21" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M173.169 17.509C172.422 17.509 171.675 17.3887 170.927 17.148C170.193 16.8947 169.597 16.5653 169.141 16.16L169.977 14.564C170.332 14.906 170.807 15.1847 171.402 15.4C172.01 15.6153 172.58 15.723 173.112 15.723C173.568 15.723 173.936 15.628 174.214 15.438C174.506 15.248 174.651 15.001 174.651 14.697C174.651 14.3803 174.499 14.1523 174.195 14.013C173.891 13.861 173.404 13.7027 172.732 13.538C172.048 13.3733 171.491 13.2087 171.06 13.044C170.63 12.8667 170.256 12.5943 169.939 12.227C169.635 11.8597 169.483 11.3657 169.483 10.745C169.483 10.175 169.635 9.66833 169.939 9.225C170.243 8.78167 170.668 8.43967 171.212 8.199C171.77 7.94567 172.397 7.819 173.093 7.819C173.765 7.819 174.411 7.914 175.031 8.104C175.652 8.294 176.165 8.57267 176.57 8.94L175.734 10.574C175.392 10.27 174.936 10.0293 174.366 9.852C173.809 9.67467 173.315 9.586 172.884 9.586C172.492 9.586 172.175 9.67467 171.934 9.852C171.706 10.0293 171.592 10.27 171.592 10.574C171.592 10.8653 171.738 11.0807 172.029 11.22C172.321 11.3593 172.796 11.5177 173.454 11.695C174.151 11.885 174.721 12.075 175.164 12.265C175.608 12.4423 175.988 12.721 176.304 13.101C176.634 13.481 176.798 13.9877 176.798 14.621C176.798 15.1783 176.64 15.6787 176.323 16.122C176.019 16.5527 175.589 16.8947 175.031 17.148C174.487 17.3887 173.866 17.509 173.169 17.509Z" fill="#FF7557"/>
            <path d="M168.17 14.944C167.816 15.7167 167.252 16.3373 166.479 16.806C165.719 17.262 164.858 17.49 163.895 17.49C162.983 17.49 162.154 17.281 161.406 16.863C160.672 16.445 160.089 15.8687 159.658 15.134C159.24 14.3867 159.031 13.5633 159.031 12.664C159.031 11.7647 159.24 10.9477 159.658 10.213C160.089 9.46567 160.672 8.883 161.406 8.465C162.154 8.03433 162.977 7.819 163.876 7.819C164.776 7.819 165.58 8.028 166.289 8.446C167.011 8.85133 167.543 9.396 167.885 10.08L166.099 11.087C165.909 10.707 165.612 10.4093 165.206 10.194C164.814 9.966 164.37 9.852 163.876 9.852C163.078 9.852 162.42 10.1243 161.9 10.669C161.381 11.201 161.121 11.866 161.121 12.664C161.121 13.1833 161.242 13.6583 161.482 14.089C161.723 14.507 162.052 14.8427 162.47 15.096C162.901 15.3367 163.376 15.457 163.895 15.457C164.44 15.457 164.921 15.3367 165.339 15.096C165.757 14.8553 166.074 14.5197 166.289 14.089L168.17 14.944Z" fill="#FF7557"/>
            <path d="M153.077 7.819C153.964 7.819 154.775 8.03433 155.509 8.465C156.244 8.883 156.827 9.46567 157.257 10.213C157.688 10.9477 157.903 11.7647 157.903 12.664C157.903 13.5633 157.688 14.3803 157.257 15.115C156.827 15.8497 156.244 16.4323 155.509 16.863C154.775 17.281 153.964 17.49 153.077 17.49C152.178 17.49 151.361 17.281 150.626 16.863C149.892 16.4323 149.309 15.8497 148.878 15.115C148.46 14.3803 148.251 13.5633 148.251 12.664C148.251 11.7647 148.46 10.9477 148.878 10.213C149.309 9.46567 149.892 8.883 150.626 8.465C151.374 8.03433 152.191 7.819 153.077 7.819ZM150.322 12.664C150.322 13.1833 150.443 13.6583 150.683 14.089C150.924 14.507 151.253 14.8427 151.671 15.096C152.102 15.3367 152.571 15.457 153.077 15.457C153.584 15.457 154.046 15.3367 154.464 15.096C154.882 14.8427 155.212 14.507 155.452 14.089C155.693 13.6583 155.813 13.1833 155.813 12.664C155.813 12.1447 155.693 11.6697 155.452 11.239C155.212 10.8083 154.882 10.4727 154.464 10.232C154.046 9.97867 153.584 9.852 153.077 9.852C152.571 9.852 152.102 9.97867 151.671 10.232C151.253 10.4727 150.924 10.8083 150.683 11.239C150.443 11.6697 150.322 12.1447 150.322 12.664Z" fill="#FF7557"/>
            <path d="M140.37 4C141.611 4 142.739 4.285 143.752 4.855C144.765 5.425 145.563 6.21667 146.146 7.23C146.729 8.23067 147.02 9.35167 147.02 10.593C147.02 11.847 146.729 12.987 146.146 14.013C145.563 15.039 144.765 15.8433 143.752 16.426C142.739 17.0087 141.611 17.3 140.37 17.3H136V4H140.37ZM140.522 15.172C141.32 15.172 142.036 14.9757 142.669 14.583C143.315 14.1777 143.822 13.6267 144.189 12.93C144.556 12.2333 144.74 11.4543 144.74 10.593C144.74 9.73167 144.556 8.96533 144.189 8.294C143.834 7.61 143.334 7.078 142.688 6.698C142.042 6.318 141.32 6.128 140.522 6.128H138.223V15.172H140.522Z" fill="#FF7557"/>
            <g clip-path="url(#clip0_492_4833)">
            <rect x="2.40698" y="3.66257" width="15.2442" height="14.4419" fill="#FFD6CD"/>
            <path fill-rule="evenodd" clip-rule="evenodd" d="M5.68667 6.08191H4.3376e-05V0.411621H5.68667V6.08191ZM20.2194 6.08193H14.5328V0.411641H20.2194V6.08193ZM0 20.5728H5.68663V14.9025H0V20.5728ZM20.2194 20.5728H14.5328V14.9025H20.2194V20.5728ZM1.89527 6.08201H3.79081V14.9025H1.89527V6.08201ZM18.3237 6.08201H16.4281V14.9025H18.3237V6.08201ZM5.68677 2.30186H14.5326V4.19196H5.68677V2.30186ZM14.5326 16.7923H5.68677V18.6824H14.5326V16.7923Z" fill="#FF7557"/>
            </g>
            <path d="M27.7201 3.91101V15.0629H34.0736V17.187H25.5011V3.91101H27.7201Z" fill="#12110D"/>
            <path d="M44.3993 17.187H42.2751V15.8974C42.0475 16.3273 41.6556 16.6813 41.0992 16.9594C40.5429 17.2376 39.9487 17.3767 39.3165 17.3767C38.4946 17.3767 37.7423 17.1617 37.0595 16.7319C36.3768 16.302 35.8394 15.7203 35.4474 14.987C35.0555 14.2537 34.8595 13.4381 34.8595 12.5404C34.8595 11.6554 35.0555 10.8462 35.4474 10.1128C35.8394 9.37947 36.3768 8.79785 37.0595 8.36796C37.7423 7.93807 38.4946 7.72313 39.3165 7.72313C39.9487 7.72313 40.5429 7.86221 41.0992 8.14037C41.6556 8.41854 42.0475 8.77256 42.2751 9.20245V7.91278H44.3993V17.187ZM36.9268 12.5404C36.9268 13.0715 37.0469 13.5519 37.2871 13.9818C37.54 14.4117 37.8814 14.7531 38.3113 15.006C38.7538 15.2462 39.2343 15.3663 39.7527 15.3663C40.2711 15.3663 40.7642 15.2336 41.232 14.968C41.6998 14.7025 42.0475 14.3611 42.2751 13.9439V11.1749C42.0602 10.7576 41.7188 10.4163 41.251 10.1507C40.7831 9.87258 40.2837 9.73349 39.7527 9.73349C39.2216 9.73349 38.7412 9.85993 38.3113 10.1128C37.8814 10.353 37.54 10.6881 37.2871 11.118C37.0469 11.5352 36.9268 12.0094 36.9268 12.5404Z" fill="#12110D"/>
            <path d="M48.7183 9.20245C48.9459 8.77256 49.3378 8.41854 49.8942 8.14037C50.4631 7.86221 51.0574 7.72313 51.6769 7.72313C52.4988 7.72313 53.2511 7.93807 53.9339 8.36796C54.6166 8.79785 55.154 9.37947 55.5459 10.1128C55.9379 10.8462 56.1339 11.6554 56.1339 12.5404C56.1339 13.4381 55.9379 14.2537 55.5459 14.987C55.154 15.7203 54.6166 16.302 53.9339 16.7319C53.2511 17.1617 52.4988 17.3767 51.6769 17.3767C51.0574 17.3767 50.4631 17.2376 49.8942 16.9594C49.3378 16.6813 48.9459 16.3273 48.7183 15.8974V17.187H46.6131V3.91101H48.7183V9.20245ZM48.7183 13.9439C48.9459 14.3611 49.2936 14.7025 49.7614 14.968C50.2292 15.2336 50.7223 15.3663 51.2407 15.3663C51.7591 15.3663 52.2333 15.2462 52.6632 15.006C53.1057 14.7531 53.4471 14.4117 53.6873 13.9818C53.9402 13.5519 54.0666 13.0715 54.0666 12.5404C54.0666 12.0094 53.9402 11.5352 53.6873 11.118C53.4471 10.6881 53.112 10.353 52.6821 10.1128C52.2522 9.85993 51.7718 9.73349 51.2407 9.73349C50.7097 9.73349 50.2103 9.87258 49.7424 10.1507C49.2746 10.4163 48.9332 10.7576 48.7183 11.1749V13.9439Z" fill="#12110D"/>
            <path d="M62.05 17.3767C61.1776 17.3767 60.3747 17.1681 59.6414 16.7508C58.908 16.3209 58.3264 15.733 57.8965 14.987C57.4666 14.241 57.2517 13.4128 57.2517 12.5025C57.2517 11.6174 57.4603 10.8145 57.8776 10.0938C58.3074 9.3605 58.8891 8.78521 59.6224 8.36796C60.3557 7.93807 61.1586 7.72313 62.0311 7.72313C62.8529 7.72313 63.6052 7.91911 64.288 8.31106C64.9707 8.70302 65.5081 9.23406 65.9001 9.90419C66.292 10.5743 66.488 11.3203 66.488 12.1421C66.488 12.3697 66.4627 12.6732 66.4121 13.0525H59.3C59.4138 13.8238 59.7172 14.4307 60.2103 14.8732C60.7161 15.3157 61.3546 15.537 62.1259 15.537C62.6443 15.537 63.1247 15.4295 63.5673 15.2146C64.0225 14.987 64.3512 14.6962 64.5535 14.3422L66.2225 15.3663C65.8811 15.9985 65.3374 16.4916 64.5914 16.8456C63.8581 17.1997 63.0109 17.3767 62.05 17.3767ZM64.3449 11.5352C64.269 10.941 64.0035 10.4542 63.5483 10.0749C63.0931 9.69556 62.5558 9.5059 61.9362 9.5059C61.2661 9.5059 60.7034 9.68924 60.2483 10.0559C59.8057 10.4099 59.5086 10.903 59.3569 11.5352H64.3449Z" fill="#12110D"/>
            <path d="M70.3511 17.187H68.2459V3.91101H70.3511V17.187Z" fill="#12110D"/>
            <path d="M81.1662 3.72135C82.0133 3.72135 82.8604 3.90469 83.7076 4.27136C84.5673 4.62539 85.2248 5.07424 85.68 5.61793L84.3903 7.34381C83.9478 6.88863 83.4168 6.52828 82.7972 6.26276C82.1903 5.99724 81.5897 5.86448 80.9955 5.86448C80.3886 5.86448 79.8891 6.00356 79.4972 6.28173C79.1179 6.55989 78.9282 6.92024 78.9282 7.36278C78.9282 7.74209 79.0483 8.05819 79.2885 8.31106C79.5414 8.56394 79.8512 8.76624 80.2179 8.91797C80.5972 9.05705 81.1156 9.22142 81.7731 9.41108C82.6708 9.66395 83.3978 9.92315 83.9541 10.1887C84.5231 10.4415 85.0036 10.8272 85.3955 11.3456C85.8001 11.8513 86.0024 12.5215 86.0024 13.356C86.0024 14.0766 85.8064 14.7468 85.4145 15.3663C85.0352 15.9732 84.4852 16.46 83.7645 16.8267C83.0438 17.1934 82.2093 17.3767 81.261 17.3767C80.2748 17.3767 79.3012 17.1617 78.3403 16.7319C77.3793 16.302 76.6017 15.7583 76.0075 15.1008L77.4299 13.4697C78.0115 14.0261 78.6058 14.4623 79.2127 14.7784C79.8196 15.0818 80.5023 15.2336 81.261 15.2336C81.9943 15.2336 82.5886 15.0629 83.0438 14.7215C83.4989 14.3801 83.7265 13.9312 83.7265 13.3749C83.7265 13.0082 83.6064 12.7111 83.3662 12.4835C83.1259 12.2433 82.8225 12.0536 82.4558 11.9146C82.0892 11.7755 81.5834 11.6174 80.9386 11.4404C80.0408 11.2002 79.3075 10.9599 78.7385 10.7197C78.1822 10.4668 77.7017 10.0875 77.2971 9.58177C76.8925 9.06337 76.6902 8.3806 76.6902 7.53347C76.6902 6.82541 76.8799 6.18058 77.2592 5.59896C77.6512 5.01735 78.1822 4.56217 78.8523 4.23343C79.5351 3.89205 80.3064 3.72135 81.1662 3.72135Z" fill="#12110D"/>
            <path d="M91.6478 17.3198C90.5984 17.3198 89.7513 17.0037 89.1064 16.3715C88.4742 15.7267 88.1581 14.8416 88.1581 13.7163V9.92315H86.3754V7.91278H88.1581V5.42827H90.2633V7.91278H92.6909V9.92315H90.2633V13.6215C90.2633 14.1525 90.4214 14.5761 90.7375 14.8922C91.0662 15.1956 91.4708 15.3474 91.9513 15.3474C92.1789 15.3474 92.4191 15.3094 92.672 15.2336L92.7858 17.0922C92.4823 17.2439 92.103 17.3198 91.6478 17.3198Z" fill="#12110D"/>
            <path d="M100.887 15.9543C100.609 16.3841 100.217 16.7319 99.7109 16.9974C99.2052 17.2503 98.6615 17.3767 98.0799 17.3767C96.9167 17.3767 95.9937 17.0037 95.3109 16.2577C94.6281 15.4991 94.2867 14.4813 94.2867 13.2042V7.91278H96.3919V12.8628C96.3919 13.6088 96.5879 14.1968 96.9799 14.6267C97.3845 15.0565 97.9218 15.2715 98.592 15.2715C99.0598 15.2715 99.496 15.1577 99.9006 14.9301C100.318 14.7025 100.647 14.3927 100.887 14.0008L100.906 7.91278H103.03V17.187H100.887V15.9543Z" fill="#12110D"/>
            <path d="M112.193 11.1749C111.978 10.7576 111.636 10.4163 111.169 10.1507C110.701 9.87258 110.201 9.73349 109.67 9.73349C109.139 9.73349 108.659 9.85993 108.229 10.1128C107.799 10.353 107.458 10.6881 107.205 11.118C106.965 11.5352 106.844 12.0094 106.844 12.5404C106.844 13.0715 106.965 13.5519 107.205 13.9818C107.458 14.4117 107.799 14.7531 108.229 15.006C108.671 15.2462 109.152 15.3663 109.67 15.3663C110.189 15.3663 110.682 15.2336 111.15 14.968C111.617 14.7025 111.965 14.3611 112.193 13.9439V11.1749ZM104.777 12.5404C104.777 11.6554 104.973 10.8462 105.365 10.1128C105.757 9.37947 106.294 8.79785 106.977 8.36796C107.66 7.93807 108.412 7.72313 109.234 7.72313C109.866 7.72313 110.461 7.86221 111.017 8.14037C111.573 8.41854 111.965 8.77256 112.193 9.20245V3.91101H114.298V17.187H112.193V15.8974C111.965 16.3273 111.573 16.6813 111.017 16.9594C110.461 17.2376 109.866 17.3767 109.234 17.3767C108.412 17.3767 107.66 17.1617 106.977 16.7319C106.294 16.302 105.757 15.7203 105.365 14.987C104.973 14.2537 104.777 13.4381 104.777 12.5404Z" fill="#12110D"/>
            <path d="M118.636 7.91278V17.187H116.531V7.91278H118.636ZM117.593 3.60756C117.959 3.60756 118.276 3.74032 118.541 4.00584C118.819 4.27136 118.958 4.58746 118.958 4.95413C118.958 5.33344 118.819 5.65586 118.541 5.92138C118.276 6.1869 117.959 6.31966 117.593 6.31966C117.226 6.31966 116.91 6.1869 116.645 5.92138C116.379 5.65586 116.246 5.33344 116.246 4.95413C116.246 4.58746 116.379 4.27136 116.645 4.00584C116.91 3.74032 117.226 3.60756 117.593 3.60756Z" fill="#12110D"/>
            <path d="M125.208 7.72313C126.093 7.72313 126.902 7.93807 127.635 8.36796C128.369 8.78521 128.95 9.36682 129.38 10.1128C129.81 10.8462 130.025 11.6617 130.025 12.5594C130.025 13.4571 129.81 14.2726 129.38 15.006C128.95 15.7393 128.369 16.3209 127.635 16.7508C126.902 17.1681 126.093 17.3767 125.208 17.3767C124.31 17.3767 123.495 17.1681 122.761 16.7508C122.028 16.3209 121.446 15.7393 121.016 15.006C120.599 14.2726 120.391 13.4571 120.391 12.5594C120.391 11.6617 120.599 10.8462 121.016 10.1128C121.446 9.36682 122.028 8.78521 122.761 8.36796C123.507 7.93807 124.323 7.72313 125.208 7.72313ZM122.458 12.5594C122.458 13.0778 122.578 13.5519 122.818 13.9818C123.058 14.3991 123.387 14.7341 123.804 14.987C124.234 15.2272 124.702 15.3474 125.208 15.3474C125.714 15.3474 126.175 15.2272 126.592 14.987C127.01 14.7341 127.338 14.3991 127.579 13.9818C127.819 13.5519 127.939 13.0778 127.939 12.5594C127.939 12.041 127.819 11.5668 127.579 11.137C127.338 10.7071 127.01 10.372 126.592 10.1318C126.175 9.8789 125.714 9.75246 125.208 9.75246C124.702 9.75246 124.234 9.8789 123.804 10.1318C123.387 10.372 123.058 10.7071 122.818 11.137C122.578 11.5668 122.458 12.041 122.458 12.5594Z" fill="#12110D"/>
            <defs>
            <clipPath id="clip0_492_4833">
            <rect width="20.2191" height="20.161" fill="white" transform="translate(0 0.411621)"/>
            </clipPath>
            </defs>
            </svg>            
        <% } %>
      </a>
    </div>

    <div class="AlgoliaSearch">
      <svg width="19" height="19" viewBox="0 0 19 19" fill="none" xmlns="http://www.w3.org/2000/svg" class="AlgoliaSearchIcon">
        <path d="M8 16C9.77498 15.9996 11.4988 15.4054 12.897 14.312L17.293 18.708L18.707 17.294L14.311 12.898C15.405 11.4997 15.9996 9.77544 16 8C16 3.589 12.411 0 8 0C3.589 0 0 3.589 0 8C0 12.411 3.589 16 8 16ZM8 2C11.309 2 14 4.691 14 8C14 11.309 11.309 14 8 14C4.691 14 2 11.309 2 8C2 4.691 4.691 2 8 2Z" />
      </svg>
      <input id="docsearch-input" placeholder="Search docs" data-site-version="<%- tier %>" />
      <svg width="43" height="31" viewBox="0 0 43 31" fill="none" xmlns="http://www.w3.org/2000/svg" class="AlgoliaSearchCmdK">
        <rect y="0.0457764" width="43" height="30" rx="15" fill="var(--cmd-k-background-color)"/>
        <path d="M11.264 21.0458C9.776 21.0458 8.816 20.1018 8.816 18.6298C8.816 17.0778 9.744 16.1658 11.568 16.1658H12.512V14.4058H11.568C9.744 14.4058 8.816 13.4938 8.816 11.9418C8.816 10.4698 9.776 9.52578 11.264 9.52578C13.056 9.52578 13.632 10.6458 13.632 11.9898V13.3178H15.392V11.9898C15.392 10.6458 15.968 9.52578 17.76 9.52578C19.248 9.52578 20.208 10.4698 20.208 11.9418C20.208 13.4938 19.28 14.4058 17.456 14.4058H16.512V16.1658H17.456C19.28 16.1658 20.208 17.0778 20.208 18.6298C20.208 20.1018 19.248 21.0458 17.76 21.0458C15.968 21.0458 15.392 19.9258 15.392 18.5818V17.2538H13.632V18.5818C13.632 19.9258 13.056 21.0458 11.264 21.0458ZM16.512 11.9578V13.3178H17.456C18.592 13.3178 19.072 12.8538 19.072 11.9418C19.072 10.9978 18.512 10.6298 17.76 10.6298C16.928 10.6298 16.512 11.1418 16.512 11.9578ZM11.568 13.3178H12.512V11.9578C12.512 11.1418 12.096 10.6298 11.264 10.6298C10.512 10.6298 9.952 10.9978 9.952 11.9418C9.952 12.8538 10.432 13.3178 11.568 13.3178ZM13.632 16.1658H15.392V14.4058H13.632V16.1658ZM11.264 19.9418C12.096 19.9418 12.512 19.4298 12.512 18.6138V17.2538H11.568C10.432 17.2538 9.952 17.7178 9.952 18.6298C9.952 19.5738 10.512 19.9418 11.264 19.9418ZM16.512 18.6138C16.512 19.4298 16.928 19.9418 17.76 19.9418C18.512 19.9418 19.072 19.5738 19.072 18.6298C19.072 17.7178 18.592 17.2538 17.456 17.2538H16.512V18.6138ZM27.3546 15.4298V21.0458H26.1546V9.84578H27.3546V14.9338L32.3146 9.84578H33.8826L28.6346 15.1738L34.3626 21.0458H32.7466L27.3546 15.4298Z" fill="var(--cmd-k-icon-color)"/>
      </svg>        
    </div>

    <button class="hamburger-button" aria-label="Toggle Menu">
      <span></span>
      <span></span>
      <span></span>
    </button>
    <nav>
      <% if(page.type !== "playground" && page.type !== "blog") { %>
        <%- partial("partials/toc", { type: page.type, tier: tier, component: "nav" }) %>
      <% } %>
      <ul class="page-header-main-nav">
        <% if(isEnterpriseTheme) { %>
          <li>
            <a href="https://humansignal.com/platform">Platform</a>
          </li>
          <li>
            <a href="https://humansignal.com/pricing">Pricing</a>
          </li>
          <li>
            <a href="https://humansignal.com/blog">Blog</a>
          </li>
          <li>
            <a href="http://app.heartex.com/" target="_blank" rel="noopener noreferrer">Log In</a>
          </li>
          <li>
            <%- partial("component/button", { url: "https://app.heartex.com/user/trial", label: "Free Trial", }) %>
          </li>
        <% } else { %>
          <li>
            <a href="https://labelstud.io/learn/">
              Learn
              <svg aria-hidden="true" width="10" height="6" viewBox="0 0 10 6" fill="none" xmlns="http://www.w3.org/2000/svg" class="page-header-chevron-icon"><path d="M1 0.5L5 4.5L9 0.5" stroke="#131522"></path></svg>
            </a>
            <button>
              <svg width="13" height="8" viewBox="0 0 13 8" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
                <path fill-rule="evenodd" clip-rule="evenodd" d="M0.292969 0.805007L1.00008 0.0979004L6.64652 5.74435L12.293 0.0979004L13.0001 0.805007L6.64652 7.15856L0.292969 0.805007Z" />
              </svg>
            </button>
            <ul>
              <li>
                <a href="https://labelstud.io/blog/">Blog</a>
              </li>
              <li>
                <a href="https://labelstud.io/videos/">Videos</a>
              </li>
              <li class="headerSeparator">
                <a href="https://labelstud.io/community/">
                  Community
                </a>
              </li>
              <li>
                <a href="https://labelstud.io/academic/">Academic Program</a>
              </li>
            </ul>
          </li>
          <li>
            <a href="https://labelstud.io/integrations">Integrations</a>
          </li>
          <li>
            <a href="https://humansignal.com/goenterprise" target="_blank" rel="noopener noreferrer">Enterprise</a>
          </li>
          <li>
            <a href="https://github.com/HumanSignal/label-studio/" class="github-stars" target="_blank" rel="noopener noreferrer">
              <svg class="github-icon" viewBox="0 0 24 24" width="24" height="24"><path fill="black" d="M12 2A10 10 0 0 0 2 12c0 4.42 2.87 8.17 6.84 9.5.5.08.66-.23.66-.5v-1.69c-2.77.6-3.36-1.34-3.36-1.34-.46-1.16-1.11-1.47-1.11-1.47-.91-.62.07-.6.07-.6 1 .07 1.53 1.03 1.53 1.03.87 1.52 2.34 1.07 2.91.83.09-.65.35-1.09.63-1.34-2.22-.25-4.55-1.11-4.55-4.92 0-1.11.38-2 1.03-2.71-.1-.25-.45-1.29.1-2.64 0 0 .84-.27 2.75 1.02.79-.22 1.65-.33 2.5-.33.85 0 1.71.11 2.5.33 1.91-1.29 2.75-1.02 2.75-1.02.55 1.35.2 2.39.1 2.64.65.71 1.03 1.6 1.03 2.71 0 3.82-2.34 4.66-4.57 4.91.36.31.69.92.69 1.85V21c0 .27.16.59.67.5C19.14 20.16 22 16.42 22 12A10 10 0 0 0 12 2z"></path></svg>
              <%- partial("component/text", {text: "", size: "Eyebrow", tag: "span", customClass: "github-stars-count"}) %>
            </a>
          </li>
          <li>
            <%- partial("component/button", { url: "/guide/quick_start.html", label: "Quick start", }) %>
          </li>
          <% } %>
      </ul>
    </nav>
    </div>
  </div>
  
  <% const quickLinks = [
      {
        label: "Docs",
        href: "/guide/",
        tier: "all"
      },
      {
        label: "Templates",
        href: "/templates/",
        tier: "all"
      },
      {
        label: "Plugins",
        href: "/plugins/",
        tier: "enterprise"
      },
      {
        label: "Tags",
        href: "/tags/",
        tier: "all"
      }
    ];
      const currentQuickLink = quickLinks.find(link => link.href.includes(page.type));

    %>
    <% if(quickLinks && quickLinks.length > 0) {%>
      <ul class="page-header-content-switcher">
      <% quickLinks.forEach(function(link) { %>
          <% if(link.tier === "all" || link.tier === tier) { %>
          <li><a href="<%- link.href %>" class="<%- link.href === currentQuickLink?.href ? 'active' : '' %>"><%- link.label %></a></li>
          <% }; %>
        <% }); %>
      <li>
        <a href="https://api.labelstud.io/api-reference/introduction/getting-started" target="_blank">
          API
          <svg width="8" height="8" viewBox="0 0 8 8" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M7.93006 6.05393L6.92213 6.06052L6.92213 1.88389L1.51359 7.29243L0.788936 6.56777L6.19748 1.15923L2.02743 1.16582L2.02743 0.151308L7.93006 0.151308L7.93006 6.05393Z" fill="#ffffff"/>
            </svg>            
        </a>
      </li>
    </ul>
    <% } %>
  
</header>

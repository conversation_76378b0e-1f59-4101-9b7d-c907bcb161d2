<% const tier = theme.tier || "all" %> <% const isEnterpriseTheme = tier ===
"enterprise" %>

<footer class="Footer">
  <section class="FooterWrapper">
    <svg
      width="106"
      height="140"
      viewBox="0 0 106 140"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      class="FooterCTASquares"
    >
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M0.463783 35.0679H70.6628V70.1674H35.5633V105.267L70.6628 105.267H0.463783V35.0679Z"
        fill="#FF7557"
      />
      <rect
        width="35.1784"
        height="35.1784"
        transform="matrix(-1 0 0 1 105.765 0)"
        fill="#FFA663"
      />
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M105.764 140H35.5645V69.8009H105.764V140ZM70.6641 104.901H35.5645V69.801H70.6641V104.901Z"
        fill="#E37BD3"
      />
    </svg>
    <div class="FooterGrid">
      <div class="FooterSocialsLogo">
        <svg
          width="341"
          height="47"
          viewBox="0 0 341 47"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
          class="FooterSocialsLogoHumanSignal"
        >
          <path
            fill-rule="evenodd"
            clip-rule="evenodd"
            d="M15.1735 1C13.3161 1 11.8103 2.50578 11.8103 4.36324V21.2559C11.8103 21.2583 11.8103 21.2606 11.8103 21.263V12.8097H23.6195V24.6192H32.0655C33.923 24.6192 35.4288 23.1134 35.4288 21.2559V4.36324C35.4288 2.50578 33.923 1 32.0655 1H15.1735Z"
            fill="#FFA663"
          />
          <path
            fill-rule="evenodd"
            clip-rule="evenodd"
            d="M20.2552 36.4287C22.1126 36.4287 23.6184 34.9229 23.6184 33.0655V24.619H11.8093V12.8095H3.36318C1.50571 12.8095 -6.48499e-05 14.3153 -6.48499e-05 16.1728V33.0655C-6.48499e-05 34.9229 1.50571 36.4287 3.36318 36.4287H20.2552Z"
            fill="#FF7557"
          />
          <path
            d="M221.794 0.463379C214.974 0.463379 210.036 4.67047 210.036 10.2968C210.036 17.514 216.035 19.1647 221.36 20.6299C225.609 21.7991 229.429 22.8501 229.429 26.517C229.429 29.9131 226.375 32.2447 222.048 32.2447C217.62 32.2447 214.261 30.2679 211.512 27.6828L208.408 31.3323C211.512 34.5256 216.653 36.9587 222.048 36.9587C229.683 36.9587 234.468 32.0926 234.468 26.4663C234.468 19.4096 228.565 17.7094 223.256 16.1802C218.916 14.9303 214.974 13.7946 214.974 9.94202C214.974 7.15418 217.671 5.17735 221.438 5.17735C224.848 5.17735 228.309 6.6473 230.548 9.08032L233.45 5.27873C231.159 2.4909 226.273 0.463379 221.794 0.463379Z"
            fill="#FDFDFC"
          />
          <path
            d="M69.3012 0.970258V15.7204H50.6725V0.970258H45.7354V36.4518H50.6725V20.3837H69.3012V36.4518H74.2384V0.970258H69.3012Z"
            fill="#FDFDFC"
          />
          <path
            d="M98.1522 11.7161V28.3924C96.778 30.8254 93.9785 32.3461 91.0773 32.3461C87.1582 32.3461 84.5624 29.5582 84.5624 25.1991V11.7161H79.8288V25.9087C79.8288 32.5995 83.8498 36.9587 90.0085 36.9587C93.4187 36.9587 96.6253 35.3367 98.1522 32.8529V36.4518H102.835V11.7161H98.1522Z"
            fill="#FDFDFC"
          />
          <path
            d="M128.74 15.6191C130.42 13.3888 133.423 11.2092 137.189 11.2092C143.246 11.2092 147.318 15.2136 147.318 21.4989V36.4518H142.635V22.3099C142.635 18.1535 140.243 15.5684 136.426 15.5684C133.83 15.5684 131.234 17.3424 129.962 19.4713C130.063 19.9274 130.114 20.4342 130.165 20.9409L130.165 20.9413V36.4518H125.483V22.3099C125.483 18.1535 123.039 15.5684 119.375 15.5684C116.575 15.5684 114.234 17.1397 113.063 19.4207V36.4518H108.33V11.7161H113.063V15.2136C114.285 13.4395 116.779 11.2092 120.851 11.2092C124.516 11.2092 127.162 12.6792 128.74 15.6191Z"
            fill="#FDFDFC"
          />
          <path
            fill-rule="evenodd"
            clip-rule="evenodd"
            d="M171.444 11.7161V15.3149C170.12 12.9833 166.659 11.2092 163.147 11.2092C156.276 11.2092 151.034 16.8356 151.034 24.0839C151.034 31.3323 156.276 36.9587 163.147 36.9587C166.608 36.9587 170.069 35.1846 171.444 32.8529V36.4518H176.177V11.7161H171.444ZM164.063 32.5488C159.279 32.5488 155.614 28.8993 155.614 24.0839C155.614 19.2179 159.279 15.6191 164.063 15.6191C167.117 15.6191 170.12 17.3425 171.444 19.7755V28.4431C170.12 30.8254 167.117 32.5488 164.063 32.5488Z"
            fill="#FDFDFC"
          />
          <path
            d="M186.337 15.3149C187.864 12.8312 191.071 11.2092 194.43 11.2092C200.639 11.2092 204.61 15.5684 204.61 22.3099V36.4518H199.927V22.9688C199.927 18.6096 197.331 15.8218 193.361 15.8218C190.511 15.8218 187.711 17.3425 186.337 19.7755V36.4518H181.604V11.7161H186.337V15.3149Z"
            fill="#FDFDFC"
          />
          <path
            d="M243.809 36.4518H239.076V11.7161H243.809V36.4518Z"
            fill="#FDFDFC"
          />
          <path
            fill-rule="evenodd"
            clip-rule="evenodd"
            d="M268.752 11.7161V15.2642C267.429 12.9326 263.967 11.2092 260.456 11.2092C253.584 11.2092 248.342 16.7342 248.342 23.8305C248.342 30.9775 253.584 36.5025 260.456 36.5025C263.917 36.5025 267.378 34.7791 268.752 32.4981V35.5394C268.752 39.6958 265.545 42.6357 261.27 42.6357C257.758 42.6357 254.551 41.4699 252.668 39.4424L250.276 42.6357C252.414 45.2715 256.994 46.8428 261.372 46.8428C268.396 46.8428 273.485 42.3823 273.485 35.5901V11.7161H268.752ZM261.372 32.1433C256.587 32.1433 252.923 28.5952 252.923 23.8305C252.923 19.0658 256.587 15.5684 261.372 15.5684C264.426 15.5684 267.429 17.2411 268.752 19.6234V28.139C267.429 30.4706 264.426 32.1433 261.372 32.1433Z"
            fill="#FDFDFC"
          />
          <path
            d="M283.431 15.3149C284.957 12.8312 288.164 11.2092 291.523 11.2092C297.733 11.2092 301.703 15.5684 301.703 22.3099V36.4518H297.02V22.9688C297.02 18.6096 294.425 15.8218 290.455 15.8218C287.604 15.8218 284.805 17.3425 283.431 19.7755V36.4518H278.697V11.7161H283.431V15.3149Z"
            fill="#FDFDFC"
          />
          <path
            fill-rule="evenodd"
            clip-rule="evenodd"
            d="M325.855 11.7161V15.3149C324.532 12.9833 321.071 11.2092 317.559 11.2092C310.688 11.2092 305.445 16.8356 305.445 24.0839C305.445 31.3323 310.688 36.9587 317.559 36.9587C321.02 36.9587 324.481 35.1846 325.855 32.8529V36.4518H330.589V11.7161H325.855ZM318.475 32.5488C313.691 32.5488 310.026 28.8993 310.026 24.0839C310.026 19.2179 313.691 15.6191 318.475 15.6191C321.529 15.6191 324.532 17.3425 325.855 19.7755V28.4431C324.532 30.8254 321.529 32.5488 318.475 32.5488Z"
            fill="#FDFDFC"
          />
          <path
            d="M336.015 0.970258H340.749V36.4518H336.015V0.970258Z"
            fill="#FDFDFC"
          />
          <path
            d="M241.441 2.2885C239.969 2.2885 238.757 3.54391 238.757 4.97249C238.757 6.44436 239.969 7.65648 241.441 7.65648C242.87 7.65648 244.125 6.44436 244.125 4.97249C244.125 3.54391 242.87 2.2885 241.441 2.2885Z"
            fill="#FDFDFC"
          />
        </svg>

        <nav class="SocialIcons FooterSocials" arial-label="Social media links">
          <ul>
            <li>
              <a
                href="https://slack.labelstud.io/?source=site"
                aria-label="Slack"
                target="_blank"
                rel="noopener noreferrer"
              >
                <svg viewBox="0 0 24 24" astro-icon="mdi:slack">
                  <path
                    fill="currentColor"
                    d="M6 15a2 2 0 0 1-2 2 2 2 0 0 1-2-2 2 2 0 0 1 2-2h2v2m1 0a2 2 0 0 1 2-2 2 2 0 0 1 2 2v5a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-5m2-8a2 2 0 0 1-2-2 2 2 0 0 1 2-2 2 2 0 0 1 2 2v2H9m0 1a2 2 0 0 1 2 2 2 2 0 0 1-2 2H4a2 2 0 0 1-2-2 2 2 0 0 1 2-2h5m8 2a2 2 0 0 1 2-2 2 2 0 0 1 2 2 2 2 0 0 1-2 2h-2v-2m-1 0a2 2 0 0 1-2 2 2 2 0 0 1-2-2V5a2 2 0 0 1 2-2 2 2 0 0 1 2 2v5m-2 8a2 2 0 0 1 2 2 2 2 0 0 1-2 2 2 2 0 0 1-2-2v-2h2m0-1a2 2 0 0 1-2-2 2 2 0 0 1 2-2h5a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-5z"
                  ></path>
                </svg>
              </a>
            </li>
            <li>
              <a
                href="https://github.com/HumanSignal/"
                aria-label="GitHub"
                target="_blank"
                rel="noopener noreferrer"
              >
                <svg viewBox="0 0 24 24" astro-icon="mdi:github">
                  <path
                    fill="currentColor"
                    d="M12 2A10 10 0 0 0 2 12c0 4.42 2.87 8.17 6.84 9.5.5.08.66-.23.66-.5v-1.69c-2.77.6-3.36-1.34-3.36-1.34-.46-1.16-1.11-1.47-1.11-1.47-.91-.62.07-.6.07-.6 1 .07 1.53 1.03 1.53 1.03.87 1.52 2.34 1.07 2.91.83.09-.65.35-1.09.63-1.34-2.22-.25-4.55-1.11-4.55-4.92 0-1.11.38-2 1.03-2.71-.1-.25-.45-1.29.1-2.64 0 0 .84-.27 2.75 1.02.79-.22 1.65-.33 2.5-.33.85 0 1.71.11 2.5.33 1.91-1.29 2.75-1.02 2.75-1.02.55 1.35.2 2.39.1 2.64.65.71 1.03 1.6 1.03 2.71 0 3.82-2.34 4.66-4.57 4.91.36.31.69.92.69 1.85V21c0 .27.16.59.67.5C19.14 20.16 22 16.42 22 12A10 10 0 0 0 12 2z"
                  ></path>
                </svg>
              </a>
            </li>
            <li>
              <a
                href="https://twitter.com/labelstudiohq"
                aria-label="Twitter"
                target="_blank"
                rel="noopener noreferrer"
              >
                <svg viewBox="0 0 24 24" astro-icon="mdi:twitter">
                  <path
                    fill="currentColor"
                    d="M22.46 6c-.77.35-1.6.58-2.46.69.88-.53 1.56-1.37 1.88-2.38-.83.5-1.75.85-2.72 1.05C18.37 4.5 17.26 4 16 4c-2.35 0-4.27 1.92-4.27 4.29 0 .34.04.67.11.98C8.28 9.09 5.11 7.38 3 4.79c-.37.63-.58 1.37-.58 2.15 0 1.49.75 2.81 1.91 3.56-.71 0-1.37-.2-1.95-.5v.03c0 2.08 1.48 3.82 3.44 4.21a4.22 4.22 0 0 1-1.93.07 4.28 4.28 0 0 0 4 2.98 8.521 8.521 0 0 1-5.33 1.84c-.34 0-.68-.02-1.02-.06C3.44 20.29 5.7 21 8.12 21 16 21 20.33 14.46 20.33 8.79c0-.19 0-.37-.01-.56.84-.6 1.56-1.36 2.14-2.23z"
                  ></path>
                </svg>
              </a>
            </li>
            <li>
              <a
                href="https://www.linkedin.com/company/humansignal/"
                aria-label="LinkedIn"
                target="_blank"
                rel="noopener noreferrer"
              >
                <svg viewBox="0 0 24 24" astro-icon="bx:bxl-linkedin">
                  <circle
                    cx="4.983"
                    cy="5.009"
                    r="2.188"
                    fill="currentColor"
                  ></circle>
                  <path
                    fill="currentColor"
                    d="M9.237 8.855v12.139h3.769v-6.003c0-1.584.298-3.118 2.262-3.118 1.937 0 1.961 1.811 1.961 3.218v5.904H21v-6.657c0-3.27-.704-5.783-4.526-5.783-1.835 0-3.065 1.007-3.568 1.96h-.051v-1.66H9.237zm-6.142 0H6.87v12.139H3.095z"
                  ></path>
                </svg>
              </a>
            </li>
            <li>
              <a
                href="https://www.youtube.com/channel/UCbloiVAlCYzBatZXk-b5rFQ"
                aria-label="Youtube"
                target="_blank"
                rel="noopener noreferrer"
              >
                <svg viewBox="0 0 24 24" astro-icon="mdi:youtube">
                  <path
                    fill="currentColor"
                    d="m10 15 5.19-3L10 9v6m11.56-7.83c.13.47.22 1.1.28 1.9.07.8.1 1.49.1 2.09L22 12c0 2.19-.16 3.8-.44 4.83-.25.9-.83 1.48-1.73 1.73-.47.13-1.33.22-2.65.28-1.3.07-2.49.1-3.59.1L12 19c-4.19 0-6.8-.16-7.83-.44-.9-.25-1.48-.83-1.73-1.73-.13-.47-.22-1.1-.28-1.9-.07-.8-.1-1.49-.1-2.09L2 12c0-2.19.16-3.8.44-4.83.25-.9.83-1.48 1.73-1.73.47-.13 1.33-.22 2.65-.28 1.3-.07 2.49-.1 3.59-.1L12 5c4.19 0 6.8.16 7.83.44.9.25 1.48.83 1.73 1.73z"
                  ></path>
                </svg>
              </a>
            </li>
          </ul>
        </nav>
      </div>
      <div class="FooterMenus">
        <nav class="FooterNav" aria-label="Footer navigation">
          <% if(isEnterpriseTheme) { %>
          <div>
            <h3 class="Text Eyebrow FooterNavHeading">Products</h3>
            <ul>
              <li>
                <a href="https://heartex.com/product">Enterprise</a>
              </li>
              <li>
                <a href="https://labelstud.io/">Community Edition</a>
              </li>
              <li>
                <a href="https://humansignal.com/platform/starter-cloud"
                  >Start Free Trial</a
                >
              </li>
              <li>
                <a href="https://heartex.com/get-demo">Schedule Demo</a>
              </li>
              <li>
                <a href="https://heartex.com/pricing">Pricing</a>
              </li>
            </ul>
          </div>
          <div>
            <h3 class="Text Eyebrow FooterNavHeading">Resources</h3>
            <ul>
              <li>
                <a
                  href="https://heartex.com/resources/data-labeling-guide-download"
                  >Guide</a
                >
              </li>
              <li>
                <a href="https://docs.humansignal.com">Documentation</a>
              </li>
              <li>
                <a href="https://heartex.com/blog">What’s New</a>
              </li>
            </ul>
          </div>
          <div>
            <h3 class="Text Eyebrow FooterNavHeading">Company</h3>
            <ul>
              <li>
                <a href="https://heartex.com/blog">Blog</a>
              </li>
              <li>
                <a href="https://heartex.com/company/about-us">About Us</a>
              </li>
              <li>
                <a href="https://heartex.com/careers">Careers</a>
              </li>
              <li>
                <a href="https://support.humansignal.com">Contact Us</a>
              </li>
            </ul>
          </div>
          <div>
            <h3 class="Text Eyebrow FooterNavHeading">Open Source</h3>
            <ul>
              <li>
                <a href="https://labelstud.io/">Install</a>
              </li>
              <li>
                <a href="http://slack.labelstud.io/?source=heartex-docs-footer"
                  >Community</a
                >
              </li>
              <li>
                <a href="https://github.com/HumanSignal/label-studio">GitHub</a>
              </li>
            </ul>
          </div>

          <% } else { %>
          <div>
            <h3 class="Text Eyebrow FooterNavHeading">Products</h3>
            <ul>
              <li>
                <a href="https://labelstud.io/">Community Edition</a>
              </li>
              <li>
                <a href="https://heartex.com/product">Enterprise</a>
              </li>
              <li>
                <a href="https://heartex.com/pricing">Pricing</a>
              </li>
            </ul>
          </div>
          <div>
            <h3 class="Text Eyebrow FooterNavHeading">Community</h3>
            <ul>
              <li>
                <a href="https://labelstud.io/blog">Blog</a>
              </li>
              <li>
                <a href="https://labelstud.io/community/">Newsletter</a>
              </li>
              <li>
                <a href="/?source=site">Slack</a>
              </li>
              <li>
                <a href="https://labelstud.io/videos">Webinars</a>
              </li>
            </ul>
          </div>
          <div>
            <h3 class="Text Eyebrow FooterNavHeading">Documentation</h3>
            <ul>
              <li>
                <a href="/guide">Quickstart</a>
              </li>
              <li>
                <a href="/guide/api">API Reference</a>
              </li>
              <li>
                <a href="/guide/sdk">SDK Reference</a>
              </li>
              <li>
                <a href="/tags">Customizable Tags</a>
              </li>
              <li>
                <a href="/templates">Labeling Templates</a>
              </li>
            </ul>
          </div>
          <div>
            <h3 class="Text Eyebrow FooterNavHeading">Company</h3>
            <ul>
              <li>
                <a href="https://heartex.com/company/about-us">About Us</a>
              </li>
              <li>
                <a href="https://heartex.com/careers">Careers</a>
              </li>
              <li>
                <a href="https://humansignal.com/contact-sales/">Contact</a>
              </li>
              <li>
                <a href="https://heartex.com/company/privacy-policy">Privacy</a>
              </li>
            </ul>
          </div>
          <% } %>
        </nav>
        <% if(!isEnterpriseTheme) { %>
        <div class="FooterCTA">
          <a class="Button FooterCTAButton" href="/guide/quick_start.html"
            ><span class="Heading XXSmall ButtonText astro-EXBSDJPD"
              >Quick Start</span
            ></a
          >
        </div>
        <% } %>
      </div>
    </div>
    <div class="FooterGridCopyright">
      <p class="Text Small FooterCopyright">
        ©
        <script>
          document.write(new Date().getFullYear());
        </script>
        HumanSignal, Inc.
      </p>
      <a href="https://heartex.com/company/privacy-policy" class="Text Small"
        >Privacy Policy</a
      >
    </div>
  </section>
</footer>

<% if (page.title.trim()) { %>
  <%- partial("component/heading", {text: page.title, size: "XLarge", tag: "h1", customClass: "home-page-title"}) %>
<% } %>

<div class="templates-grid">
  <% page.cards.forEach(function(card) { %>
    <% const tags = typeof card.categories === "object" ? card.categories.join(", ") : card.categories %>
    <div class="templates-card">
      <img src="<%= card.image %>" alt="" />
      <a href="<%= card.url %>"><%- partial("component/heading", {text: card.title, size: "XXSmall", tag: "span" }) %></a>
      <%- partial("component/text", {text: tags, tag: "p", size: "Small"}) %>
    </div>
  <% }); %>
</div>
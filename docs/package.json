{"name": "label-studio-documentation", "version": "0.0.1", "author": {"name": "Heartex Labs", "url": "https://github.com/heartexlabs"}, "private": true, "hexo": {"version": "6.3.0"}, "dependencies": {"@notionhq/client": "^2.2.3", "@sanity/client": "^3.4.1", "co": "^4.6.0", "concat-md": "^0.5.1", "dotenv": "^16.0.3", "front-matter": "^4.0.2", "hexo": "^6.3.0", "hexo-admonition": "^1.1.2", "hexo-cli": "^4.3.0", "hexo-deployer-git": "^3.0.0", "hexo-footnote": "^1.0.4", "hexo-fs": "^4.1.1", "hexo-generator-archive": "^0.1.5", "hexo-generator-category": "^0.1.3", "hexo-generator-index": "^0.2.1", "hexo-generator-search": "^2.4.3", "hexo-generator-sitemap": "^3.0.1", "hexo-generator-tag": "^0.2.0", "hexo-include": "^1.1.0", "hexo-insert-markdown": "^1.4.4", "hexo-ipynb": "^0.2.4", "hexo-jupyter-notebook": "0.0.3", "hexo-renderer-ejs": "^2.0.0", "hexo-renderer-marked": "^6.1.0", "hexo-renderer-stylus": "^0.3.3", "hexo-server": "^3.0.0", "node-html-parser": "^6.1.4", "notion-to-md": "https://github.com/huguestennier/notion-to-md", "save": "^2.4.0"}, "resolutions": {"follow-redirects": "^1.15.5", "hexo-footnote/hexo-util": "3.0.1", "markdown-it": "13.0.1"}, "scripts": {"clean": "hexo clean && touch db.json", "start": "hexo clean && touch db.json && hexo serve --config opensource.yml,_config.yml", "start:enterprise": "hexo clean && touch db.json && hexo serve --config enterprise.yml,_config.yml", "server": "hexo clean && touch db.json && hexo serve --config opensource.yml,_config.yml", "server:enterprise": "hexo clean && touch db.json && hexo serve --config enterprise.yml,_config.yml", "publish": "hexo clean && touch db.json && hexo generate --config opensource.yml,_config.yml && hexo deploy --config opensource.yml,_config.yml", "publish:enterprise": "hexo clean && touch db.json && hexo generate --config enterprise.yml,_config.yml && hexo deploy --config enterprise.yml,_config.yml", "build": "hexo clean && touch db.json && hexo generate --config opensource.yml,_config.yml", "build:enterprise": "hexo clean && touch db.json && hexo generate --config enterprise.yml,_config.yml", "new": "hexo new"}, "devDependencies": {"hexo-deployer-aws-s3": "^1.0.2"}}
[[redirects]]
from = "/redirect/guide/*"
to = "/guide/:splat"
status = 301
force = true

[[redirects]]
from = "https://heartex-docs.netlify.app/api/*"
to = "https://app.heartex.com/docs/api/:splat"
status = 301
force = true

[[redirects]]
from = "https://docs.humansignal.com/api/*"
to = "https://app.heartex.com/docs/api/:splat"
status = 301
force = true

[[redirects]]
from = "/guide/starter_cloud"
to = "https://humansignal.com/platform/starter-cloud/manage"
status = 301
force = true

[[redirects]]
from = "/guide/dataset_create"
to = "/guide"
status = 301
force = true

[[redirects]]
from = "/guide/dataset_manage"
to = "/guide"
status = 301
force = true

[[redirects]]
from = "/guide/dataset_overview"
to = "/guide"
status = 301
force = true

[[redirects]]
from = "/guide/dataset_search"
to = "/guide"
status = 301
force = true

[[redirects]]
from = "/guide/scripts"
to = "/guide/plugins"
status = 301
force = true

[[redirects]]
from = "/guide/script_examples"
to = "/plugins"
status = 301
force = true

[[redirects]]
from = "/"
to = "/guide"
status = 301
force = true

[[redirects]]
from = "/*"
to = "/404.html"
status = 404

[dev]
command = "yarn start"
targetPort = 4000

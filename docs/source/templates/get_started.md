---
title: Get Started with Label Studio Templates
type: templates
order: 50
meta_title: Get started with templates in Label Studio
meta_description: Label Studio Template library of data labeling and annotation configurations for various data types.
---

Label Studio templates provide you with a way to get started quickly with your data labeling process or machine learning workflow.

The templates are predefined labeling interfaces for specific use cases and data types. Use our templates to get started labeling right away, or as a starting point to build a fully customized labeling interface for your data labeling project.

Have a template that you're working on and want to share it with the community? Share it with us in the [Community Slack](https://slack.labelstud.io/?source=GitHub) or [open an issue on GitHub](https://github.com/HumanSignal/label-studio/issues/new/choose).

Need a hand with customizing our templates? Check out [our documentation on the labeling interface](/tags).

<a class="button" href="index.html">See all templates!</a>

## Template components

Templates use tags to do the following:
- Define which objects are being labeled (Object tags)
- Define how to label those objects (Control tags)
- Define how to lay out the interface (Visual tags). 

You can customize any template. [Use tags and the tag arguments](/tags) to customize or extend templates to fit a more specific data labeling use case.

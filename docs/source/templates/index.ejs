---
title: Template Gallery
type: templates
order: 001
meta_title: Gallery of Labeling Templates
meta_description: Gallery of templates available to perform data labeling and annotation tasks with Label Studio for your machine learning model and data science projects.
layout: templates
cards:
- title: Computer Vision
  categories:
  - object detection
  - semantic segmentation
  - image classification
  image: "/images/templates/semantic-segmentation-with-polygons.png"
  url: "/templates/gallery_cv.html"
- title: Dynamic Labels
  categories: object detection, semantic segmentation, image classification
  image: "/images/templates/inventory-tracking.png"
  url: "/templates/gallery_dynamic_labels.html"
- title: Natural Language Processing
  categories: named entity recognition, text classification, relation extraction
  image: "/images/templates/relation-extraction.png"
  url: "/templates/gallery_nlp.html"
- title: Audio/Speech Processing
  categories: automatic speech recognition, speaker segmentation, intent classification
  image: "/images/templates/speaker-segmentation.png"
  url: "/templates/gallery_asr.html"
- title: Conversational AI
  categories: natural language understanding, chatbot response generation, slot filling
  image: "/images/templates/response-generation.png"
  url: "/templates/gallery_conversational_ai.html"
- title: Ranking & Scoring
  categories: pairwise classification, document retrieval
  image: "/images/templates/pairwise-classification.png"
  url: "/templates/gallery_rns.html"
- title: Structured Data Parsing
  categories: freeform metadata, tabular data, pdf classification
  image: "/images/templates/freeform-metadata.png"
  url: "/templates/gallery_data_parsing.html"
- title: Time Series Analysis
  categories: activity recognition, forecasting, outliers and anomaly detection
  image: "/images/templates/activity-recognition.png"
  url: "/templates/gallery_timeseries.html"
- title: Videos
  categories: video classification, timeline segmentation
  image: "/images/templates/video-timeline-segmentation.png"
  url: "/templates/gallery_videos.html"
- title: LLM Fine-tuning
  categories: generative ai, llm
  image: "/images/templates/generative-pairwise-human-preference.png"
  url: "/templates/gallery_generative_ai.html"
- title: LLM Evaluations
  categories: generative ai, llm
  image: "/images/templates/generative-chatbot-assessment.png"
  url: "/templates/gallery_llm_evals.html"
---
---
title: Plugin Gallery
type: plugins
order: 001
meta_title: Plugin Gallery
meta_description: Plugins
layout: templates
tier: enterprise
cards:
- title: Bulk Labeling for Text Spans
  categories:
  - Automation
  - Text
  - NER
  image: "/images/plugins/bulk-labeling-thumb.png"
  url: "/plugins/bulk_labeling"

- title: Spam and Bot Detection
  categories:
  - Automation
  - Pause annotator
  - Behavioral checks
  image: "/images/plugins/pause-thumb.png"
  url: "/plugins/pause_annotator"

- title: Connect to LLM Backend
  categories:
  - Automation
  - LLM
  image: "/images/plugins/llm-backend-thumb.png"
  url: "/plugins/llm_backend"

- title: Markdown to HTML
  categories:
  - Visualization
  - Text
  image: "/images/plugins/markdown-thumb.png"
  url: "/plugins/markdown_to_html"

- title: Simple Content Moderation
  categories:
  - Validation
  - Transcription
  image: "/images/plugins/moderation-thumb.png"
  url: "/plugins/moderation"

- title: Data Visualization with Plotly
  categories:
  - Visualization
  - Plotly
  - Import
  image: "/images/plugins/plotly-thumb.png"
  url: "/plugins/plotly"

- title: Multi-Frame Video View
  categories:
  - Visualization
  - Video
  image: "/images/plugins/frame-offset-thumb.png"
  url: "/plugins/frame_offset"

- title: Text Span Overlap Validation
  categories:
  - Validation
  - Text
  - NER
  image: "/images/plugins/ner-overlap-thumb.png"
  url: "/plugins/span_overlap"

- title: Dynamic Image Swap
  categories:
  - Visualization
  - Labels
  - Images
  image: "/images/plugins/dynamic-images-thumb.png"
  url: "/plugins/images_per_label"

- title: Spellcheck
  categories:
  - Validation
  - Import
  - Text area
  image: "/images/plugins/spellcheck-thumb.png"
  url: "/plugins/spellcheck"

- title: Redact Annotator PII
  categories:
  - Visualization
  - CSS
  image: "/images/plugins/redact-thumb.png"
  url: "/plugins/redact_pii"

- title: Text Area Word Count
  categories:
  - Validation
  - Text area
  image: "/images/plugins/wordcount-thumb.png"
  url: "/plugins/wordcount"

- title: Validate JSON
  categories:
  - Validation
  - Text area
  image: "/images/plugins/json-validate-thumb.png"
  url: "/plugins/json_validation"
---

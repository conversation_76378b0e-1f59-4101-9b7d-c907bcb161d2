## How Label Studio saves results in annotations

Each annotation result consists of list or items, also called _regions_, stored under `annotation.result` field:
```json
{"result": [{"id": "123", ...}, {"id": "456", ...}], ...}
```
Regions can represent any annotation action - drawn bounding box, created relation, assigned category etc. Each `"id"` field formed as a string with the characters `A-Za-z0-9_-` and assigned according to the structure of annotation: each individual labeling entity gets its own ID, and same IDs are used to link different entities together - for example, relate them with `<Relation>`, or assign conditional annotation by using `"perRegion"` attribute.

The format is inferred by the following principles:
- there is always should be at least 1 object tag
- object tags define data types used (image, html, video, etc.)
- at least 1 control tag should be attached to object tag to create regions on that object
- every control tag produces 1 result for region
- results for the same region share the same id
- classification technically creates special empty classification region

When a prediction is used to create an annotation, the result IDs stay the same in the annotation field. This allows you to track the regions generated by your machine learning model and compare them directly to the human-created and reviewed annotations.

The `"value"` signifies the outcome of the labeling process, and its structure varies based on the labeling configuration. To understand the structure for specific tags, please refer to the [Control tags](/tags) documentation and explore the relevant tag.

## Examples


### per-regions
Example of result format when conditional `"perRegion"` structure is enforced:

```xml
<Image name="image" value="$image"/>
<RectangleLabels name="product" toName="image">
  <Label value="Some label" />
  ...
</RectangleLabels>
<TextArea name="name" toName="image" perRegion="true" />
```

1–2 results for one region: required labeling and optional per-region text result

```json
[{
  "id": "X_12fGk",
  "from_name": "product",
  "to_name": "image",
  "type": "rectanglelabels",
  // ...
  "value": {
    "labels": ["Some label"],
    // ...
  }
}, {
  "id": "X_12fGk",
  "from_name": "name",
  "to_name": "image",
  "type": "textarea",
  // ...
  "value": {
    "text": ["Roasted beans"],
    // ...
  }
}]
```

### optional labels

```xml
<Image name="image" value="$image"/>
<Rectangle name="product" toName="image" />
<Labels name="kind" toName="image">
  <Label value="Tea" />
  <Label value="Coffee" />
</Labels>
```

1–2 results for one region: required drawing tool and optional labeling attached to it

```json
[{
  "id": "X_12fGk",
  "from_name": "product",
  "to_name": "image",
  "type": "rectangle",
  // ...
  "value": {
    "x": 100,
    "y": 200,
    // ...
  }
}, {
  "id": "X_12fGk",
  "from_name": "kind",
  "to_name": "image",
  "type": "labels",
  // ...
  "value": {
    "labels": ["Tea"],
    // ...
  }
}]
```

### multi-labels

```xml
<Image name="image" value="$image"/>
<Rectangle name="product" toName="image" />
<Labels name="kind" toName="image">
  <Label value="Tea" />
  <Label value="Coffee" />
</Labels>
<Labels name="country" toName="image">
  <Label value="Sri-Lanka" />
  <Label value="Brazil" />
</Labels>
<Number name="price" toName="image" perRegion="true" required="true" />
```

1-4 results for one region: optional labels and required price + rectangle itself

```json
[{
  "id": "X_12fGk",
  "from_name": "product",
  "to_name": "image",
  "type": "rectangle",
  // ...
  "value": {
    // rectangle sizes
  }
}, {
  "id": "X_12fGk",
  "from_name": "kind",
  "to_name": "image",
  "type": "labels",
  // ...
  "value": {
    "labels": ["Coffee"],
    // rectangle sizes
  }
}, {
  "id": "X_12fGk",
  "from_name": "country",
  "to_name": "image",
  "type": "labels",
  // ...
  "value": {
    "labels": ["Brazil"],
    // rectangle sizes
  }
}, {
  "id": "X_12fGk",
  "from_name": "price",
  "to_name": "image",
  "type": "labels",
  // ...
  "value": {
    "number": 12.5,
    // rectangle sizes
  }
}]
```
### relations
You can draw relation arrow between two objects. For example, object detection configuration
```xml
<Image name="image" value="$image"/>
<RectangleLabels name="kind" toName="image">
  <Label value="Car" />
  <Label value="Airplaine" />
</RectangleLabels>
```
```
```json
[{
  "id": "oid67",
  "type": "rectanglelabels",
  // ...
},
{
  "id": "RQbW3Sj_Zr",
  "type": "rectanglelabels",
  // ...
},
{
  "type": "relation",
  "to_id": "RQbW3Sj_Zr",
  "from_id": "oid66",
  "direction": "right"
}]
```

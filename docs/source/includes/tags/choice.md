### Parameters

| Param | Type | Description |
| --- | --- | --- |
| value | <code>string</code> | Choice value |
| [selected] | <code>boolean</code> | Specify whether to preselect this choice on the labeling interface |
| [alias] | <code>string</code> | <PERSON><PERSON> for the choice. If used, the alias replaces the choice value in the annotation results. <PERSON><PERSON> does not display in the interface. |
| [style] | <code>style</code> | CSS style of the checkbox element |
| [hotkey] | <code>string</code> | Hotkey for the selection |
| [html] | <code>string</code> | Can be used to show enriched content, it has higher priority than `value`, however `value` will be used in the exported result (should be properly escaped) |
| [hint] | <code>string</code> | Hint for choice on hover |
| [color] | <code>string</code> | Color for Taxonomy item |


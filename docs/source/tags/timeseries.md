---
title: TimeSeries
type: tags
order: 309
meta_title: Time Series Tags for Time Series Data
meta_description: Customize Label Studio with the TimeSeries tag to annotate time series data for machine learning and data science projects.
---

The `TimeSeries` tag can be used to label time series data. Read more about Time Series Labeling on [the time series template page](../templates/time_series.html).

Note: The time axis in your data must be sorted, otherwise the TimeSeries tag does not work.
To use autogenerated indices as time axes, don't use the `timeColumn` parameter.

Use with the following data types: time series.

{% insertmd includes/tags/timeseries.md %}

## Channel

Channel tag can be used to label time series data

{% insertmd includes/tags/channel.md %}

### Example

Labeling configuration for time series data stored in a CSV loaded from a URL containing 3 columns: time, sensor1, and sensor2. The time column stores time as a number.

```html
<View>
  <TimeSeries name="device" value="$timeseries" valueType="url" timeColumn="time">
     <Channel column="sensor1" />
     <Channel column="sensor2" />
  </TimeSeries>
  <TimeSeriesLabels name="label" toName="device">
    <Label value="Run" background="#5b5"/>
    <Label value="Walk" background="#55f"/>
  </TimeSeriesLabels>
</View>
```
### Example

Labeling configuration for time series data stored in the task field `ts` in Label Studio JSON format. The time field is stored as a date in the `timeformat` field and formatted as a full date on the plot (by default).

```html
<View>
  <TimeSeries name="device" value="$ts" timeColumn="time" timeFormat="%m/%d/%Y %H:%M:%S">
     <Channel column="sensor1" />
     <Channel column="sensor2" />
  </TimeSeries>
</View>
```

---
hide_sidebar: true
---

## Label Studio Enterprise 2.4.0

<div class="onprem-highlight">Audio player enhancements, comments and notifications improvements, support for numpad hotkeys</div>

This section highlights the new features and enhancements, and bug fixes in Label Studio Enterprise 2.4.

### New features and enhancements 
- The [Comments and Notifications](/guide/comments_notifications.html) feature allows you to discuss task issues and other problems during labeling and reviewing processes.
- The new Audio Player feature provides a new configurable UI that improves the audio labeling efficiency and usability.
- Add `updated_at` field to comments API.
- Cancel old import jobs when a new one is created.
- Support for numpad hotkeys (letter/number keys and keyboard shortcuts) that allows you to label or classify the materials faster in productivity/metrics.
- Support for environment files from Vault ca-injector.


### Bug fixes
- Accept/Reject action does not update `updated_by` field of task.
- Fixed the `terms of service` link on sign up page.
- Fixed an issue where the notification about annotator's comment in draft was not sent to anyone.
- Show/hide toggle on the Outliner worked with all region types.
- Used Hotkey for label assignment to selected regions (`rectanglelabels`, `polygonlabels`,`keypoints`, `brushes`, `audio`, `timeseries`, `text`, `html`, `paragraph`, and so on).
- Added boundaries that limited the working area for video regions.
- Fixed an issue where CSV exports incorrectly serialized complex data types.
- Fixed the **Show labels inside the regions** option to work in the video template.
- Fixed import tasks data validation for nested fields with repeater.
- Fixed an issue when clicking the **Update** button in Label Stream lead the annotator to the next task.
- Comments were associated with the current draft even when the draft was in pending save state.
- Comment edited session state was displayed accurately in the updated form. (For example, `Updated 10 minutes ago`).
- Fixed an issue with the **Review** stream performance optimization.
- Fixed errors on task switching after notification link.
- Fixed an issue where the lack of network connection caused infinite loop of requests.
- Resolved an issue for read-only file system (FS).
- Fixed an issue where the Google Cloud Storage (GCS) persistent storage was broken.
- Fixed the issue with spam requests.
- Avoided the creation of `pg_trgm` in Postgres if it existed.
- Fixed review stream tasks ordering.
- Informed users about invalidated control tag names when there was a mismatch between labels in configuration and labels in data.
- Fixed CSV export when a few rows did not have the column values.
- Unfinished regions were easily detected using the sidebar or outliner so they were completed before the task was submitted.
- Changed color when a user changed the label.
- Removed `MEDIA_URL` from the uploaded file path.
- Improved the initialization of annotation configurations to reduce the memory used by complex cases such as the `Repeater` tag.
- Set the numbering of the first frame of the video timeline to be consistent.
- Fixed page crashes with enabled **Interactive View All** mode for review.
- Added a fix for read-only file structure (FS).
- GCS persistent storage was broken.
- Fixed the issue with data corruption using region manual editing through the **Details** panel.
- Fixed the issue with spam requests.
- Failed export in CSV: UnicodeEncodeError: `ASCII` codec failed to encode character.
- Fixed `update_tasks_counters` call with DM filters.
- Review statistics on the dashboard were correct when the feedback loop was enabled, and the reviewing options for the Reviewed counter on Dashboard were counted.
- Fixed dashboard-members API with "action=updated" for annotation review.
- Improved project duplication speed.
- Admin users were not able to access the project activity logs.
- Resolved a visual bug affecting overflowing text that passed the sidebar on the right.
- Fixed annotation disappears on undo.
- Fixed the `showSubmitButton="false"` to work correctly.
- Removed WASD (W, A, S, and D represent up, left, down, and right) shortcuts from DM navigation.
- Avoided the creation of `pg_trgm` in Postgres if it already existed.
- Added test coverage for new project template functionality.
- Users were able to pan images if an annotation history item was selected.
- Correctly undo actions over the fresh loaded draft with audio.
- Fixed label configuration validation for several `Choices` tags in a single view.
- Allowed clearing `DateTime` values.
- Exported consistency check.
- Fixed an issue where the Outliner grouping changed when the task annotations were changed.
- Fixed the issue with the inability to change the S3 session token once set.
- Filtered with the specific annotator worked very slowly.
- Added validation for project name length in duplicate dialog.
- Disabled task counter in **Label** or **Review** stream.
- Downloaded storage empty path.
- Fixed the broken **Review** stream on the second task using Audio v3.
- SSO failed to work with capitalized emails. Use this environment variable to enable autofix: `ALLOW_FIX_LOWERCASE_USER=true`.
- Removed duplicated PDF template.
- Supported copying regions in the Outliner.
- Fixed an issue with undoing closed polygons by Hotkey.
- Time Series truncate signal and triangle marks disappeared.
- SCIM was broken and always returned a logout page.
- Filtering failed to work for Annotation results.
- Returned `400` bad requests on incorrect XML.
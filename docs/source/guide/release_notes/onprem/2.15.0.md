---
hide_sidebar: true
---

## Label Studio Enterprise 2.15.0

<div class="onprem-highlight">Reviewer workflow updates, drag-and-drop tab reordering, usability enhancements for the <b>Show region labels</b> option, and upgrading to Django 4.2 </div>

*Sep 03, 2024*

Helm Chart version: 1.6.3

### Enhancements

- The reviewer workflow has changed to be more user-friendly and intuitive. As part of this enhancement, you will see the following changes:
    - If you are a Reviewer, Manager, Admin, or Owner and you click a task in the Data Manager, you will now see the reviewer actions (**Accept, Reject, Fix+Accept**) instead of the **Update** action. Going forward, only the user who created the annotation will see the **Update** action.
    - The **Explore All Reviews** option is no longer available. This is because the same basic functionality is now available by simply selecting tasks in the Data Manager.
    - When you click **Review All Tasks**, by default you will now be shown tasks in the same order in which the annotator completed their tasks. Previously, you were shown tasks in reverse order from completion.

   For more information, see [Improvements to HumanSignal Reviewer Workflow](https://humansignal.com/blog/improvements-to-humansignal-reviewer-workflow/). 

* You can now reorder tabs in the Data Manager by dragging and dropping them.

    ![Gif of reordering tabs](/images/releases/2-15-drag-and-drop.gif)

* When viewing regions with the **Show region labels** option enabled, the region label will now include the same index identifier that you see in the regions list. 

    Before:

    ![Screenshot of label regions before](/images/releases/2-15-label-after.png)

    After:

    ![Screenshot of label regions after](/images/releases/2-15-label-before.png)

* When you make changes to the labeling configuration and attempt to navigate away before leaving, you will now see a warning message prompting you to save your changes. 

    ![Screenshot of warning message](/images/releases/2-15-unsaved-changes.png)

* When using the Annotator Performance Reports, you can now select whether you want to aggregate data by Creation Date or Updated Date. 

    ![Screenshot of warning message](/images/releases/2-15-performance-report.png)

* There is a new Performance Score column on the project Members page:

    The Performance Score column reflects the overall performance of annotators. This score takes into account all review actions, including annotations that were initially rejected and later accepted. The calculation is as follows:
    - Each annotation review action (accept, reject, fix+accept) contributes to the score.
    - The score is calculated by summing the scores of all review actions and dividing by the total number of review actions.
    For example:
    - If an annotation is rejected twice and then accepted once, the Performance Score would be (0 + 0 + 1) / 3 = 33%.
    - If an annotation is rejected once and then fixed+accepted with a score of 42%, the Performance Score would be (0 + 0.42) / 2 = 21%.

    This is different from the Review Score, which only reflects the current accepted/rejected state of annotations.

    ![Screenshot of performance score](/images/releases/2-15-performance-score.png)

* There is now a link to the HumanSignal support portal available from the menu. You can find FAQ and troubleshooting information here, as well as a link to open a support ticket.  

    ![Screenshot of warning message](/images/releases/2-15-support-link.png)

### Breaking changes

- This release includes an upgrade to Django 4.2. As a result, PostgreSQL 11 is no longer support. Before upgrading, you must migrate to PostresSQL 12 or later.

### Security

Upgraded NLTK to 3.9.1 to address [CVE-2024-39705](https://nvd.nist.gov/vuln/detail/CVE-2024-39705). 

### Feature flag updates

The following feature flags have been removed:

- `fflag_feat_front_prod_e_111_annotator_workflow_control_short`
- `fflag_fix_front_lsdv_4673_rect3point_relative_310523_short`
- `ff_back_1614_rejected_queue_17022022_short`

### Bug fixes

- Fixed an issue where the View all annotations action was not working when the `<Text>` tag value was empty.

- Fixed an issue where Annotators were not seeing comments when assigned to the Reviewer role on the project-level.

- Fixed an issue with agreement scores for Annotators being inaccurately calculated on the Members page within projects. This issue would appear when a Reviewer would reject an annotation and then later accept it.

- Fixed an issue with displaying large integer numbers in the Data Manager.

- Fixed an issue with breaking Text/HyperText content that contains emoji when regions are added.

- Fixed an issue with tooltips that was causing errors in the transition effects.

- Fixed an issue where workspaces could be deleted through the API even if they still contained projects.

- Fixed an issue where users were seeing an error when visiting the Annotator Performance Report page without first selecting a user. Instead, users should be redirected away from the page until a user is selected.

- Fixed usability issues seen in the Members table once the member user list grows large enough.

- Fixed an issue where users were seeing a runtime error when loading the dashboard in situations in which the project’s labeling configuration did not include labels.

- Fixed an issue where tracing was breaking presigned URL requests.

- Fixed an issue where the Project dashboard was returning an error.

- Fixed an issue where, when attempting to select multiple bounding boxes by pressing Command (or Ctrl), a new bounding box would be created instead.

- Fixed an issue where the project summary was not being included when duplicating a project.

- Fixed an issue where images were improperly resizing after loading due to how the original dimensions were set.

---
hide_sidebar: true
---

## Label Studio Enterprise 2.6.0

<div class="onprem-highlight">New <code>snap</code> parameter for KeyPoint, KeyPointLabels, Polygon, and PolygonLabels tags, improved usability when reviewing video in Outliner </div>

*Oct 24, 2023*

### Enhancements

- The [Keypoint](https://labelstud.io/tags/keypoint), [KeyPointLabels](https://docs.humansignal.com/tags/keypointlabels), [Polygon](https://docs.humansignal.com/tags/polygon), and [PolygonLabels](https://docs.humansignal.com/tags/polygonlabels) tags all support a new `snap` parameter for use in Image Segmentation labeling. When `snap="pixel"` is enabled, the (x, y) coordinates of each point are rounded to the pixel size. This enhancement will help ensure precise and uniform coordinates within images. For polygons, points are snapped to the pixel edge. For example, given a polygon point with the coordinates (0.25, 0.25), your resultant coordinates would snap to the edge of the pixel at (0,0). For keypoints, points are snapped to the pixel center.  For example, given a keypoint with the coordinates (0.25, 0.25), your resultant coordinates would snap to the center of the pixel at (0.5,0.5).
- When reviewing video in Outliner, if you click on a marked region, the video playback will automatically jump to the selected region. Previously, users had to manually scroll to the starting point. This change will make it easier to quickly view and edit video segments.

### Breaking changes

- This release adds a deployment-wide `VERIFY_SSL_CERTS` setting that defaults to `true`. Customers who are loading data (e.g. uploading tasks) from https URLs without verifiable SSL certificates must set `VERIFY_SSL_CERTS` to `false` in their environment variables before deploying Label Studio 2.6.0+.
- Add `WINDOWS_SQLITE_BINARY_HOST_PREFIX` environment variable to support hosting SQLite binaries on a server other than [sqlite.org](http://sqlite.org/), for Windows deployments running Python 3.8 only.

### Bug fixes

- Fixed an issue where pressing `Ctrl +` or `Ctrl -` (Windows) or `Cmd +` or `Cmd -` (Mac) was not zooming in/out on images as expected.
- Fixed an issue where the number of drafts displayed in the project summary was not updated when drafts were submitted as annotations.
- Fixed an issue where, in certain contexts, labeling instructions were displayed in raw HTML.
- Fixed an issue that occurred after project creation in which users were prevented from moving forward if changes were made in the template preview.
- Fixed an issue where AWS Lambdas custom agreement functions only worked for `Choice` tags.
- Fixed an issue where custom metrics for agreements weren’t working for annotations that had drafts.
- Fixed an issue where users were unable to navigate through their task list after saving a draft.
- Fixed an issue where, when viewing the Projects page, reviewers and managers were not seeing the total number of annotations in a project.
- Fixed an issue where blank drafts were being created when annotations were submitted.
- Fixed several issues with how annotation drafts were handled. Users will now see a more descriptive error message when trying to update a labeling configuration that is still being used in annotations or in drafts. Also, when using the Data Manager to delete all annotations, this will also delete all task drafts and annotation drafts.
- Fixed an issue where when duplicating a project that had cloud storage, the storage sync status and number of synced items was also copied when they should be reset.
- Fixed an issue of missing annotators from members dashboard when a performance optimization is enabled.
- Fixed an issue where `<Choice selected="true">` was not working within the `Taxonomy` tag.
- Fixed an issue that would cause a blank draft to be created when using hot-keyed annotation submit.


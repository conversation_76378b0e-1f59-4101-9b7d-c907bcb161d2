---
hide_sidebar: true
---

## Label Studio Enterprise 2.2.9

<div class="onprem-highlight">Dynamic Labels template, Comments column in the Data Manager, decommissioning MinIO</div>

This section highlights the breaking changes, new features and enhancements, and bug fixes in Label Studio Enterprise 2.2.9. 

### Breaking changes
Label Studio Enterprise 2.2.9 includes the following breaking change:

- This release decommissions MinIO as a service.

### New features and enhancements 
Label Studio Enterprise 2.2.9 introduces the following new features and enhancements.

#### Backend
- This release provides proxy support for content-range HTTP responses.
- Add API for project duplication.

#### Frontend
- This release introduces the ability to select model version on model cards from the machine learning page >> **Settings**.
- Now, you can show the comments column in the Data Manager to reviewers.
 
#### Templates
- This release introduces [Dynamic Labels templates](https://labelstud.io/templates/gallery_dynamic_labels.html#main). You can now show labels dynamically as a task input, either as a prediction from the model or from a database lookup. With a broader set of options, dynamic labeling saves time and increases the consistency of labeling the objects. 

### Bug fixes
Label Studio 2.2.9 includes the following bug fixes:

#### Backend
- Optimized dashboard-members API performance for reviews.
- Enabled Query optimization for Uniform Sampling in the Labeling Stream.
- Fixed runtime error when duration was not extracted on `ASR_MANIFEST` export.
- Fixed permissions for a manager role.
- Fixed `annotation_id` was provided as float value in CSV export.
- Replaced `inner_id` index with multicolumn.
- Recalculate stats when control weights were updated.
- Fixed empty agreement for taxonomy with extra labels.
- Fixed `is_labeled` calculation after task import.

#### Frontend 

- Fixed the regions that disappeared from UI in **Annotation History**. 
- Improved the **Annotation History** name/timestamp spacing.
- Fixed audio crashes in **View All** mode.
- Pan does not finish the polygon.
- Fixed nested choices for the indeterminate state.
- Fixed an issue to get text after granularity was applied in **Annotation Result**.
- Zoomed image region out of bounds.
- Viewed all audio responsive.
- Fixed an issue where all parts of audio in the **View All** mode were equally responsive to browser size changes.
- Resynchronized annotations that failed to synchronize in **Target Storage**.
- Supported lengthy unbroken taxonomy line breaks.
- Retained the size for key points. 
- Display the correct number of member icons on project cards.
- Fixed rendering issue on video regions.
- Fixed the loading issue for `Paragraph` data on **QuickView**.
- Allowed edit action on Time Series results imported as read-only.
- Fixed Annotation History when exiting **View All**. 
- Added X-axis zoom threshold.
- Added guard with an error message for non-incremental, non-sequential datasets in Time Series.
- Disabled the delete **all region** button when an annotation is read-only.
- Fixed blind Server-side Request Forgery (SSRF) on add model and import.
- Deselected the ImageView bounding box before creating another. 
- Fixed data in Search Engine Results Page (SERP) ranking in-app template. 
- Unfinished polygon zoom fix. 
- Fixed flickering issue when regions were created with the Bounding box.
- Video regions were edited when Annotation History was selected.
- Added background as a new parameter to text shortcuts.
- Fixed the form layout and allowed the model version selector when the ML backend was edited.
- Text and Header tags work with integers now.
- Fixed synchronization speed between video and audio.
- Fixed an issue with prop `whenChoiceValue`.
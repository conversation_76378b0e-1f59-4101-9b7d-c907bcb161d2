---
hide_sidebar: true
---

## Label Studio Enterprise 2.17.0

<div class="onprem-highlight">New reviewer workflow options, streamlined layout for the Projects page, and other UI improvements </div>

*Oct 22, 2024*

Helm Chart version: 1.6.3

### New features

#### Allow Reviewers to either requeue or reject annotations

There is a new option on the **Review** page of the project settings: **Allow reviewer to choose: Requeue or Remove**.

When enabled, reviewers will see two reject options for an annotation: **Remove** (reject and remove the annotation from the queue) and **Requeue** (reject and then requeue the annotation).

This is useful for situations in which some annotations are incorrect or incomplete, but are acceptable with some fixes. In those cases, it may be more useful to send the task back to the annotator. In other cases, where the annotation is far from correct, it is better to remove it entirely. 

For more information, see [Project settings - Review](project_settings_lse#Review).

![Screenshot of reject setting options](/images/releases/2_17_0_review_settings.png)

![Screenshot of reject actions](/images/releases/2_17_0_review_actions.png)

### Enhancements

- The Projects page header has been updated with a more compact design:
    - The search bar, projects drop-down menu, and create actions have all been consolidated onto one line.
    - The **Use Template** action has been moved and is now available as a drop-down option within the **Create Project** button.
    - The **Use Template** action has also been renamed **Create from saved template**.

   ![Screenshot of project page](/images/releases/2_17_0_project_page.png) 
- For better clarity, the Sandbox workspace has been renamed **Personal Sandbox**.
- You will now see a progress bar when performing searches against the activity log to indicate that the search is still processing.
- When creating regions that have start and end times (such as when annotating sections of an audio track), you will now see the duration of your selection under the **Info** tab.
    ![Screenshot of duration info](/images/releases/2_17_0_duration.png)

### Bug fixes

- Fixed an issue in which the agreement score was not updating after a reviewer completed the **Fix+Accept** action.
- Fixed an issue that caused the time portion of the DateTimePicker in the project Dashboard to not display correctly.
- Fixed an issue which caused the Label Config UI preview to display stale information. 
- Fixed an issue in which users were not able to use Quick View to load tasks that included an external taxonomy.
- Fixed an issue where the instructions modal was not appearing for reviewers even though **Show before reviewing** was enabled in the project settings.
- Fixed an issue in which draft lead time could incorrectly inflate the lead time calculation for an annotation.
- Fixed an issue in which Data Manager drop-down menus were inaccessible in smaller viewports.
- Fixed a small UI issue seen when displaying drop-down menus with multiple nested selection options.
- Fixed an issue where images were improperly spaced in the Annotator Agreement Matrix.
- Fixed an issue in which **Label All Tasks** would not respect filters that had been applied in the Data Manager.


---
hide_sidebar: true
---

## Label Studio Enterprise 2.11.1

<div class="onprem-highlight">Improved machine learning & LLM integrations, action to remove duplicated tasks, Redis ACL support, and other enhancements and bug fixes</div>

*Apr 30, 2024*

Helm Chart version: 1.4.4

### New features 

#### Improved machine learning & LLM integrations

This release streamlines the way ML models and LLMs are connected to Label Studio with a focus on security and simplified user experience. 

Using the powerful ML backend integration, users can add models and customize automated workflows for:

- **Pre-labeling**: Letting ML/AI models predict labels autonomously, which can then be reviewed by human annotators.
- **Interactive labeling**: An automated process that applies initial labels to data, which are then refined through manual review, enhancing the efficiency and accuracy of data annotation for machine learning models.
- **Model evaluation and fine-tuning**: Used in models like the Segment Anything Model (SAM), involves a human-in-the-loop approach where the model provides initial predictions or annotations, and a human annotator interacts directly with these predictions to correct or refine them.

Label Studio Enterprise users can add custom models, or reference a [new examples library](https://github.com/HumanSignal/label-studio-ml-backend/tree/master) to connect popular models, including Segment Anything, OpenAI, Grounding DINO, select Hugging Face models, Tesseract, and more.

Updates to the ML backend integration in this release include:

- New support for basic auth, which means users can now connect to hosted ML backends that require a password.
- The ability to specify additional parameters to pass to the model, which means users can now easily connect to Azure-hosted OpenAI in addition to OpenAI and popular ML models.
- An improved UI and simplified project settings, including:
    - A new **Live Predictions** section under the **Annotation** page of the project settings makes it easier select whether you want to use predictions or a model in your annotation workflow.
    - A new **Predictions** page, where you can easily upload and manage predictions.
    - Removed obsolete settings that are no longer compatible (for example, auto-updating version).
    - Fixed various usability issues related to the annotation experience with a model connected.

For more information, see [Integrate Label Studio into your machine learning pipeline](ml).

![Screenshot of the new ML backend screens](/images/releases/2-11-0-ml-backend2.png)

![Screenshot of the new ML backend screens](/images/releases/2-11-0-ml-backend.png)


#### Remove Duplicated Tasks action

There is a new Remove Duplicated Tasks action available from the Data Manager. This action had previously only been available as an experimental feature.  

When you use this action, annotations from duplicated tasks are consolidated into one task (the first task found among the duplicated tasks). 

![Screenshot of the Remove Duplicated Tasks action](/images/releases/2-11-1-remove-duplicate.png)


### Enhancements

- Redis ACLs are now supported.

- Improved usability for project dashboards by changing how time filtering works to more accurately reflect annotation progress.

- Multiple domains per organization are now supported for SSO login.


### Security

- Enhanced validation to ensure that projects created through the API have workspaces within the active organization.

- Fixed an issue in which insufficient permission checks were performed for certain API calls.


### Bug fixes

- Fixed an issue where, during the review process, the mouse cursor would disappear against light gray backgrounds.

- Fixed an issue where some tasks were not displayed when using the review explorer.

- Fixed an issue with missing text in results for specific OS and browsers.

- Fixed an issue which could produce incorrect unresolved comment counts when updating the comment `is_resolved` state

- Fixed an issue in which the Dashboards page returned an error if the project had no tasks.

- Fixed an issue with project dashboard label distribution charts in which some labels could be improperly grouped.

- Fixed an issue where blank charts would be created in the project dashboards. 

- Fixed an issue in which project dashboard charts would return errors and appear empty.

- Fixed an issue is which the `TextArea` input area would resize while you were editing it.

- Fixed an issue with loading data manager while there are trailing special characters in keys of imported data.

- Fixed an issue where validation would fail for `<Table>` tags when the imported data used list format.

- Fixed an issue in which exporting data would use excessive memory.

- Fixed an issue in which some icons were not displayed correctly.


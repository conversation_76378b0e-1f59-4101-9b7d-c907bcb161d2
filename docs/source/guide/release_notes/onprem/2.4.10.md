---
hide_sidebar: true
---

## Label Studio Enterprise 2.4.10

<div class="onprem-highlight">Contextual scrolling for audio and video, search field on the Projects page, autocomplete prompt when editing the labeling configuration</div>

*Sep 13, 2023*


### New features

- Contextual scrolling allows you to sync your text transcripts with their corresponding audio or video. When enabled, the text transcript automatically scrolls to the new listening point as the media plays. This is now the default mode for the Conversation Analysis template.
    
    For more information, see the [Contextual Scrolling template documentation](https://docs.humansignal.com/templates/contextual_scrolling).

    ![Screenshot of an audio file with contextual scrolling](/images/releases/2-4-10-scrolling.png)
    
- There is a new search field on the Projects page. You can use this field to search project titles. It can also be used with the project filters. For more information, see [Search projects](https://docs.humansignal.com/guide/manage_projects#Search-projects). 

    ![Screenshot of the search field on the Projects page](/images/releases/2-4-10-search.png)

- When working with the labeling configuration code editor, you will now see an autocomplete prompt that lists and defines possible tags and parameters. 

    ![Screenshot of the autocomplete feature in action](/images/releases/2-4-10-autocomplete.png)
    
- You can now use the API to filter [projects by title](https://app.heartex.com/docs/api/#tag/Projects/operation/api_projects_list). 
- There is a new **Drafts** column available in the Data Manager. You can also filter and sort by this column.
    
    ![Screenshot of the Drafts column in the Data Manager](/images/releases/2-4-10-drafts.png)

### Enhancements

- When using an LLM-based ML backend, the `<TextArea>` tag now supports chat mode. You can send a prompt and receive a response to populate your TextArea inputs. 
- New tooltips throughout the UI provide guidance on advanced features and configurations to improve labeling efficiency and quality. 
- Label distribution now shows the number of labels instead of the percentage. 
- Deactivated user pages now include contact information. 
- For organizations using SSO, you can now disable regular logins for your users. 
- The project migration script has been improved to ensure that annotation history, annotation reviews, and drafts are migrated appropriately. 
- You should see improved performance in multiple API calls due to optimization work related to data handling and loading.  
- Improved exact frames matching, including adjusting BBox impact weight and improved basic matching for more accurate consensus scores. 
- Several design improvements to project dashboards:
    - Progress bars are now clearer. 
    - Task Pending Review and Annotated Tasks indicators now have labels for better clarity. 

### Security

This release addresses a vulnerability regarding how SECRET_KEY is set.

- The SECRET_KEY is now configurable through an environment variable. **Users are strongly encouraged to set SECRET_KEY to a random secret of their choosing.** A fallback SECRET_KEY is specified by default, but will be removed in a future version.
- Older versions also included a vulnerability in which the secret key could be leaked via identity provider callbacks. This release patches that vulnerability.
- **Helm Chart update**: Version 1.2.0 is now available. This version includes automatic generation of a random SECRET_KEY, which also populates a Kubernetes secret. No manual setting required. [See the full changelog here](https://github.com/HumanSignal/charts/blob/master/heartex/label-studio/CHANGELOG.md). 

### Bug fixes

- Fixed an issue where all users in an organization were listed in the drop-down filter for annotators, rather than just users within that project. 
- Fixed an issue where when saving a labeling configuration, users were redirected to the Data Manager. 
- Fixed an issue where some users were unable to log in via LDAP due to TLS cypher settings. 
- Fixed an issue where FileProxy was blocking local IPs. 
- Fixed an issue where labels were missing from the Outliner UI when labels from different tags were applied to the same text span.
- Fixed an issue that was preventing users from changing labels. 
- Fixed an issue affecting split channel audio. 
- Fixed an issue where the show/hide icon was not appearing when working in regions that were grouped by tools. 
- Fixed an issue where the number of completed tasks listed on the All Projects page displayed an incorrect value in situations where the project is duplicated. 
- Fixed an issue where the Project page was making unnecessary API calls. 
- Fixed an issue where `is_labeled` was being miscalculated. 
- Fixed an issue where filtering by annotation results in the Data Manager was causing errors. 
- Fixed an issue with RichText tags when using non-Chromium browsers. 
- Fixed an issue that occurred when users selected keypoints and polygons within the same annotation. 
- Fixed an issue where users were able to import unsupported file types.
- Fixed an issue where there wasn’t sufficient spacing between the Author filter and the first paragraph. 
- Fixed a double encoding issue with file-proxy URLs. 
- Fixed an issue where the Move and Pan icons were missing in the Create Project preview. 
- Fixed an issue where the workspace overflow menus were visible even when the user was not hovering over the workspace name. 
- Fixed an issue where DB deadlocks were occurring due to lengthy transactions. 
- Fixed an issue where pushing a SCIM group would automatically create a workspace named after that group, which should not happen in cases where a role to group mapping already exists. 
- Fixed an issue where the date picker on project dashboards was being incorrectly calculated. 
- Fixed an issue where a large empty space was appearing at the bottom of the Workspaces page. 
- Fixed an issue where users were unable to edit label configurations for Natural Language Processing groups. 
- Fixed an issue with column naming collisions in certain API responses. 
- Fixed an issue where, when using an ML backend, the model version was not displaying in the Data Manager despite being explicitly set. 
- Fixed an issue where pressing Escape would not close the Create Project modal. 
- Fixed an issue where annotators were able to archive workspaces. This should be restricted to owners, managers, and admins. 
- Fixed issues to ensure more robust and uniform SSRF defenses.
- Fixed an issue where organization names were improperly appearing in error logs. 
- Fixed numerous issues related to Text and HyperText that affected performance and usability. 
- Fixed several issues to improve region tree responsiveness. 
- Fixed an issue where clicking an annotator’s profile picture would throw an error due to `displayName` being undefined or when user references were stale. 
- Fixed an issue where roles were not being checked for task assignments. 
- Fixed an issue where annotators were able to access tasks to which they were not assigned. 
- Fixed an issue causing deadlocks on task import when running parallel jobs. 

---
hide_sidebar: true
---
  

## Label Studio Enterprise 2.4.4
<div class="onprem-highlight"><code>skipDuplicates</code> parameter for TextArea, audio v3 Web Audio alternative decoder option, S3 custom endpoint for persistent storage </div>

### New features
- New parameter skipDuplicates of TextArea allows to keep submissions unique.

### Enhancements
- Audio v3 webaudio alternative decoder option
- S3 custom endpoint support for persistent storage
- Table tag ordering items alphabetically

### Bug fixes
- Fix DM columns visual problems
- Fix column sizes on datamanager
- Hiding an audio region allows selection of regions below.
- Fix Intersection over 1d timeseries spans agreement calculation for Time series

- Fixes playback micro-stutters for Video Timeline Segmentation.
- Add error handlers like it is for AudioV1
- Don't let ghost regions be created
- AttributeError: 'GCSExportStorage' object has no attribute 'links_count'
- Disable file proxy for cloud using FILE_PROXY_ENABLED environment variable
- Audio playback and playhead remain in sync.

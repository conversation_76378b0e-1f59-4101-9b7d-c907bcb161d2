---
hide_sidebar: true
---

## Label Studio Enterprise 2.5.0

<div class="onprem-highlight">Project-level roles for SAML/SCIM, ability to pause annotation sessions</div>

*Sep 28, 2023*

### New features

- Project-level roles are now available and configurable through SAML or SCIM.  By mapping user groups to project roles, you'll have more granular access controls for your data and you can simplify permissions management for internal teams and annotators. For more information, see [the HumanSignal blog](https://humansignal.com/blog/manage-and-restrict-access-to-your-data-at-a-more-granular-level-with-project-based-roles/) and [the related documentation](https://docs.humansignal.com/guide/scim_setup#Set-up-group-mapping). 

    ![Screenshot of SCIM mapping for project roles](/images/releases/2-5-0-project-roles.png)

- Users can now pause their annotation session so that they can take a break from annotating without it affecting their lead time scores. They can do this by selecting **Submit and Exit** or **Update and Exit**. Their work is automatically saved as a draft. For more information, see [Exit a labeling flow](https://docs.humansignal.com/guide/labeling#Exiting-a-labeling-flow). 

    ![Screenshot of the Save and Exit option when labeling](/images/releases/2-5-0-submit-and-exit.png)

### Enhancements

- Improved performance of prediction counter calculations, leading to faster response times for project pages and stats calculations. 
- Improved dashboard load times, correcting an issue that caused projects with numerous different labels to timeout. 

### Bug fixes

- Fixed an issue where attempting to access AWS target storage resulted in a 403 error. This was fixed by allowing prefix-level bucket access. 
- Fixed an issue where an XSS vulnerability meant that a user’s cookies could be exposed when viewing our API documentation. As a result, users must now be logged in when visiting the Label Studio Enterprise [API docs page](https://app.heartex.com/docs/api/) or the [Swagger page](https://app.heartex.com/swagger). 
- Fixed an issue where users’ changes to the Labeling Interface Settings were not being saved. 
- Fixed a performance issue when using mouse clicks to interact with OCR regions that have large numbers (>50) of bounding boxes. 
- Fixed an issue where Admin users who were assigned the Reviewer role in a project (using the SCIM project-role mapping) were seeing their own annotations in the review stream for the project. 
- Fixed an issue where organization-level roles rather than project-level roles were being reflected in project cards. 
- Fixed an issue to ensure that project-level role mapping is removed when the associated SCIM and SAML mappings are removed. 
- Fixed an issue with login page indexing that was preventing users from being added to projects. 
- Fixed an issue where the predictions counter was not correct when using project-level role mapping. 
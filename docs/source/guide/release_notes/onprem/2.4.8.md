---
hide_sidebar: true
---

## Label Studio Enterprise 2.4.8

<div class="onprem-highlight">Enhanced UI for panels and tabs, new Ranker tag, new Outliner filter, async imports</div>

*Jun 16, 2023*

### New features
- Panels now have tabs that can be moved between panels by dragging or can become panels themselves. Panels can also now be stacked, resized, collapsed, and reordered
- Support for Ranker tag validation and sample data from the API
- New outliner filter for improved efficiency and navigation
- Annotation tabs to better split out and manage tasks
- Target storage successfully validates unexisting buckets
- Make imports asynchronously to ensure stability on the server requests

### Enhancements
- Add Generative AI templates to Label Studio
- Remove export and conversion files from storage when related export snapshot is deleted
- Optimize requests made for pre-signing cloud storage urls
- Add labeling config templates for Ranker tag
- Annotation selection is now handled using a carousel instead of a dropdown
- Enhance cloud storages with progress tracking and status information for improved monitoring and debugging
- Add Backend API for Label Distribution chart
- Confidence score to be displayed all the time on regions
- Forward and back buttons on all views, different roles have different interface buttons available
- New defaults for panels and tabs
- New List+Ranker tags that work in tandem to display and rearrange list of items
- Show agreement column to reviewers in DM
- DataManager default width should be equal to the main menu width in Quick View
- Upgrade to NodeJS LTS
-  parameter for . You can now use  parameter for  tag.  It allows the display of additional information for the each item when you hover over it. This works for both '<Choices/>' and '<Taxonomy/>' tags as a containers
- Change the quick view column button
- Improvement to the user general settings modal to align with the new labelling UI changes
- Annotation Tab Button
- Conditional annotation with visibleWhen choice-selected works with Taxonomy
- Annotation instructions are a modal instead of a top bar
- Change LSF linter to target changed files only

### Bug fixes
- Fix CORS errors when accessing previously valid urls
- Fix issue with missed Hide all regions button
- Check Connection for Azure storage doesn't actually check connection
- Add validation for min/max params in DateTime
- Project duplication saves updated description
- Fix duplicated workspaces when SAML workspace mapping is used
- Fix issue with possibility of missing dynamic children of Taxonomy
- Update wheel, django and sub-dependencies to address security vulnerabilities
- Labels in drafts now also using for config validation
- Duplication of tasks at first sync
- Fix OOM during target storage export
- Error from_name in draft saving
- Fix statistics calculation for paragraph labels with  spans
- Fix an issue with missed timestamps while zooming Time Series with huge data
- Fix empty stream with show_overlap_first enabled
- Improve project list performance by requesting less data in all requests
- Fix an issue when Brush tool completely crashes UI if it's defined before the image it's attached to
- Fix expanding/ collapsing Quick View side-panel to prevent reversion of annotations to the top of the undo stack
- Reset button now successfully resets the time field
- Fix side-panel spacing in view all mode
- Remove bottom bar in view all
- Copy formatting respected in initial instructions modal
- Time is now consistent between date time sessions
- Outliner manual group sort order arrow
- Project duplication correctly copies over the annotation setting to require leaving a comment on skip
- Fix an issue with using local file upload with cloud storage urls causing errors
- Project duplication correctly copies over the quality setting for annotation agreement metric name
- Activities of a Member is being Tracked across multiple LSE Organization
- Dashboard start and end have time by default
- Sort annotations by creation time
- New regions are handled in the filters
- Tooltip is missing when hovering over Confidence score value
- Fixed an issue with interpolating a video region rotation prop
- Center justify text on last step of sign up
- PreNotification is_processed_for_* index
- Fix audio and video sync issues with alternative audio player webaudio
- Fixed tab switch on breakpoints, prevent dragging to collapsed groups, allow line breaks in info modal, prevent panel revert on screen size change
- Fix documentation for Ranker
- Navigate between tabs in the side panels doesn't reset the postpone button state
- Prediction results cannot be displayed immediately
- Update the MIG feature flag to match the naming convention
- Improve interactions and feedback on date and time fields of the date time picker
- Fix a script incompatibility causing API docs to not load
- Fix any unhandled errors with pre-signed proxy urls
- Fix image vulnerability: CVE-2023-31047
- New feature of the parameter skipDuplicates of TextArea allows to keep submissions unique while editing existing results
- Fix runtime error whenever a user deletes a source annotation and proceeds to submit/update the duplicate
- Validate doesn't work for export storage
- Add hover state to panel header, improved buttons for collapse and expand
- Always display correct author of draft when user check others' comments
- Annotation tab annotator name line height not per specs
- Fix inconsistency in the display of the region item lock and hide controls
- Keep the created at timestamp of an associated annotation to its saved draft
- Fix toggle selected region visibility using hotkey (Alt + H)
- Fix icons in TopBar
- Always use time even if not provided on kpi api calls
-  removes the ability for patch requests to update user email
- 'PDF Classification' classification template is displayed twice
- A fix for the date-time picker calendar to prevent the selection of all available dates when the user clicks and drags
- Handle AWS CORS implementation edge cases for images
- Support predictions for Ranker
- Migrate the rest of the system to Yarn
- Insufficient Protection Against Malicious Software
- Fix XSS in wrong task data
- Shorten ordered by title in outliner to allow for filters

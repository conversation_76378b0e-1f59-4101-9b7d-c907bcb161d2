---
hide_sidebar: true
---

## Label Studio Enterprise 2.4.2

<div class="onprem-highlight">Label placement change for regions, YOLO support for PolygonLabels</div>

### New features
- Labels are not displayed inside the regions
- Add YOLO support for PolygonLabels in export options

### Enhancements
- Backend: Update invite url to send user to new password page
- Postpone button is disabled if there is no new comments on label steam
- Rendering performance improvements for large-duration audio
- Variable frame rate for videos using frameRate="$fps" doesn't work
- Display correct docs links in LSE/LS

### Bug fixes
- User can resize bbox as he wants if ImageView is zoomed.
- Fixed issue with keeping zoom position on resize of working area
- Fix appearance of all the connected labels to region in Details view
- Text and HyperText elements can be added without value and name
- Datetime annotation produces empty payload for the value: {}
- Page breaks on completing audio Annotation (when using large audio files in a ParagraphLabels project)
- In data manager UI, the moving and resize are mixed, resize is not usable
- Postpone mode reverts task back and forces user to create 10 annotations in a row
- Quick View breaks when navigating between annotations
- Video zoom doesn't follow the cursor pointer location
- Task locks missed in postponed / skipped queue
- Taxonomy titles clash (reappearing bug)
- Administrator can't be removed from project members
- Four digits added at the end of image file_name in outputted COCO jsons  
- Optimize memory and speed during exports
- maxUsages for KeyPointLabels and RectangleLabels doesn't work
- Fixed an issue with backspace hotkey in Audio
- Import jobs are submitted twice (or more) times
- Details section layout is broken when long named labels are used 
- Second click on label unselects it and creates 'No label'
- Fix missing tasks in Data Manager upon upload
- Region is selected when user drag the region
- Saving model version on Machine Learning settings doesn't work

---
hide_sidebar: true
---

## Label Studio Enterprise 2.24.0

<div class="onprem-highlight">Dark mode, new home page, annotator evaluation settings, plugins library, and multiple usability enhancements</div>

*May 20, 2025*

Helm Chart version: 1.9.10

### New features

#### Dark mode

Label Studio can now be used in dark mode.

Click your avatar in the upper right to find the toggle for dark mode.

- **Auto** - Use your system settings to determine light or dark mode.
- **Light** - Use light mode.
- **Dark** - Use dark mode.

!!! note
    Dark mode is not available for environments that use white labeling. 

![Screenshot of dark mode](/images/releases/2-24-dark-mode.png)

![Animated gif of dark mode](/images/releases/2-24-darkmode.gif)

![Screenshot of dark mode](/images/releases/2-24-dark-mode2.png)


#### New Label Studio Home page

When you open Label Studio, you will see a new Home page. Here you can find links to your most recent projects, shortcuts to common actions, and links to frequently used resources

![Screenshot of home page](/images/releases/2-24-home.png)

#### Annotator Evaluation settings

There is a new Annotator Evaluation section under **Settings > Quality**.

When there are ground truth annotations within the project, an annotator will be paused if their ground truth agreement falls below a certain threshold.

For more information, see [**Annotator Evaluation**](https://docs.humansignal.com/guide/project_settings_lse#annotator-eval).

<img src="/images/releases/2-24-evaluation.png" style="max-width:600px; margin: 0 auto" alt="Screenshot of evaluation settings">

#### New Insert Plugins menu and Testing interface

There are a number of new features and changes related to plugins:

- There is a new **Insert Plugins** menu available. From here you can insert a pre-built plugin that you can customize as necessary.
- When you add a plugin, you will see a new **Testing** panel below the plugin editing field. You can use this to verify what events are triggered, manually trigger events, and modify the sample data as necessary.
- To accompany the new **Insert Plugins** menu, there is [a new Plugins gallery](https://docs.humansignal.com/plugins/) in the documentation that discusses each option and has information on creating your own custom plugs.
- There is also a new setting that allows you to restrict access to the Plugins tab to Administrator users. By default, it is also available to Managers. This can be set through the Django admin panel. 

![Screenshot of plugins](/images/releases/2-24-plugin-menu.png)

![Screenshot of plugins](/images/releases/2-24-plugin-test.png)

### Enhancements

#### Agreement score popover in Data Manager

Click any agreement score to view pairwise agreement scores with others.

![Screenshot of agreement popover](/images/releases/2-24-agreement-popover.png)

#### Adjustable text spans

You can now click and drag to adjust text span regions.

![Animated gif of text span drag and drop](/images/releases/2-24-text-drag.gif)

#### Dynamic brush sizes

The cursor now adjusts dynamically to brush size to allow for more precision in segmentation tasks.

<video style="max-width: 800px;" class="gif-border" autoplay loop muted>
  <source src="/images/releases/brush-size.mp4">
</video>
 
#### Support for BrushLabels export to COCO format

You can now export polygons created using the BrushLabels tag to COCO format.

#### Create support tickets through AI Assistant

If you have AI Assistant enabled and ask multiple questions without coming to a resolution, it will offer to create a support ticket on your behalf:

<img src="/images/releases/2-24-ai-ticket.png" style="max-width:600px; margin: 0 auto" alt="Screenshot of AI assistant">

#### Clear chat history in AI Assistant

You can now clear your chat history to start a new chat.

<img src="/images/releases/2-24-ai-new.png" style="max-width:600px; margin: 0 auto" alt="Screenshot of AI assistant">

#### Export underlying data from the Annotator Performance dashboard

There is a new **Export Underlying Data** action for the Annotations chart.

![Screenshot of agreement popover](/images/releases/2-24-export.png)

#### Annotators can now view their own performance dashboard metrics

When logging in, annotators will now see a link to the Annotator performance dashboard, where they can see their own performance metrics. 

<img src="/images/releases/2-24-annotator-dashboard.png" style="max-width:600px; margin: 0 auto" alt="Screenshot of annotator dashboard button">

#### Improved drop-down selectors

When there are a large number of options in a drop-down menu, you can now search through the list of available options.

<img src="/images/releases/2-24-drop-down.png" style="max-width:400px; margin: 0 auto" alt="Screenshot of annotator dashboard button">

#### Label Studio Converter CLI

When you install the Label Studio SDK, you can now use the `label-studio-converter` command from your terminal.

#### Miscellaneous

- Performance enhancements around how membership API requests are made.

- Added a new API call to rotate JWT tokens: [POST api/token/rotate](https://app.heartex.com/api/token/rotate/)


### Security

- Addressed a CSP issue by removing `unsafe-eval` usage.

- Added a rule that password resets will be limited to 5 per hour.

- Upgraded Babel to address vulnerabilities.

- Improved security on CSV exports.

- Removed an unused endpoint.

- By default, CORS is permissive. However, you can now set an environment variable to ensure it is in strict mode. Set **one** of the following:
    - `CORS_ALLOWED_ORIGINS`  
     A comma-separated list of Origin header values the Label Studio server will receive, e.g. `https://example.org,https://example.net`

    - `CORS_ALLOWED_ORIGIN_REGEXES`  
      Same as above, except using regex. 
    - `CORS_ALLOW_ALL_ORIGINS`  
        Set to `false` or `0` to reject all Origin header values (that is, allow no cross-origin requests). By default this is set to `true`. 



### Bug fixes

- Fixed an issue where interacting with the Manage Members modal would sometimes throw an error.

- Fixed an issue where white-labeled Label Studios instances were showing the incorrect logo.

- Fixed an issue where the `Filter` tag did not work with `Choices` tags.

- Fixed an issue where annotators were seeing a misleading message that a project was not ready, even though the project was completed.

- Fixed a server worker error related to regular expressions.

- Fixed several small visual issues with the AI assistant.

- Fixed an issue that was causing multiple annotators to be assigned to tasks beyond the overlap settings.

- Fixed an issue where “Deleted User” repeatedly appeared in filter drop-down menus.

- Fixed an issue where clicking on the timeline region in the region list did not move the slider to the correct position.

- Fixed an issue where a "Script running successfully" message continuously appeared for users who had plugins enabled.

- Fixed an issue where the drop-down menu to select a user role was overflowing past the page edge. 
  
- Fixed an issue where the `visibleWhen` parameter was not working when used with a taxonomy.

- Fixed an issue where there were some UI inconsistencies that would occur during certain page navigations.

- Fixed an issue where certain drop-down menus were inaccessible at different zoom levels.

- Fixed an issue where the Data Manager would go blank when filtering by the predicted model version.

- Fixed an issue where, if a 500 error was returned when syncing storage, the user would not see the error.

- Fixed an issue where forward and rewind hotkeys for audio were not working.

- Fixed an issue where the bars in the Tasks graph on the project dashboard were not accurately grouped by `reviewed_at` or `completed_at`.


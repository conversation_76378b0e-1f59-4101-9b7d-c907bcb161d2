---
hide_sidebar: true
---

## Label Studio Enterprise 2.14.0

<div class="onprem-highlight">Refreshed interface for the HumanSignal platform and bug fixes</div>

*Jul 30, 2024*

Helm Chart version: 1.6.0

### New features

#### Refreshed Label Studio interface

This release includes a new UI with updated colors and fonts, giving it a sleek new look while maintaining the same intuitive navigation you're familiar with. All Label Studio tools, features, and settings are still in the same place, ensuring a smooth transition.

![Screenshot of new UI](/images/releases/2-14-UI-login.png)

![Screenshot of new UI](/images/releases/2-14-UI-new.png)

![Screenshot of new UI](/images/releases/2-14-UI-projects.png)

![Screenshot of new UI](/images/releases/2-14-UI-settings.png)

![Screenshot of new UI](/images/releases/2-14-UI-settings2.png)

### Bug fixes

- Fixed an issue where users were unable to export in Pascal VOC XML format when applying bounding boxes to images.

- Fixed an issue where after an annotation had been fixed and accepted, the update action was not recorded when exporting the annotation history to JSON.

- Fixed a sizing issue affecting the icons for workspace actions.

- Fixed an issue where the docs link icon was not properly formatted.

- Fixed an issue affecting SAML users caused by changes to the HumanSignal app domain.

- Fixed an issue in which sometimes the Submit button would be displayed when it should be the Update button.

- Fixed an issue where Annotators where able to resolve comments made by Reviewers, when this action should not be available to them.

- Fixed an issue where usernames in the Annotator Performance Report were not displayed correctly if the user had a long email address.

- Fixed an issue where annotation history was not working correctly if `created_by` was null.


---
hide_sidebar: true
---

## Label Studio Enterprise 2.19.0

<div class="onprem-highlight">Paginated multi-image labeling and a new Task Reservation setting </div>

*Dec 17, 2024*

Helm Chart version: 1.7.3

### New features

#### Paginated multi-image labeling

Paginated multi-image labeling allows you to label an album of images within a single task. When enabled, a page navigation tool is available within the labeling interface. 

While you can use paginated multi-image labeling with any series of related images, it can also be especially useful for for document annotation. 

For example, you can pre-process a PDF to convert it into image files, and then use the pagination toolbar to navigate the PDF. For more information, see our [Multi-Page Document Annotation template](/templates/multi-page-document-annotation).

To enable this feature, use the `valueList` parameter on the [`<Image> tag`](/tags/image).

![Screenshot of multi-page annotation](/images/releases/2-19-mig.png)

#### Set task reservation time

There is a new project setting under **Annotation > Task Reservation**.

You can use this setting to determine how many minutes a task can be reserved by a user. You can also use it for projects that have become stalled due to too many reserved tasks. For more information, see [Project settings - Task Reservation](https://docs.humansignal.com/guide/project_settings_lse#lock-tasks).

By default, the task reservation time is set to one day (1440 minutes). This setting is only available when task distribution is set to **Auto**.

![Screenshot of multi-page annotation](/images/releases/2-19-reservation.png)

### Enhancements

- When using the **Send Test Request** action for a connected ML backend model, you will now see more descriptive error messages.

- The placeholder text within labeling configuration previews is now more descriptive of what should appear, rather than providing example text strings.

- Improved the inter-annotator agreement API so that it is more performant and can better handle a high number of annotators.

- Improved Annotator Performance Report page load time.

- TextArea elements have been updated to reflect the look and feel of other labeling elements.

### Bug fixes

- Fixed an issue where SSO/SAML users were not being redirected back to the originally requested URL.

- Fixed an issue where a timeout on the inter-annotator agreement API would cause missing data in the Annotator Summary table on the Members page.

- Fixed an issue where the default date format used when exporting to CSV was incompatible with Google Sheets.

- Fixed an issue where commas in comment text breaking were causing errors when exporting to CSV from the Annotator Performance report.

- Fixed an issue that was causing 404 errors in the Activity Log.

- Fixed an issue where users were unable to deselect tools from the toolbar by clicking them a second time.

- Fixed an issue where users were presented with Reviewer actions even if the annotation was still in Draft state.

- Fixed an issue with the Source Storage editor in which some fields were overlapping in the user interface.

- Fixed an issue with the Data Manager filters when the columns are different from those in the labeling config and when `$undefined$` is present in the task data.

- Fixed an issue where filter options in the Data Manager would disappear on hover.

- Fixed an issue which caused XML comments to incorrectly be considered in the label config validation.

- Fixed an issue causing an error when marking a comment as read.

- Fixed an issue where an error message would appear when selecting or unselecting the **Get the latest news & tips from Heidi** option on the Account Settings page.

- Fixed an issue where annotators were seeing a tooltip message stating that the project was not ready yet, even though the project had already been completed.

- Fixed an issue where project-level roles did not affect role upgrades performed at the Organization level.


### Feature flag updates

The following feature flags have been removed:

- `fflag_feat_front_dev_2984_dm_draggable_columns_short`
- `fflag-feat-front-dev-2982-label-weights-settings`
- `ff_back_2070_inner_id_12052022_short`



---
hide_sidebar: true
---

## Label Studio Enterprise 2.13.0

<div class="onprem-highlight">Annotator performance dashboards, potential breaking change for Google Cloud Storage users, feature flag changes</div>

*Jul 02, 2024*

Helm Chart version: 1.4.9

### New features

#### Annotator performance dashboards - Beta

With this release, you will see a new **Performance report** action available from the Organization page. 

![Screenshot of Performance Report button](/images/project/user_report.png)

Clicking this takes you to a dashboard focused on annotator performance. This new dashboard is designed to help you manage your team, figure out resource allocation, and save the expense of building custom internal tracking tools.

![Screenshot of the annotator dashboard](/images/releases/2-13-annotator-report.png)

The annotator performance dashboard provides insight into each user’s annotation activity over a period of time. You can see how much time they spent annotating, how many annotations they submitted, and their average time spent per annotation. You can further refine this dashboard by workspace and project. 

For more information, see [Annotator performance dashboard](dashboard_annotator) and [Annotator Dashboard Helps Optimize Team Performance](https://humansignal.com/blog/new-annotator-dashboard-helps-optimize-team-performance/).

### Enhancements

Improved performance on the Projects list page due to improvement on the API level.


### Breaking changes

- Fixed an issue with Google Cloud Storage when the connection has the **Use pre-signed URLs** option disabled. In these situations, Google was sending pre-signed URLs with the format `https://storage.googleapis.com` rather than sending BLOBs.

    With this fix, Google Cloud Storage will begin returning BLOBs/base64 encoded data when **Use pre-signed URLs** is off. This means that Label Studio will start reading data from Google Cloud Storage buckets, which can result in large amounts of data being sent to your Label Studio instance - potentially affecting performance.

### Feature flag changes

- As part of an ongoing effort to streamline our codebase, we have identified a number of seldom-used feature flags. We have marked these feature flags as `stale`, meaning they can no longer be enabled by users. For a full list of all affected feature flags, see https://github.com/HumanSignal/label-studio/pull/5971

### Bug fixes

- Fixed an issue with Redis being unable to connect to SSL.
- Fixed an issue where Redis storage connections were causing errors due to a missing field in the storage form (Storage Title).
- Fixed an issue where connected ML backends were unable to return more than one prediction per task.
- Fixed an issue where annotators were not being prompted to leave a comment when skipping a task, even though the project settings required them to do so.
- Fixed an issue where sometimes actual usernames were being replaced by a generic “Admin” username in the annotation history.


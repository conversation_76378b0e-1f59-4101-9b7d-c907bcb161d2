---
hide_sidebar: true
---

## Label Studio Enterprise 2.16.0

<div class="onprem-highlight">Video frame classification, user activation through SCIM, and bug fixes </div>

*Sep 24, 2024*

Helm Chart version: 1.6.3


### New features

#### Video frame classification

You can now apply labels to video frames. Previously, we only supported per-video classification. This new feature allows you to apply labels at a per-frame level. 

You can implement this feature using a new tag: [`<TimelineLabels>`](/tags/timelinelabels). 

For more information, see [New! Video Frame Classification](https://humansignal.com/blog/video-frame-classification/).

![Video frame classification](/images/releases/2_16_video.png)


### Enhancements

- You can now deactivate and activate user accounts via SCIM. Note that for this to work, `manual_role_management` must be set to `False`.  

- Changed the default behavior of the project Dashboard so that it no longer defaults to the “include time” option in the calendar.

### Bug fixes

- Fixed an issue where user limits were not being enforced when users were added via LDAP.

- Fixed a regression issue with BigInteger support in the Data Manager.

- Fixed a styling issue in which buttons were overlapping in the review workflow.

- Fixed an issue with the Activity Logs page where some options would become unavailable.

- Fixed an issue where setting a task agreement threshold incorrectly affected counts in the project Dashboard.

- Fixed an issue in which notifications were fetched too frequently.

- Fixed an issue with a missing `db` field for Redis storage, which caused issues for users adding Redis target storage.

- Fixed an issue in which project-level roles could not be reverted once set.


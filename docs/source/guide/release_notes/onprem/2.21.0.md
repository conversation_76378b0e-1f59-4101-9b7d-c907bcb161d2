---
hide_sidebar: true
---

## Label Studio Enterprise 2.21.0

<div class="onprem-highlight">Bulk labeling, enable AI features, and multiple labeling and Data Manager enhancements</div>

*Feb 25, 2025*

Helm Chart version: 1.9.5


### New features

#### Bulk labeling and image preview 

There is a new **Bulk label** action available from the Data Manager. You can use this to quickly label tasks multiple tasks at once.

This feature also includes enhancements to the Grid View in the Data Manager. Now when viewing images, you can zoom in/out, scroll, and pan.

For more information, see the [Bulk labeling documentation](labeling#Bulk-labeling) and [Bulk Labeling: How to Classify in Batches](https://humansignal.com/blog/bulk-labeling-how-to-classify-in-batches/).

![Screenshot of bulk label action](/images/releases/2-21-bulk-label.png)

![Screenshot of Grid View preview](/images/releases/2-21-data-preview.png)

#### Enable AI features

There is a new toggle on the **Billing & Usage** page (only available to users in the Owner role). You can use this to enable [AI features](ask_ai) throughout Label Studio. 

![Screenshot of enable AI](/images/releases/2-21-ai-enable.png)

![Screenshot of AI banner](/images/releases/2-21-ai.png)


### Enhancements

#### Updated Billing & Usage page

The **Billing & Usage** page (only accessible to users in the Owner role) has several new options:

- **Early Adopter** - Opt in to new features before they're generally available.
- **Enable AI Features** - Enables AI helper tools within Label Studio. See [AI features](ask_ai).
- **White labeling** - Contact sales about enabling white labeling.
- **Custom Scripts** - Contact sales to enable custom scripts. See [Custom scripts for projects](scripts).

#### Updated template images

The thumbnail images for the pre-built templates have been redesigned.

![Screenshot of template library](/images/releases/2-21-templates.png)

#### Clearer description of annotation count

The annotation counter at the bottom of the Data Manager has been updated to read "Submitted Annotations." It previously read "Annotations," which could cause confusion.

![Screenshot of counter](/images/releases/2-21-count.png)

#### Display the prediction score in the labeling interface

When you have annotations generated by predictions (pre-annotations), you will now see the prediction score (also known as the "confidence score") under the model name in the labeling interface tabs.

![Screenshot of scores](/images/releases/2-21-score.png)

#### Region number in Relations panel

The **Relations** panel now displays the number identifier for the region when viewing relations between regions.

![Screenshot of region IDs](/images/releases/2-21-regions.png)

#### Export images with YOLO, YOLO_OBB, and COCO

Previously, when exporting data in YOLO, YOLO_OBB, or COCO format, the images themselves were not included in the export. 

To improve this, we have introduced three new choices to the export options:

* **YOLO_WITH_IMAGES**

* **YOLO_OBB_WITH_IMAGES**

* **COCO_WITH_IMAGES**

#### Set ground truths by user

There is a new action from the Data Manager that allows you to mark the annotations submitted by a specific user as ground truth annotations.  

![Screenshot of ground truths](/images/releases/2-21-gt.png)

![Screenshot of ground truths](/images/releases/2-21-gt2.png)

#### Control login redirects

There is a new `LOGIN_PAGE_URL` variable will redirect the login page to the URL specified in the variable. This is useful for organizations with that have white labeling enabled and/or multiple internal groups that have different IdP provider logins (or no IdP provider login).   

#### Performance improvements

Various performance improvements around Members page load time, annotation creation, and memory usage for Image tags. 

### Security

- Updated Iodash to address security vulnerabilities.

- Ensured that file paths remain hidden when import operations fail.

### Bug fixes

- Fixed an issue where the Annotator Performance drop-down was not filtering the results as expected.

- Fixed an issue where users were unable to select and move bounding box regions after adding brush regions.

- Fixed an issue where seeking within a video would display duplicate frames in the the Video tag.

- Fixed an issue when managers could review skipped tasks in Quick View.

- Fixed an issue where project to groups mapping was not working correctly for SAML.

- Fixed an issue that caused would sometimes cause project creation to fail when pasting code into the code editor.

- Fixed an issue where Sentry would still attempt to load assets even if disabled.

### Feature flag updates

The following feature flags have been marked stale or deleted, meaning they can no longer be turned on or off by users:

`fflag_feat_front_optic_767_annotator_project_multiselect_short`  
`fflag_fix_back_leap_612_explore_review_09042024_short`  
`fflag_fix_optic_214_extra_blank_dashboard_charts_short`  
`fflag_fix_optic_391_tasks_outside_low_agreement_project_counts_short`  
`fflag_fix_all_leap_877_annotator_membership_api_03042024_short`  
`fflag_feat_all_optic_520_annotator_report_short`  
`feat_all_optic_71_dashboard_multiple_labeling_group_support_v1_01092023_short`  
`fflag_feat_front_prod_281_project_list_search_19072023_short`  
`fflag_feat_all_lsdv_e_295_project_level_roles_via_saml_scim_ldap_short`  
`ff_back_2884_comments_notifications_02092022_short`  
`ff_back_DEV_1711_review_queue_140222_short`  
`ff_front_dev_1480_created_on_in_review_180122_short`  
`fflag_fix_front_leap_32_zoom_perf_190923_short`  
`fflag_feat_front_lsdv_5452_taxonomy_labeling_110823_short`  
`fflag_fix_front_dev_3793_relative_coords_short`  
`ff_front_dev_2715_audio_3_280722_short`  
`fflag_feat_front_optic_1351_use_new_projects_counts_api_short`  
`fflag_feature_all_optic_1421_cold_start_v2`  
`fflag_fix_back_optic_1407_optimize_tasks_api_pagination_counts`  
`fflag_fix_optic_1259_lse_projects_read_apis_use_replica_short`  
`fflag_feat_all_optic_1181_membership_performance`  
`fflag_feat_optic_1025_zendesk_widget_integration`  
`fflag_feat_all_optic_991_dashboard_v2_short`  
`fflag_feat_optic_378_limit_projects_per_page_to_ten_short`  
`fflag_feat_optic_67_drag_and_drop_charts`  
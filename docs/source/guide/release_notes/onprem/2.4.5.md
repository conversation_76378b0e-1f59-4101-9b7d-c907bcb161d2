---
hide_sidebar: true
---

## Label Studio Enterprise 2.4.5

<div class="onprem-highlight">Performance optimization for projects API, storage link resolver for nested task data fields</div>

*Apr 10, 2023*

### Enhancements
- Performance optimization for api/projects with task number
- Storage link resolver in nested task data fields

### Bug fixes
- Fixed a problem where filtering by empty or not empty in annotation results and prediction results would not work
- Start, end and offset of paragraph selection should not include empty or newline 
- Fixed an issue with regrouping regions after annotations switching
- Opening video in tasks should not trigger a CORS issue in HEAD response
- Can't patch tasks when task data has a taxonomy null-values
- Fix error on duplicating Project with external storage when it wasn't synced
- Improved filetype playable detection for video sources 
- Proper unhandled exceptions processing in *_from_request functions. Activity log middleware fix for project id. Warning: Some of 500 errors about validation are 400 errors now.
- CORS errors on valid audio files occur sometimes when accessed in succession
- Fix Video Rectangles to display while drawing
- Fixed import several tasks from one csv file


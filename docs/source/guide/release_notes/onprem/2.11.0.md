---
hide_sidebar: true
---

## Label Studio Enterprise 2.11.0

<div class="onprem-highlight">New ability to configure project-level roles, setting to hide the Cloud Storage page from Manager roles, other enhancements and bug fixes </div>

*Apr 09, 2024*

Helm Chart version: 1.4.2

### New features

#### Project-level roles

You can now assign the Reviewer or Annotator role to users on a per-project basis. This will help simplify team management and allow more flexibility for project managers within your organization to meet the needs of each project.  

This feature was previously only available to organizations who had enabled SCIM for their user management. Now, project-level roles can be assigned as part of the project membership settings, and is applicable to any user who has the Annotator or Reviewer role at the organization level. For example, a user can be an Annotator at the organization level, but have the Reviewer role for a specific project. Similarly, a user with the Reviewer role at the organization level can be assigned as an Annotator to different projects as needed. 

For more information, see [Project-level roles](project_settings_lse#Project-level-roles).

![Screenshot of the project-level roles](/images/releases/2-11-0-project-level-roles.png)

### Enhancements

- Added support for `X-Api-Key: <token>` as an alternative to `Authentication: Token <token>`. This will make it easier to use API keys when integrating with cloud-based services. 
- Small UI improvement to make it clearer which project members are included in the project by default.
- There is a new setting in place that can control access to the Cloud Storage page for users with the Manager role. If you would like to enable this setting, set the `HIDE_STORAGE_SETTINGS_FOR_MANAGER` environment variable to `True`.
- Several enhancements for organizations with SCIM enabled, including:
    - More detailed error messages.
    - Allow workspace and role mappings to support multiple SCIM groups.

### Bug fixes

- Fixed an issue where Google Cloud Logging was not working due to a missing dependency.
- Fixed an issue where `/api/version` was not reporting all updates.
- Fixed an issue where, after revoking an invite to users who are already in projects, the project failed to load.



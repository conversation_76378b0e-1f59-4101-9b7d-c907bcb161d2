---
hide_sidebar: true
---

## Label Studio Enterprise 2.4.9-2

<div class="onprem-highlight">New workspace actions, per-image classifications</div>

*July 26, 2023*

### New features
- Workspaces now have a drop down option to allow you to edit, delete, and archive them. This enables you to hide workspaces from view while still allowing access to those workspaces as-needed
- Per-image classifications are now available. You can use the perItem parameter of classification control tags in Multi-Image Segmentation cases to attach classification to separate images in the set. For now it is supported by `DateTime`, `Number`, `Choices`, `Taxonomy`, `Rating` and `Textarea`

### Enhancements
- Fixed medium vulnerabilities for Vanta
- Print more descriptive debug messages for SAML configuration errors on an error page
- Consistent feature flags for all products
- New disabled state on date picker buttons

### Bug fixes
- Fixed issue with 3-point rectangle too that it didn't work with relative coords
- After selecting several tasks in data manager, reviewers get "URL too long" error
- Persist collapse state of side panels
- <PERSON><PERSON><PERSON> in rqworkers uses error level for logging always
- Fixed issue where the user is able to move a region even when it's locked
- When "Must leave a comment" is selected, the comments tab will come to focus
- Fixed relation hotkeys so that they work with and without relation action buttons
- Fixed the inability to modify regions which were initially beneath another
- Fixed sorting by signed numeric values
- Current draft version is NOT always saved after clicking the 'Postpone' button
- Fixed issue with selecting hidden regions by selection tool
- Fixed issue with unavailable regions inside selection area
- Load Predictions + Dynamic Labels properly, unknown labels are not removed from results now
- Disallow users from adding users from other organizations to their project
- Fixes issue where ReviewStream task navigation buttons were missing
- Fixed data import with SDK and async import
- Inconsistent behavior when adding New project to the archived workspace
- Tooltip is missing when expanding / collapsing "Archived Workspaces" section

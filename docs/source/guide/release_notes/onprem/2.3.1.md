---
hide_sidebar: true
---

## Label Studio Enterprise 2.3.1

<div class="onprem-highlight">New helm chart, multiple usability enhancements, including new Data Manager columns and the ability to duplicate projects</div>

This section highlights the breaking changes, new features and enhancements, and bug fixes in Label Studio Enterprise 2.3.1.

### New helm chart

A common chart for LS and LSE has been released and is available as of LSE version 2.3.x. The chart can be accessed at the following repository: https://github.com/HumanSignal/charts/tree/master/heartex/label-studio.

#### Migration Process

The migration process can be performed without any downtime. The steps required to carry out the migration are documented in the migration guide, available at: https://github.com/HumanSignal/charts/blob/master/heartex/label-studio/FAQs.md#label-studio-enterprise-upgrade-from-decommissioned-label-studio-enterprise-helm-chart.

### Breaking changes
Label Studio Enterprise 2.3.1 includes the following breaking change:

-  This release moves Nginx to a sidecar container.
-  After the announcement in LSE 2.2.9, <PERSON><PERSON> was still supported. Now, this release finally decommissions MinIO as a service.

### New features and enhancements 
Label Studio Enterprise 2.3.1 introduces the following new features and enhancements.

- Allows annotators and reviewers to filter the view of transcriptions by author name.
- Improve project list performance by hydrating counters in a second request.
- Project duplication interface that allows users to copy projects with settings, tasks, and annotations.
- Introduce the project pinning interface that allows users to pin projects for better visibility of commonly referenced projects.
- Duplication of tasks and annotations in project duplication API.
- Navigate taxonomy with arrow keys, Up/Down to navigate, Right to open subtree, Space to select item; also important fixes for multi-lines and interactions.
- Add user notification about Storage Persistence availability.
- Implement new columns for the commentary system: **comment count**, **unresolved comment count**, **comment authors**, **last comment date**.
- Introduce size presets to zoom an image to fit within the viewport or to have it at its natural size (up to available space in viewport). With this release, you can now set the image to be positioned vertically (top, center, bottom) and horizontally (left, center, right).
- Introduce comments system for communication between Annotators and Reviewers. Allows a Reviewer to mark comments as resolved. Comments feature also introduces new columns in the Data Manager to be able to filter tasks by comments inside.
- Add workspace grouping for Annotators, displaying the Workspaces where they are the members.
- Display drop-down with model versions for each machine learning backend.
- Change in rotate anchor that is no longer out of the Bbox and now are close to all resize anchors.
- Add Label weights settings section in **Settings** >> **Quality**.
- Add date and action filters for dashboard data.
- Support `PosixGroupType` for LDAP.
- Add Paragraphs to substring_matching example.
- Update the invite people modal to include invite by email.
- Add **Resend** and **Revoke** invitation buttons to **Organization** page when a user is selected.
- Update the organization role drop-down visual to show an indicator for inactive users.
- Update welcome emails on signup verification and invites.
- Add the ability to sustain the collapse state of the label across tasks and maintain consistency in the order of the label groups.
- Cleanup lambda custom-metrics if it's not required.
- Add cron jobs to verify trial expiration.
- Export command for open source using console.
- Block the entire screen by a non-closable modal window only when the trial ends.
- Add option to synchronize audio with paragraphs allowing playback of chunk position.
- Support a custom column order with draggable columns.
- Support notifications links in Label Stream and Review Stream.
- Add links to annotations in notifications.
- Enable manual mode for assigning Reviewers to tasks.
- Introduce new attributes for the `<Audio/>` tag: `defaultZoom`, `defaultSpeed` and `defaultVolume`.
- Add simpler hotkeys to jump between frames in the Video Segmentation scenario.
- Add video metric with intersection for interpolated frames.
- New comment behavior for Submit/Update/Skip/Accept/Reject buttons.
- Support Django GCS with signed URLs without service account token creator permission.
- Add the video type support and video preview to the Data Manager.
- Add a list of supported video formats.
- Allow negative timeseries data and additional customization options to visualization.
- Introduce new Video settings in the Labeling Interface to allow changing the hop size.
- Add Multi-page document annotations template with `<Repeater>` example among the template gallery.
- Inactive users now show `Never` in the **Last Activity** column of the organization table instead of the date they were invited.
- Improve revoke invite UX so it's consistent when used from the selected user section and the revoke invite button in the dropdown in User list.
- Annotator's Data Manager filters persist between page navigation.
- Run `api/workspaces?user_email=xxx` API call to return the list of workspaces.
- The region navigation now works in scrolling (list) mode.

### Bug fixes
Label Studio 2.3.1 includes the following bug fixes:

- Fixed an issue where unfinished polygons should save as draft and remain in open state if left unclosed.
- Retained history on initial load of annotation review stream.
- Fixed workspace filter for project list API.
- Displayed source filename for tasks from storage in a separate column.
- Fixed "Tasks per page" field that should be in sync with the number of tasks displayed.
- Fixed an issue where **Quick View** failed to open when the user attempted to copy-paste its URL to another tab.
- Deselected image region Bbox on short click.
- Fixed the behavior of the drop-down menu that wasn't grouping when the organization wasn't activated  .
- Added a change in rotate anchor that was no longer out of the Bbox and currently close to all resize anchors.
- Prevented users from being able to edit fields that are not meant to be editable.
- Multiple rendered labels in regions.
- Fixed an issue where the relationship delete button wasn't working as intended.
- Ensured `review_settings` was included in the initial request.
- New `DateTime` tag for date, date time, or year that can be conditionally rendered.
- Allowed annotators and reviewers to filter view of transcriptions by author name.
- Added ability to delete points with an alt click..
- Allowed users to pin/unpin projects to more easily filter and find projects of interest.
- Fixed `PyJWT` vulnerability.
- `get_local_path` doesn't work for local-files in ML backends and converters.
- Hold to continuously draw image view shapes should work with DEV-1442 enabled.
- Skipped tasks are placed in the beginning of the label stream, however they should go at the end
- Added agreement calculation for `Datetime` tag.
- Speed up **Members** page in case of big annotations.
- Resolved an error where the 3 point Bbox would remain usable after removing rectangles from the labeling configuration.
- Fixed an issue where the imported annotation was marked as read-only, but allowed users to make changes anyway.
- Fixed UX and behavior when expanding/collapsing the panels and unsnapping/snapping to the sides.
- Displayed drop-down with model versions for each machine learning backend.
- Updated Django to 3.2.14.
- Fixed broken default page number for non-admin accounts on Projects page.
- User could not edit `VideoRectangle` when it was locked.
- Fixed an issue when a user can resize a panel in such a way that it obscures all the underlying content.
- Fixed clashed text entries for multi-value TextArea.
- Fixed an issue when selection is still active after hiding an Image region.
- Fixed an issue when selection is broken after showing previously hidden selected region.
- Added columns for comment management in the Data Manager: **Comment count**, **unresolved comment count**, **comment authors**, and **last comment date**.
- Prevented polygon being duplicated when finishing drawing.
- Implemented new columns for the commentary system: comment count, unresolved comment count, comment authors, last comment date.
- Locked polygons don't show the editable points any longer.
- Removed validation for new data fields in label config.
- Fixed the issue when grouping by empty label caused the app to crash completely.
- Fixed an issue when Audio regions were displaced due to zoom/viewport size.
- Fixed an issue when panels can fall out of the viewport if the viewport's size changed.
- Recalculated overlap when changing overlap to 1 and changing enforce overlap.
- Fixed user's inability to hide regions within the NER scenario.
- Added a unique constraint for workspace members.
- Fixed UX issue with an almost invisible text area in a region list when the region is selected.
- Fixed app crash with Author Filter for Paragraphs enabled.
- Fixed an issue when the text captured by a region was not displayed in the **Details** panel.
- Resolved an issue affecting the tooltip of the flyout menu tooltips on small screens.
- Disabled the delete button when previewing the historic item.
- Showed indeterminate loading when project duplication is in progress.
- Unfinished polygon region was not auto-completed when the user moved it.
- Annotation region locking should not persist.
- Changed environment variable for enforcing local URL check for ML backend.
- Can't upload data without annotation history
- Fixed an issue when the selected **Annotation History** item was not rendered on the canvas.
- Increased external storage sync job timeout.
- Label weight was not reset after Labels change.
- Project list had project duplicates.
- Fixed an issue where a missing empty body was generated for 204 responses.
- Broken "All Projects" pagination.
- Fixed an issue with paragraph regions that were not selectable within the new Outliner.
- Fixed configuration validation for Repeater tag.
- Implemented lazyload on image to improve loading performance.
- Improved polygon point removal during drawing: you can use usual undo hotkeys (ctrl/cmd+z) to remove the point you just set or redo it if you want (ctrl/cmd+shift+z).
- Fixed an issue with displaying Annotation History in LSC.
- **Details** panel was automatically updating on lock action.
- Disabled error for label configuration validation with <!DOCTYPE> tag.
- Showed list of new users created using API correctly.
- Added the Talk to an expert modal.
- Added a minor correction to invite/revoke button text.
- Cleaned up logging, excluding potential security concerns related to sensitive data exposure.
- Resolved an issue that added an entry to the annotation history when zoom was changed.
- Project list card requests used wrong Feature Flags.
- Fixed an issue when the text captured by a region was not displayed in the **Details** panel.
- `settings.HOSTNAME` for password reset.
- Corrected an error where clearing the email field in Ask an expert modal would still allow a successful commit
- Added validation to avoid users import local files using URL
- Invite modal when opened from ribbon refreshed the **Organization** page on for submit if opened on that page.
- Fixed issue when selecting the region will cause region update and changes history to record new change.
- Added updated_by to dashboard API
- The Undo functionality for video labels was broken by the Show/Hide/Lock/Unlock actions.
- Improved delete tasks action speed.
- Fixed an issue when locking UI disappeared when "trial days" is negative.
- Fixed an issue when the image shrinks in certain cases.
- Logout menu displayed in smaller screens.
- Turned off lambda prefix and tags for cloud instance.
- Fixed a bug where the loader would appear when user list is empty
- Tasks were not updated after filter field changed in DM
- Fixed an issue when Sentry cannot properly capture Frontend exceptions.
- Excluded Deactivated/Not Activated users from project dashboards and member assignments lists.
- Checked user limit for invites.
- Deleted tasks were not working with some ordering (e.g. by annotators).
- Prevented the annotating collapsed phrases in paragraphs.
- Fixed tabs being randomly mixed after label stream.
- helm: Fixed support for Google project ids with only digits in name.
- Detached menu style update.
- **Copy to clipboard** icon was replaced with **Copied to clipboard** icon (green check-mark in circle) when an user clicked on it.
- Cannot change the user role for a user that had their invitation revoked.
- Sort order of regions grouped by labels was now based on label order + collapsed state persists through page load.
- Fixed tag template.
- Exact matching for attached tags (choices, numbers) ignored the labels spans.
- Fixed region grouping in Outliner.
- Fixed gaps on image borders on different screen sizes which may lead to slight region subpixel shifts.
- Show region index in Outliner to distinguish regions.
- Temporarily disabled the full-screen mode for video.
- Fixed Completed field in case maximum annotations change after overlap change.
- Created the possibility to enable pagination in the repeater for performance improvement.
- Added more error information when ML backend validation has failed.
- Allowed frames scrubbing on the timeline.
- Moved the video zoom button from the top to the controls section.
- Allowed video playhead/seeker scrubbing.
- Fixed an issue when `TextArea` placement in the config prevents video annotation.
- When a page was selected from a region, the item per page was changed to 1 and the selected item was displayed.
- `labels` to textarea result was not added.
- Fixed syncing data with invalid annotations or predictions.
- Fixed an issue when the user was unable to pan an image that was smaller than a viewport.
- Resolved an issue affecting filters.
- Switching to drawing tools during the process of drawing a new region was not supported.
- Fixed initial audio region history state.
- Fixed an error caused by expecting a field that doesn't always exist
- Fixed video regions w/o label
- Showed unsupported video format error message if not supported.
- Data manager broke when the annotator was deactivated.
- Resolved an obscure issue that can occur when changing `defaultZoom`, `defaultVolume` or `defaultSpeed` in Audio tag while working with Video Timeline Segmentation.
- Fixed video configuration validation.
- Resolved a pagination error on Data Manager.
- Fixed an issue with shifting image regions at different window sizes.
- Fixed annotator's data manager filters to persist through page reload.
- Added `CreateOnlyFieldsMixin` to `BaseUserSerializer` for emails to be write-able on creation.
- Fixed selected attribute in view configuration for Taxonomy.
- Fixed an issue affecting per region taxonomies where value would save on submit/update but wouldn't persist visually.
- Fixed an issue when high resolution videos produced bounding boxes with corrupted coordinates due to the zoom lag.
- Fixed selecting regions in outline and text when browsing history.
- Export failed with review counters in filters.
- Fixed an issue when the meta is not saved to the region.
- Removed interpolation from the currently selected frame hides the label and the selection box.
- Fixed the issue when the meta is not saved to the region.
- Enabled alias for taxonomy choice.
- Fixed URL serialization of numeric virtual tab filters..
- Fixed loading indicator resolving too early and showing no more annotations in label stream.
- Reverted current `isReady` fix.
- Denied removing users by API.
- Added simple equality metric for video.
- Fixed issue with `<Repeater>` scrolling and Taxonomy annotations display.
- Prevented the tabs from being removed and clearing out the related popup.
- Fixed CONLL export tokenization issue with splitting into individual tokens.
- Implemented Proxy storage links through nginx for auth check.
- Fixed review stats recalculation after metric change.
- The Bbox coordinates were preserved for both ‘Object detection' template and 'Repeater on images with taxonomy.
- Fixed project card to show correct counter for finished tasks.
- Removed the blocking modal when the server was unresponsive.
- Added per annotation choice distribution calculation.
- Fixed for projects, displayed on user's **Organization** page, include other organizations.
- Annotated audio regions spanned all channels.
- Previously created user through common signup failed with the SAML SSO login process.
- Fixed an issue with filtering over choices.
- Added agreement calculation for OCR template with `Brushlabels`, `RectangleLabels` and `Polygonlabels`.
- Fixed an agreement calculation for OCR with empty text values.
- Added images for empty annotations in export files for `You only look once (YOLO)` and `Visual Object Classes (VOC)`.
- SAML workspaces were reset on user login when `MANUAL_WORKSPACE_MANAGEMENT` was set to false.
- Cancelled skipped annotation retained previous history.
- Fixed review stream for assigned tasks.
- Fixed large timeseries datasets displayed incorrect `y` values.
- Fixed duplicating process to copy Google source/target storage.
- Fixed source storage duplicating tasks when clicking the **Sync** button multiple times.
- Vertical scrolling in **Review Stream** worked the same as in **Quick View** and **Label Stream**.
- Unfinished polygons were saved automatically and the history undo/redo hotkeys worked correctly.
- Stacktrace was no longer visible in the server error API responses.
- Resolved an issue affecting canceled skips for annotations where an incorrect button will display after.
- Fixed naive metric for the regions without labels and compound configs (like `<Rectangle>` + `<Labels>`).
- Fixed OCR template agreement calculation for missing labels.
- Removed project number from `file_name` of image in COCO Export.
- Fixed the issue when switching between history items doesn't display selected choices/taxonomy.
- Copied all project settings from template to new project.
- Fixed an issue with broken `<Repeater>` pagination mode when "Select regions after creating" was opted.
- Logins expired after 15 minutes of inactivity or 8 days after login, based on first come first served occurrence.
- Fixed validation error for history.
- Resolved an issue affecting the Eraser tool which made it unusable since it cleared selected regions on tool selection.
- Manual updates to region coordinates in the region editor were applied correctly and did not block moving the region.
- Fixed the empty `toName` in `Control` tag.
- Fixed an issue with history steps in the scenario of auto-detection.
- Navigation using task links was broken.
- Fixed an issue with high memory consumption, memory leakage, and increased loading times.
- Added edit/delete comment functionality.
- Addressed the issue when the dynamic `Choices` was saved with the incorrect/empty value.
- Updated swagger docs for `AllStorage` APIs.
- Added example output for `HyperTextLabels` in the Label Studio documentation suite.
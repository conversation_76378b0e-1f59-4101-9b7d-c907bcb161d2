---
hide_sidebar: true
---

## Label Studio Enterprise 2.20.1

<div class="onprem-highlight">Security-related fixes</div>

*Feb 12, 2025*

Helm Chart version: 1.9.2

### Security

- Image file paths are restricted as to prevent arbitrary path traversal. 
- As an XSS prevention measure, `/projects/upload-example` no longer accepts GET requests.
- Only recognized S3 endpoints from a list of known S3 API providers will return full list exceptions when an HTTP call is submitted.

!!! note
    If you want to use a non-standard/custom domain for hosting your S3 API and you still want full exceptions to be visible, you can add your domain to the `S3_TRUSTED_STORAGE_DOMAINS` environment variable.

    Separate multiple domains with a comma. For example, if the endpoints you are using are `https://foo.mys3endpoint.net` and `https://myothers3endpoint.biz`, then you would set it as: 
    
    `S3_TRUSTED_STORAGE_DOMAINS=mys3endpoint.net,myothers3endpoint.biz`

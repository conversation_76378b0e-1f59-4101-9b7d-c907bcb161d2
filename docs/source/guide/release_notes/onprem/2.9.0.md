---
hide_sidebar: true
---

## Label Studio Enterprise 2.9.0

<div class="onprem-highlight">Improved webhook performance and various UI improvements</div>

*Jan 16, 2024*

Helm chart version: 1.3.3

### Enhancements

- Improved webhook performance by assigning them to their own queue.

- Adjusted spacing in the SCIM and SAML/SSO pages to improve readability.

- When configuring SCIM with Ok<PERSON>, we will respond to their `userName` filter requests with the `email` attribute to ensure unique identifiers while maintaining compatibility.

- When hovering over the **Submitted Annotations** card on the project dashboard, the tooltip will make it clear when the number includes skipped and empty annotations.


### Security

- Fixed an issue with HTML sanitization to address a vulnerability identified by CodeQL.
- Addressed [`CVE-2024-23633`](https://github.com/HumanSignal/label-studio/security/advisories/GHSA-fq23-g58m-799r) by setting a `sandbox` CSP header on the `/data/upload/` endpoint. 

### Bug fixes

- Fixed some usability issues in the project Dashboards page related to the date picker and page refresh.

- Fixed an issue where, when labeling tasks, hiding a region would create a draft and display the **Fix and Accept** action, even if no other changes had been made.

- Fixed an issue where credential validation was failing in the Label Studio interface for cloud storages configured using SDK.


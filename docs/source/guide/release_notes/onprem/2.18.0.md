---
hide_sidebar: true
---

## Label Studio Enterprise 2.18.0

<div class="onprem-highlight">Link comments to fields, export information from the Annotator Performance dashboard </div>

*Nov 19, 2024*

Helm Chart version: 1.7.1

### New features

#### Link comments to fields

You can now link comments to specific regions or fields within an annotation.

This change will help improve clarity between Annotators and Reviewers, enhancing the quality review process.

For more information, see [Comments and notifications](https://docs.humansignal.com/guide/comments_notifications).

![Screenshot of comment linking](/images/releases/2-18-comments1.png)

![Screenshot of comment linking](/images/releases/2-18-comments2.png)

#### Export information from the Annotator Performance dashboard

There is a new **Export** action available from the [Annotator Performance dashboard.](https://docs.humansignal.com/guide/dashboard_annotator)

- **Report** - Download the information in the dashboard as CSV or JSON.
- **Timeline** - Download a detailed timeline of all the user's annotation actions within the timeframe, including when the began and submitted each annotation.
- **Comments Received** - Download a CSV file with all of the comments that other users have left on the user's annotations.

![Screenshot of export action](/images/releases/2-18-export.png)

### Enhancements

#### Performance score added to the Annotator Performance dashboard

A **Performance Score** metric has been added to the Annotator Performance dashboard metrics. This reflects the overall performance of annotators in terms of review actions (**Accept**, **Reject**, **Fix+Accept**). For more information, see [Performance summaries](https://docs.humansignal.com/guide/dashboard_annotator#Performance-summaries). 

![Screenshot of Performance Score metric](/images/releases/2-18-score.png)

#### Edit regions when classifying video frames

We recently introduced the ability to perform [video frame classification](https://docs.humansignal.com/templates/video_frame_classification) with the `<TimelineLabels>` tag.

You now have the ability to edit the frame spans you select in the timeline editor, making it easier to control which frames you want to label.

![Screenshot of edit action](/images/releases/2-18-edit.png)

#### Improved usability on project settings pages

There are a number of [project settings](https://docs.humansignal.com/guide/project_settings_lse) that are only applicable when auto distribution is enabled for users.

To prevent confusion, settings that are not applicable will be hidden when manual distribution is enabled.

This means the following settings will be hidden when **Annotation > Distribute Labeling Tasks** is set to **Manual**:

- **Annotation > Task Sampling**
- **Quality > Overlap of Annotations**
- **Quality > Low Agreement Strategy**

#### Hotkey to show/hide all regions

A new hotkey (**Ctrl + h**) has been added. Use this shortcut to hide all regions. Or, if no regions are visible, show all regions.

### Bug fixes

- Fixed an issue where users were shown a 500 error when attempting to create a project without first selecting a workspace.

- Fixed an issue where in certain scenarios users were unable to receive a password reset email.

- Fixed an issue where non-unicode symbols would cause the Activity Log page to not load.

- Fixed an issue where, despite the project settings, reviewers were not required to leave a comment on reject if they were using Quick View.

- Fixed an issue where links were not resolving when using multiple S3 storages.

- Fixed an issue where users were unable to use multiple source storages.

- Fixed a small UI issue seen when displaying drop-down menus with multiple nested selection options.

- Fixed an issue where deleting reviews did not clear cancelled values from the Data Manager.

- Fixed an issue where the **Allow reviewer to choose: Requeue or Remove** setting could cause the Label All Tasks action to be enabled for annotators when there were no tasks to label.

- Fixed an issue where instructions were not visible to reviewers in the Review Stream.

- Fixed an issue in which SMTP configuration was not working correctly despite passing initial tests.

- Fixed an issue where deleted annotator users were not available as option when building filters in the Data Manager.

- Fixed an issue that could produce duplicate accounts when synced from SCIM.

- Fixed an issue where the application would crash when annotators who have also had a project role of Reviewer would navigate to the Data Manager.

- Fixed an issue where users were getting errors if using Redis passwords that included special characters.


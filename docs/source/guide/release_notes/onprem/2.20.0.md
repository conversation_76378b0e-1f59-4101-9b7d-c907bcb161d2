---
hide_sidebar: true
---

## Label Studio Enterprise 2.20.0

<div class="onprem-highlight">Taxonomy for labeling, new audio hotkey, performance improvements, bug fixes </div>

*Jan 28, 2025*

Helm Chart version: 1.9.0

### Enhancements

#### Use Taxonomy for labeling

There is a new `labeling` parameter available for the Taxonomy tag. When set to true, you can apply your Taxonomy classes to different regions in text. For more information, see [Taxonomy as a labeling tool](/templates/taxonomy).

![Screenshot of taxonomy as labeling tool](/images/releases/2-20-taxonomy.png)

#### Hotkeys for audio labeling

There is a new hotkey available for pausing and starting audio: `ctrl`+`p` (Windows) or `command`+`p` (Mac). This is in addition to the space hotkey that performs the same function. However, this new hotkey is useful when you are working with audio and have a text area field in focus.

#### Video frame classification template

The video frame classification template is now available in the Label Studio app as well as [the documentation](/templates/video_frame_classification).


#### Performance improvements

Optimized the API calls made from the frontend within the members management and Data Manager users lists. Also optimized the Projects page for faster load times. 

### Security

- Upgraded pyarrow to address vulnerabilities in older packages.

- Updated the default settings for CSRF cookie to be more secure and added an environment setting to control cookie age.

### Breaking changes

This release includes an upgrade to Django 5. As part of this change, Label Studio now requires PostgreSQL version 13+. 

### Bug fixes

- Fixed an issue where the Label Studio version as displayed in the side menu was not formatted properly.
  
- Fixed an issue where the `contextlog` was not reporting the `content_type`.

- Fixed an issue with overlapping relations on the overlay on highlighting.

- Fixed an issue where task IDs were being duplicated when importing a large number of tasks through the API.

- Fixed an issue where users were not being redirected to the appropriate page after logging in.

- Fixed an issue where users were unable to edit meta information that they previously added to bounding box regions.

- Fixed multiple issues resulting from Poetry/Poetry core 2 release.

- Fixed an issue where the django-rq admin page was unavailable.

- Fixed a possible race condition when dynamically loading the Data Manager or editor that would prevent either from loading.

- Fixed an issue where skipped tasks were not being calculated as completed when the project Skip Queue setting was set to **Ignore Skipped**.



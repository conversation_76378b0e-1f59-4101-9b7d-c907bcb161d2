---
hide_sidebar: true
---

## Label Studio Enterprise 2.8.0

<div class="onprem-highlight">Improved member list filtering from the Organization page, collapsible Ranker items, various UI improvements</div>

*Dec 19, 2023*

Helm chart version: 1.3.2

### Enhancements

- The members list on the Organization page now allows you to filter by one or more roles. By default, Deactivated and Not Activated users are hidden. This makes it easier to locate users within the member list.

    ![Screenshot of org list filter](/images/releases/2-8-0-org-list.png)

- You can now expand and collapse Ranker items for easier management and rearrangement.

    ![Animated gif of Ranker in action](/images/releases/2-8-0-ranker2.gif)

- Added a new `disable_reviewer_annotator_tokens`setting for organization licenses. When set to `true`, token authorization is disabled for users in the Reviewer and Annotator role. This will help maintain organization security by preventing users from accessing Label Studio via the API.

- Updated the font and spacing for the **Submit and Exit** button to match Label Studio UI styling guidelines.

- Updated text on the SAML and SCIM settings pages to match Label Studio UI styling guidelines.

- Improved error message clarity when configuring SCIM.

- Added the ability to reinstate deleted users by re-inviting them to the organization.

- Improved usability of the Label Interface Settings options, so that clicking anywhere within the description toggles the setting.

### Security

- Upgraded urllib3 to 1.26.18 to address [CVE-2023-45803](https://github.com/advisories/GHSA-g4mx-q9vg-27p4), and Django to 3.2.23 to address [CVE-2023-46695](https://github.com/advisories/GHSA-qmf9-6jqf-j8fq).

- Patched an ORM leak vulnerability. 

- Due to an XSS vulnerability, we previously added a requirement that users must log in to view the Label Studio API doc reference and Swagger. You can now view the [API docs](https://app.heartex.com/docs/api/) without logging in. However, the Swagger version is still only available to logged in users. 


### Bug fixes

- Fixed an issue where reviewers could not see annotation ID column.

- Fixed an issue where role selections were not persisting after reloading the Organization page.

- Fixed an issue where deleted users were appearing in the members list on the Organization page.

- Fixed an issue where the Labeling Interface was not saving user changed.

- Fixed an issue where activity logs contained sensitive information when using SCIM.

- Fixed an issue where users were unable to edit polygon points.

- Fixed a sync error when importing large amounts of tasks from Azure storage.

- Fixed an issue where the crosshair parameter was not working.

- Fixed an issue where the **Draft saved successfully** message was appearing when it wasn’t needed.

- Fixed some usability issues in the project Dashboards page related to the date picker and page refresh.

- Fixed an issue with wrong position of brushstroke highlighted on hover.

- Fixed an issue with relation positions in multi-image segmentation.

- Fixed an issue where the **Auto accept annotation suggestions** toggle was not working as expected in some situations.


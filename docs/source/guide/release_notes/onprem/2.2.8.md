---
hide_sidebar: true
---

## Label Studio Enterprise 2.2.8

<div class="onprem-highlight">Comment visibility, SCIM enhancements, Redis SSL support, notification center, drafts in Annotation History, new history types</div>

This section highlights the breaking changes, new features and enhancements, and bug fixes in Label Studio Enterprise 2.2.8. 

### New features and enhancements
Label Studio Enterprise 2.2.8 introduces the following new features and enhancements.

- This release displays comments in **DM** to reviewers.
- Support for [Redis Secure Sockets Layer (SSL)](security.html#Secure-access-to-Redis-storage).
- Add tags and prefixes to [AWS metric parameters](custom_metric.html#How-to-write-your-custom-agreement-metric).
- Change review API to take into account feedback loop.
- Notification Center
- Add drafts in Annotation History.
- Introduce new history types. 
- Support for System for Cross-domain Identity Management (SCIM 2.0) user and group provisioning.
- Add the ability to pass a comment to APIs on skip.

### Bugfixes
Label Studio 2.2.8 includes the following bug fixes:

- Per label score for Choices was calculated when no Choice was selected (hotfix-7).
- Fixed Rotating bounding box bugs (hotfix-4)
- Fixed permissions for manager role (hotfix-3)
- Fixed export to file using `SerializableGenerator`.
- Fixed accepted state in review.
- Made Annotation History with linear Reject (Reject = Update + Reject).
- Fixed Annotation History icons.
- Annotation history fixes.
- Fixed an issue where the Annotation History was not loading because of string ID.
- Fixed validation in Labeling Interface preview with Dynamic Labels.
- Fixed history 404 on unskip in label stream.
- Fixed **Annotation History** reset for predictions.
- Fixed job cancellation for `_update_tasks_states`.
- Fixed an issue to return `404` for `api/project/id/tasks` when the page was out of scope
- Interactive preannotations for **Paragraphs**.
- Improved the speed to 180 secs for assigned tasks.
- Disabled **Poly** and **Keypoints** for **Annotation History**.
- Fixed tools multiplication issue.
- Prevented the scroll-out **TopBar** option.
- Fixed skip queue.
- Allowed **Canvas** to fill all the space.
- Truncated long words in comments.
- Added scroll to view when focus changes to `stickyList` in table component.
- Used `contain` instead of `icontain` for **Annotation Result** field in the **Data manager** filters.
- Fixed `is_labeled` for tasks with no assignments.
- Added default settings.
- Implemented `Go back to previously reviewed task` functionality for reviewing stream.
- Refactored and optimized Redis Queues.
- Fixed runtime error during import with no `total_annotations` and other.
- Reviewed Next Task API performance optimizations.
- Fixed the reset rejected status after the annotation update.
- Fixed skip **Annotation History** for the previous task in label stream.
- Fixed Reviewed filter.
- Fixed counters for skipped annotations.
- Fixed an issue where tasks were flagged as REVIEWED by default.
- Fixed an issue for skipped tasks to get the `Completed` status.
- Fixed error when a user tried to delete all tasks.
- Fixed filter by empty reviewers.
- Fixed incorrect review card counters in the feedback loop for skipped annotations.
- Moved from signal to model delete method.
- Added new skip behavior for annotations that are requeued back to annotator.
- Fixed **Annotation History** drafts.
- Fixed regions for text span when it was out of bounding in the regions list and created horizontal scroll.
- Fixed in **Manage Members** modal (on project **Members** tab, on workspace members, on **Members** settings) header with search overlaps by the main list.
- Fixed `Textarea` for **Custom Function** on the **Quality** settings page.
- Fixed `startOffset` for empty nodes.
- Fixed the runtime error for users who deleted an annotation from **Quick View**,  switched to another task, and returned back to the same task.
- Added command for all orgs and optimize `update_tasks_counters`.
- After annotations from predictions `is_labeled` should be recalculated.
- Fixed 404 on skip.

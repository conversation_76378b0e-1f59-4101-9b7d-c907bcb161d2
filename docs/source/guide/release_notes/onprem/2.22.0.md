---
hide_sidebar: true
---

## Label Studio Enterprise 2.22.0

<div class="onprem-highlight">Pause annotators, set annotation limits, new API tokens, deep linking for annotation and regions, usability improvements for audio</div>

*Mar 25, 2025*

Helm Chart version: 1.9.6

### New features

#### Pause an annotator

There is a new action to pause annotators. This is available from the Members dashboard and via the API.

For more information, see [Pause an annotator](https://docs.humansignal.com/guide/quality#Pause-an-annotator).

![Screenshot of pause](/images/review/pause.png)

#### Annotation limits

There is a new **Quality > Annotation Limit** section in the project settings.

You can use these fields to set limits on how many tasks each user is able to annotate. Once the limit is reached, their progress will be paused.

For more information, see [Annotation Limit](https://docs.humansignal.com/guide/project_settings_lse#annotation-limit).

![Screenshot of annotation limits](/images/releases/2-22-annotation-limit.png)

#### Personal access tokens

There is a new type of token available for API access. The new tokens use JWT standards.

You can enable or disable these tokens from the Organization page. Once enabled, they will be available for users to generate from their Account & Settings page. Legacy tokens can still be used unless disabled from the organization level. 

For more information, see [Access tokens](access_tokens).

![Screenshot of tokens 1](/images/releases/2-22-jwt-org.png)

![Screenshot of tokens 2](/images/releases/2-22-jwt.png)

#### Deep link annotations and regions

You can now link directly to specific annotations or regions within an annotation. These actions are available from the labeling interface in the overflow menus for the annotation and the region. 

![Screenshot of linking annotation](/images/releases/2-22-link-annotation.png)

![Screenshot of linking region](/images/releases/2-22-link-region.png)


### Enhancements

#### Usability improvements for audio tasks

**Scrollbar navigation**

You can now scroll forward and backward within audio files. This can be activated using the scrolling motion on a trackpad or a mouse.

![Screenshot of audio scroll](/images/releases/2-22-audio-scroll.png)

**New settings**

We have introduced two new settings for audio tasks:

- **Auto-Play New Regions -** Automatically play a new region after it has been selected.
- **Loop Regions** - When playing a region, loop the audio.
  
![Screenshot of audio scroll](/images/releases/2-22-audio-settings.png)

#### New templates

There are three new templates available from the template gallery:

- **Natural Language Processing > [Content Moderation](/templates/content_moderation)**
- **Computer Vision > [Medical Imaging Classification with Bounding Boxes](/templates/medical_imaging_classification)**
- **Generative AI > [LLM Response Grading](/templates/llm_response_grading)**

#### Miscellaneous

- Added a link to a user’s performance summary from the Annotation Summary table on the Members dashboard.

- The Label Studio URL format has been updated so that you can now link to specific workspaces.

- Improved 4xx and 5xx error page design to include helpful links.

- Added validation for S3 bucket name formats.

- Performance improvements around notifications and caching.

- Error handling improvements.

- UI fixes to ensure consistency in styles across Label Studio.

### Security

Made security improvements regarding org membership visibility.

### Bug fixes

- Fixed an issue where images were distorted when zooming in.

- Fixed an issue where an empty Quick View was displayed if a user tried to open a URL linking to a non-existing task ID.

- Fixed an issue where deeply nested Choices were visible even if parents were hidden.

- Fixed an issue where users were able to create bounding boxes outside the image boundaries.

- Fixed an issue that was causing intermittent loading errors in the Data Manager.

- Fixed an issue that caused incorrect video frame to be rendered when pausing.

- Fixed an infinite loading issue with the notification drawer.

- Fixed an issue where users in the Reviewer role were able to submit reviews via the API even if they were not a project member.

- Fixed an issue that would cause an API error when switching workspaces and fetching the incorrect page of projects.

- Fixed an issue where users were getting their role reset if they were provisioned via SCIM without an assigned group.

- Fixed an issue where filters were not being respected when performing bulk annotation actions.

- Fixed an issue where bulk annotation was sometimes failing with a 500 error.

- Fixed an issue with resizing Bulk Annotation drawer after having collapsed it previously.

- Fixed an issue where bulk annotation was not respecting the **Allow empty annotations** setting.

- Fixed an issue where an error was sometimes thrown when loading the workspaces list.

- Fixed an issue with the signup link styling for white labeled applications.



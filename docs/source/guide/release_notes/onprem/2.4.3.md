---
hide_sidebar: true
---

## Label Studio Enterprise 2.4.3

<div class="onprem-highlight">New <code>splitchannels</code> option on audio configs, keyboard shortcuts for the Data Manager, restore locked annotations</div>

### Enhancements
- Support simultaneous render of multi-channel audio with added splitchannels="true" option on Audio config (larger memory requirement)
- Allow selecting task automatically on Data Manager page whenever the user presses shift+up/down
- Restore locked annotations
- Navigation back to previous tasks in labeling stream is now persistent against page reloads and user labeling sessions
- Add sync update of is_labeled field
- Improved responsiveness for image regions selection

### Bug fixes
- Fixed bug with presigned TTL setting in cloud storage connection being not persistent
- <PERSON><PERSON><PERSON> follows the positional seeker in video when using the step forward or backward buttons.
- Now it is possible to retrieve the list of uploaded files with `api/projects/<project-id>/file-uploads&all=true` request
- Improved performance for projects page and annotation-related API
- Setting - Quality - Custom weights: UI too limited
- Wrong xpath in annotation result (remove FF)
- Fixed an issue with missed step of setting password for invited users
- AnnotationReview stats are calculated in 2 separate jobs
- Task data with dicts in array are incorrectly resolved
- Fixed authorization check for roles change
- Prevent persistent Cross-Site Scripting (XSS) in Activity Log
- Fixed issue with saving empty drafts
- Unclear error about unknown tag
- Migration for fixing organization id in activity logs
- The first Audio V3 region created is not added to the undo stack

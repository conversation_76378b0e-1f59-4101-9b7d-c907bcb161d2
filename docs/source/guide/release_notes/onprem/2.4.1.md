---
hide_sidebar: true
---

## Label Studio Enterprise 2.4.1
<div class="onprem-highlight">Improved logging, API for project annotation history</div>

### New features and enhancements 
- Add project annotation history API
- Improve logging

### Bug fixes
- Fix anonymize annotators while reviewing
- Unrelated organizations can view Roles from another organizations
- Remove unused task lock ttl settings
- Fixed issue with displaying history in review after View All
- Readonly regions should always be selectable.
- Fix agreement calculation for Taxonomy with custom labels
- Restrict tabs deletion for reviewers
- Fixed issue with hotkeys of dynamic children
- Add validation for required toName parameter on TextArea
- Fix project cloning with cloud storages tasks
- Add filters by columns, datetime range filter and ordering to activity log
- Add project annotation history API
- Logs error: AttributeError: 'Task' object has no attribute 'get_lock_ttl'
- Enable player head time input and change the way that it was working
- Switch level of next task log messages
- Fix log message
- Fix layout in Preview for small content configs panels now are pinned to the bottom
- Prevent annotations duplicating in Label Stream
- Fix status code for not found task to 404 in tasks API
- Text and HyperText elements should have value and name properties
- Fixed issues in following  cursor pointer during video zooming
- Task locks in postponed/skipped queue
- Prevent annotation duplicates when 'Re-queue skipped tasks back to the annotator' parameter is selected
- Add Google project ID to Source/Target GCS form
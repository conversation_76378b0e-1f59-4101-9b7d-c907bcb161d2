---
hide_sidebar: true
---

## Label Studio Enterprise 2.10.1

<div class="onprem-highlight">New <b>Reset Cache</b> action for projects, security update for activity logs, various bug fixes</div>

*Mar 12, 2024*

Helm chart version: 1.4.0

### Enhancements

There is a new **Reset Cache** action available from project settings under the **Danger Zone** section. You can use this action to reset and recalculate the labeling cache. 

This action is particularly useful for situations in which you are attempting to modify the labeling configuration and you receive a validation error pointing to non-existent labels or drafts. 

![Screenshot of the Reset Cache action](/images/releases/2-10-1-reset-cache.png)

### Security

Fixed an issue where sensitive information was available in activity logs. 

### Bug fixes

- Fixed an issue where users could not submit annotations if the labeling configuration included the `TextArea` tag with the `required` and `skipDuplicates` parameters.

- Fixed an issue where the Projects page was displaying the wrong number when indicating how many projects are displayed on the page.

- Fixed an issue where when calling `GET api/tasks?projects={id}&fields=all`, reviews were not returned.

- Fixed an issue where an empty draft was created every time a user clicked **View all annotations**.

- Fixed an issue where the URL in email invites was missing the `http` protocol.

- Fixed an issue where sometimes the **Refresh** action on the project dashboard would be stuck in a loading state.


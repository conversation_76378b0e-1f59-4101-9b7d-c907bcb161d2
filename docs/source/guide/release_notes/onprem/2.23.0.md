---
hide_sidebar: true
---

## Label Studio Enterprise 2.23.0

<div class="onprem-highlight">Google Cloud Storage WIF, drag-and-drop for video timelines, multiple security enhancements</div>

*Apr 22, 2025*

Helm Chart version: 1.9.9

### New features

#### Support for Google Cloud Storage Workload Identity Federation (WIF)

When adding project storage, you now have the option to choose Google Cloud Storage WIF.

Unlike the the standard GCS connection using application credentials, this allows Label Studio to request temporary credentials when connecting to your storage.

For more information, see [Google Cloud Storage with Workload Identity Federation (WIF)](https://docs.humansignal.com/guide/storage#Google-Cloud-Storage-with-Workload-Identity-Federation-WIF).

![Screenshot of WIF](/images/releases/2-23-wif.png)

!!! note
    While this option is available for on-prem users, the typical way to set up GCS in an on-prem environment is through persistent storage as documented [here](https://docs.humansignal.com/guide/persistent_storage.html#Configure-the-GCS-bucket). 


### Enhancements

#### Drag-and-drop adjustment for video timeline segments

You can now drag and drop to adjust the length of video timeline segments.

![Screenshot of video timeline](/images/releases/2-23-drag-drop.png)

#### "Custom Scripts" are now "Plugins"

We have renamed "Custom Scripts" to "Plugins." This is reflected in the UI and [in our docs](/plugins).

![Screenshot of video timeline](/images/releases/2-23-plugins.png)

#### Miscellaneous

- Improved tooltips related to [pausing annotators](quality).

- Ensured that when a user is deactivated, they are also automatically logged out. Previously they lost all access, but were not automatically logged out of active sessions.

- Multiple performance improvements for our [AI Assistant](ask_ai).


### Security

- Made security improvements around the verbosity of certain API calls.

- Made security improvements around SAML.

- Made security improvements around project parameter validation.

- Made security improvements around exception error messages.

- Made security improvements around workspace permission checks.


### Bug fixes

- Fixed an issue where importing from the UI would fail when importing from a URL.

- Fixed an issue where users were unable to edit custom agreement metrics if using manual distribution mode.

- Fixed an issue where regions would be added to the wrong task when moving quickly between tasks.

- Fixed an issue where **Exact frames matching for video** was always showing as an option for agreement metrics regardless of whether the labeling config referenced a video.

- Fixed an issue where the `prediction-changed` value was not being reset after making manual changes to pre-annotations.

- Fixed an issue where a paused annotator is unpaused when a reviewer rejects their annotation and the project is configured to requeue tasks back to the annotator.

- Fixed an issue where some segments were not previewable when annotating videos with the TimeLineLabels tag.

- Fixed several small issues with how labeled regions appear when completing OCR tasks.








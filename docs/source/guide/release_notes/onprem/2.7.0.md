---
hide_sidebar: true
---


## Label Studio Enterprise 2.7.0

<div class="onprem-highlight">External taxonomy, group visibility in label distribution charts, soft delete users</div>

*Nov 21, 2023*

Helm chart version: 1.2.9

### New Features

#### External taxonomy

This release introduces the ability to load an external taxonomy into your labeling configuration. The mechanism for this is a new `apiUrl` parameter on the Taxonomy tag, which allows you to load your taxonomy from an external JSON-formatted file. Previously, you had to use `Choice` tags to manually define your taxonomy within the labeling configuration. 

This feature provides multiple benefits, including:

- **Performance** - Significant performance improvements for large taxonomies.
- **Usability and standardization** - With JSON formatting and the ability to manage taxonomies in your editor of choice, external taxonomies are easier to organize and update.
- **Security** - You can now securely store taxonomies outside of Label Studio.

Fore more information, see [Quickly Load and Manage Large-Scale Taxonomies From External Sources](https://humansignal.com/blog/new-quickly-load-and-manage-large-scale-taxonomies-from-external-sources/), the [Taxonomy template](https://docs.humansignal.com/templates/taxonomy), and [Taxonomy tag](https://docs.humansignal.com/tags/taxonomy). 

![Animated gif showing taxonomy in action](/images/releases/2-7-0-taxonomy.gif)

#### Group visibility in label distribution charts and drag-and-drop reordering

When a labeling configuration includes multiple label groups, you can now use the project dashboard to gain insight into label group performance.   

A new **Summary** view displays a donut chart showing label group distribution. This allows for more visibility into your labeling progress, and will help you identify areas within a project that might need additional task data.  

![Screenshot of label group distribution chart](/images/releases/2-7-0-label-groups.png)

You will also now be able to drag-and-drop the KPIs and charts to reorder them to your preference. 

For more information, see [Introducing Label Distribution Charts for Label Groups and User Soft Delete](https://humansignal.com/blog/introducing-label-distribution-charts-for-label-groups-and-user-soft-delete/). 

![Animated gif showing dashboard reordering](/images/releases/2-7-0-reorder.gif)

#### Soft delete users

Administrators can now delete users through the app or the API. This feature is intended to provide enhanced user management for administrators while minimizing potential security risks. 

Previously, administrators could only remove users by deactivating their account. However, this made it difficult to differentiate between users who are unlikely to return (such as a former employee) and users who might be temporarily inactive (such as freelance annotators). Now you can choose to either deactivate or delete a user. 

For more information, see [Introducing Label Distribution Charts for Label Groups and User Soft Delete](https://humansignal.com/blog/introducing-label-distribution-charts-for-label-groups-and-user-soft-delete/). 

![Screenshot of user delete action](/images/releases/2-7-0-user-delete.png)

### Enhancements

- Added support for AWS Signature Version 4 query parameters.
- The **Submitted Annotations** metric on project dashboards now includes a tooltip with additional information about skipped and empty annotations.

    ![Screenshot of tooltip on dashboard](/images/releases/2-7-0-tooltip.png)

### Security

- Fixed an SSRF DNS rebinding issue.
- Fixed an XSS vulnerability on certain error pages.
- Fixed an XSS vulnerability related to file extensions for avatars. This change addresses [`CVE-2023-47115`](https://github.com/HumanSignal/label-studio/security/advisories/GHSA-q68h-xwq5-mm7x).

### Bug fixes

- Fixed an issue where a validation error was improperly displayed when setting the labeling configuration for a project.
- Fixed an issue with zoom performance in Image Segmentation cases.
- Fixed an issue where annotator performance dashboard agreements were incorrect.
- Fixed a run time error seen when syncing with Azure blob storage.
- Fixed an issue where tasks created through source storage were not triggering webhooks.
- Fixed an issue where code was unnecessarily executing when contextual scrolling was disabled.
- Fixed an issue where draft annotations were not being saved before navigating away.
- Fixed an issue where `PATCH api/tasks/<id>` was returning an error.
- Fixed an issue where a duplicate project would incorrectly contain an annotation count with no annotations copied.
- Fixed an issue where agreement groundtruth API calls were using excess resources.


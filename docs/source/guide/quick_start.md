---
title: Quick start
short: Quick start
tier: opensource
type: guide
order: 50
order_enterprise: 0
meta_title: Quick start guide for Label Studio
meta_description: Quick start guide for installing Label Studio and creating a new project. 
section: "Install & Setup"
date: 2023-11-27 13:34:32
---

1. Install Label Studio:
```bash
pip install label-studio
```

2. Start Label Studio
```bash
label-studio start
```

1. Open Label Studio at `http://localhost:8080`.
2. Sign up with an email address and password that you create.
3. Click **Create** to create a project and start labeling data.
4. Name the project and optionally enter a description and select a color.
5. Click **Data Import** and upload the data files that you want to use. If you want to use data from a local directory, cloud storage bucket, or database, skip this step for now.
6. Click **Labeling Setup** and choose a template and customize the label names for your use case.
7. Click **Save** to save your project.

You're ready to start [labeling and annotating your data](labeling.html)!

!!! info Tip
    For a quickstart tutorial that includes demo data, see [Zero to One: Getting Started with Label Studio](https://labelstud.io/blog/zero-to-one-getting-started-with-label-studio/). 



---
title: Set up email backend for Label Studio Enterprise
short: Set up email backend
tier: enterprise
type: guide
order: 0
order_enterprise: 90
meta_title: Email backends in Label Studio
section: "Install & Setup"

---

In Label Studio Enterprise, you can configure email backends to enable password reset via email and receive notifications. There are three available options for setting up email backends:
* Dummy console email backend - all emails will be printed in the app console.
* SMTP backend
* [Sendgrid backend](https://sendgrid.com/)


### Dummy Console Backend

The Dummy Console Email Backend is a simple option for testing purposes. When this backend is configured, all emails generated by the application will be printed in the application console, rather than being sent to recipients.

```bash
FROM_EMAIL = Label Studio <<EMAIL>>

# option 1: app console
EMAIL_BACKEND = 'django.core.mail.backends.console.EmailBackend'
```

### SMTP Backend

The SMTP (Simple Mail Transfer Protocol) Backend allows you to use a custom SMTP server to send emails. This provides flexibility in configuring email delivery options and can integrate with various email service providers.

More details can be found here: https://docs.djangoproject.com/en/4.2/topics/email/#smtp-backend. If your SMTP configuration is not compatible with certificate or hostname validation, we provide the
`label_studio.core.utils.mail.NoVerificationEmailBackend` which can be used instead of Django's default `smtp.EmailBackend`, but note that this option is less secure. See https://docs.djangoproject.com/en/5.0/releases/4.2/#miscellaneous for more information.

```bash
# SMTP server,
FROM_EMAIL=Label Studio <<EMAIL>>
EMAIL_BACKEND=django.core.mail.backends.smtp.EmailBackend
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=PASSWORD # It's your account password, or separate application password, if you have 2fa on your google account
EMAIL_USE_TLS=1
EMAIL_USE_SSL=0
# EMAIL_SSL_KEYFILE=/path/inside/of/ls/container/key  # optional
# EMAIL_SSL_CERTFILE=/path/inside/of/ls/container/cert  # optional
EMAIL_TIMEOUT=60
```

### Sendgrid Backend

The Sendgrid Backend utilizes the Sendgrid API to send emails. This option requires an active Sendgrid account and API key for authentication. The Sendgrid backend offers an easy-to-use and reliable email delivery service.

```bash
# option 2: Sendgrid
EMAIL_BACKEND=sendgrid_backend.SendgridBackend
SENDGRID_API_KEY=<SENDGRID-API-KEY>
```

---
cards:
- categories:
  - Natural Language Processing
  - Text Classification
  - BERT
  - Hugging Face
  hide_frontmatter_title: true
  hide_menu: true
  image: /tutorials/bert.png
  meta_description: Tutorial on how to use BERT-based text classification with your
    Label Studio project
  meta_title: BERT-based text classification
  order: 35
  tier: all
  title: Classify text with a BERT model
  type: guide
  url: /tutorials/bert_classifier.html
- categories:
  - Computer Vision
  - Optical Character Recognition
  - EasyOCR
  hide_frontmatter_title: true
  hide_menu: true
  image: /tutorials/easyocr.png
  meta_description: The EasyOCR model connection integrates the capabilities of EasyOCR
    with Label Studio to assist in machine learning labeling tasks involving Optical
    Character Recognition (OCR).
  meta_title: EasyOCR model connection for transcribing text in images
  order: 40
  tier: all
  title: Transcribe text from images with EasyOCR
  type: guide
  url: /tutorials/easyocr.html
- categories:
  - Natural Language Processing
  - Named Entity Recognition
  - Flair
  hide_frontmatter_title: true
  hide_menu: true
  image: /tutorials/flair.png
  meta_description: Tutorial on how to use Label Studio and Flair for faster NER labeling
  meta_title: Use Flair with Label Studio
  order: 75
  tier: all
  title: NER labeling with Flair
  type: guide
  url: /tutorials/flair.html
- categories:
  - Natural Language Processing
  - Named Entity Recognition
  - GLiNER
  - BERT
  - Hugging Face
  hide_frontmatter_title: true
  hide_menu: true
  image: /tutorials/gliner.png
  meta_description: Tutorial on how to use GLiNER with your Label Studio project to
    complete NER tasks
  meta_title: Use GLiNER for NER annotation
  order: 37
  tier: all
  title: Use GLiNER for NER annotation
  type: guide
  url: /tutorials/gliner.html
- categories:
  - Computer Vision
  - Image Annotation
  - Object Detection
  - Grounding DINO
  hide_frontmatter_title: true
  hide_menu: true
  image: /tutorials/grounding-dino.png
  meta_description: Label Studio tutorial for using Grounding DINO for zero-shot object
    detection in images
  meta_title: Image segmentation in Label Studio using a Grounding DINO backend
  order: 15
  tier: all
  title: Zero-shot object detection and image segmentation with Grounding DINO
  type: guide
  url: /tutorials/grounding_dino.html
- categories:
  - Computer Vision
  - Image Annotation
  - Object Detection
  - Zero-shot Image Segmentation
  - Grounding DINO
  - Segment Anything Model
  hide_frontmatter_title: true
  hide_menu: true
  image: /tutorials/grounding-sam.png
  meta_description: Label Studio tutorial for using Grounding DINO and SAM for zero-shot
    object detection in images
  meta_title: Image segmentation in Label Studio using a Grounding DINO backend and
    SAM
  order: 15
  tier: all
  title: Zero-shot object detection and image segmentation with Grounding DINO and
    SAM
  type: guide
  url: /tutorials/grounding_sam.html
- categories:
  - Generative AI
  - Large Language Model
  - Text Generation
  - Hugging Face
  hide_frontmatter_title: true
  hide_menu: true
  image: /tutorials/hf-llm.png
  meta_description: This tutorial explains how to run Hugging Face Large Language
    model backend in Label Studio. Hugging Face Large Language Model Backend is a
    machine learning backend designed to work with Label Studio, providing a custom
    model for text generation.
  meta_title: Label Studio tutorial to run Hugging Face Large Language Model backend
  order: 20
  tier: all
  title: Hugging Face Large Language Model (LLM)
  type: guide
  url: /tutorials/huggingface_llm.html
- categories:
  - Natural Language Processing
  - Named Entity Recognition
  - Hugging Face
  hide_frontmatter_title: true
  hide_menu: true
  image: /tutorials/hf-ner.png
  meta_description: This tutorial explains how to run a Hugging Face NER backend in
    Label Studio.
  meta_title: Label Studio tutorial to run Hugging Face NER backend
  order: 25
  tier: all
  title: Hugging Face NER
  type: guide
  url: /tutorials/huggingface_ner.html
- categories:
  - Natural Language Processing
  - Named Entity Recognition
  - Interactive matching
  hide_frontmatter_title: true
  hide_menu: true
  image: /tutorials/interactive-substring-matching.png
  meta_description: Use the interactive substring matching model for labeling NER
    tasks in Label Studio
  meta_title: Interactive substring matching for NER tasks
  order: 30
  tier: all
  title: Interactive substring matching for NER tasks
  type: guide
  url: /tutorials/interactive_substring_matching.html
- categories:
  - Generative AI
  - Retrieval Augmented Generation
  - Google
  - OpenAI
  - Langchain
  hide_frontmatter_title: true
  hide_menu: true
  image: /tutorials/langchain.png
  meta_description: Use Langchain, OpenAI, and Google to generate responses based
    on Google search results.
  meta_title: RAG with a Langchain search agent
  order: 45
  tier: all
  title: RAG with a Langchain search agent
  type: guide
  url: /tutorials/langchain_search_agent.html
- categories:
  - Generative AI
  - Large Language Model
  - OpenAI
  - Azure
  - Ollama
  - ChatGPT
  hide_frontmatter_title: true
  hide_menu: true
  image: /tutorials/llm-interactive.png
  meta_description: Label Studio tutorial for interactive LLM labeling with OpenAI,
    Azure, or Ollama
  meta_title: Interactive LLM labeling with OpenAI, Azure, or Ollama
  order: 5
  tier: all
  title: Interactive LLM labeling with GPT
  type: guide
  url: /tutorials/llm_interactive.html
- categories:
  - Computer Vision
  - Object Detection
  - Image Annotation
  - OpenMMLab
  - MMDetection
  hide_frontmatter_title: true
  hide_menu: true
  image: /tutorials/openmmlab.png
  meta_description: This is a tutorial on how to use the example MMDetection model
    backend with Label Studio for image segmentation tasks.
  meta_title: Object detection in images with Label Studio and MMDetection
  order: 65
  tier: all
  title: Object detection with bounding boxes using MMDetection
  type: guide
  url: /tutorials/mmdetection-3.html
- categories:
  - Audio/Speech Processing
  - Automatic Speech Recognition
  - NeMo
  - NVidia
  hide_frontmatter_title: true
  hide_menu: true
  image: /tutorials/nvidia.png
  meta_description: Tutorial on how to use set up Nvidia NeMo to use for ASR tasks
    in Label Studio
  meta_title: Automatic Speech Recognition with NeMo
  order: 60
  tier: all
  title: Automatic Speech Recognition with NVidia NeMo
  type: guide
  url: /tutorials/nemo_asr.html
- categories:
  - Computer Vision
  - Image Annotation
  - Object Detection
  - Segment Anything Model
  hide_frontmatter_title: true
  hide_menu: true
  image: /tutorials/sam2-images.png
  meta_title: Using SAM2 with Label Studio for Image Annotation
  order: 15
  tier: all
  title: SAM2 with Images
  type: guide
  url: /tutorials/segment_anything_2_image.html
- categories:
  - Computer Vision
  - Video Annotation
  - Object Detection
  - Segment Anything Model
  hide_frontmatter_title: true
  hide_menu: true
  image: /tutorials/sam2-video.png
  meta_title: Using SAM2 with Label Studio for Video Annotation
  order: 15
  tier: all
  title: SAM2 with Videos
  type: guide
  url: /tutorials/segment_anything_2_video.html
- categories:
  - Computer Vision
  - Object Detection
  - Image Annotation
  - Segment Anything Model
  - Facebook
  - ONNX
  hide_frontmatter_title: true
  hide_menu: true
  image: /tutorials/segment-anything.png
  meta_description: Label Studio tutorial for labeling images with MobileSAM or ONNX
    SAM.
  meta_title: Interactive annotation in Label Studio with Segment Anything Model (SAM)
  order: 10
  tier: all
  title: Interactive annotation with Segment Anything Model
  type: guide
  url: /tutorials/segment_anything_model.html
- categories:
  - Natural Language Processing
  - Text Classification
  - Scikit-learn
  hide_frontmatter_title: true
  hide_menu: true
  image: /tutorials/scikit-learn.png
  meta_description: Tutorial on how to use an example ML backend for Label Studio
    with Scikit-learn logistic regression
  meta_title: Sklearn Text Classifier model for Label Studio
  order: 50
  tier: all
  title: Sklearn Text Classifier model
  type: guide
  url: /tutorials/sklearn_text_classifier.html
- categories:
  - Natural Language Processing
  - Named Entity Recognition
  - SpaCy
  hide_frontmatter_title: true
  hide_menu: true
  image: /tutorials/spacy.png
  meta_description: Tutorial on how to use Label Studio and spaCy for faster NER and
    POS labeling
  meta_title: Use spaCy models with Label Studio
  order: 70
  tier: all
  title: spaCy models for NER
  type: guide
  url: /tutorials/spacy.html
- categories:
  - Computer Vision
  - Optical Character Recognition
  - Tesseract
  hide_frontmatter_title: true
  hide_menu: true
  image: /tutorials/tesseract.png
  meta_description: Tutorial for how to use Label Studio and Tesseract to assist with
    your OCR projects
  meta_title: Interactive bounding boxes OCR in Label Studio with a Tesseract backend
  order: 55
  tier: all
  title: Interactive bounding boxes OCR with Tesseract
  type: guide
  url: /tutorials/tesseract.html
- categories:
  - Generative AI
  - Large Language Model
  - WatsonX
  hide_frontmatter_title: true
  hide_menu: true
  image: /tutorials/watsonx.png
  meta_title: Integrate WatsonX with Label Studio
  order: 15
  tier: all
  title: Integrate WatsonX with Label Studio
  type: guide
  url: /tutorials/watsonx_llm.html
- categories:
  - Computer Vision
  - Object Detection
  - Image Segmentation
  - YOLO
  hide_frontmatter_title: true
  hide_menu: true
  image: /tutorials/yolo.png
  meta_description: Tutorial on how to use an example ML backend for Label Studio
    with YOLO
  meta_title: YOLO ML Backend for Label Studio
  order: 50
  tier: all
  title: YOLO ML Backend for Label Studio
  type: guide
  url: /tutorials/yolo.html
- categories:
  - Computer Vision
  - Video Classification
  - Temporal Labeling
  - LSTM
  hide_frontmatter_title: true
  hide_menu: true
  image: /tutorials/yolo-video-classification.png
  meta_description: Tutorial on how to use an example ML backend for Label Studio
    with TimelineLabels
  meta_title: TimelineLabels ML Backend for Label Studio
  order: 51
  tier: all
  title: TimelineLabels ML Backend for Label Studio
  type: guide
  url: /tutorials/yolo_timeline_labels.html
layout: templates
meta_description: Tutorial documentation for setting up a machine learning model with
  predictions using PyTorch, GPT2, Sci-kit learn, and other popular frameworks.
meta_title: Machine Learning Example Tutorials
order: 260
order_enterprise: 260
section: Machine Learning
tier: all
title: ML Examples and Tutorials
type: guide
---
